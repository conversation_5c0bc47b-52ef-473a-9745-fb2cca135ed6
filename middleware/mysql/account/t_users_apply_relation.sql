CREATE TABLE `t_users_apply_relation` (
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `op_user_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作人用户ID,指定账户才有权限为下属员工开通账户',
    `company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '企业名称',
    `username` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户名称',
    `email` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '邮箱',
    `mobile` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
    `initial_password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '账户密码',
    `status` tinyint(8) NOT NULL DEFAULT 0 COMMENT '状态【0账号生成中、1启用、2禁用】',
    `is_formal` tinyint(1) NOT NULL DEFAULT 0 COMMENT '用户属性【0测试用户、1正式用户】',
    `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uidx_username` (`username`),
    UNIQUE KEY `uidx_email` (`email`),
    UNIQUE KEY `uidx_mobile` (`mobile`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='FD01 Saas账户授权关联表';