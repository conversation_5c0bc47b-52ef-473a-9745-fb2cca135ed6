package redis

import (
	"context"
	"fmt"
	"github.com/go-redis/redismock/v8"
	"micro-service/pkg/cfg"
	"sync"

	goRedis "github.com/go-redis/redis/v8"
)

var once sync.Once

var singleInstance *goRedis.Client

var singleMockInstance redismock.ClientMock

func GetInstance(opt ...cfg.Redis) *goRedis.Client {
	if singleInstance == nil && len(opt) > 0 {
		once.Do(func() {
			singleInstance, singleMockInstance = initRedis(&opt[0])
		})
	}
	return singleInstance
}

func initRedis(conf *cfg.Redis) (*goRedis.Client, redismock.ClientMock) {
	// 如果是测试环境，则使用mock
	if IsTest() {
		return initRedisMock()
	}

	rc := goRedis.NewClient(&goRedis.Options{
		Addr:        fmt.Sprintf("%s:%d", conf.Address, conf.Port), // redis服务ip:port
		Password:    conf.Password,                                 // redis的认证密码
		DB:          conf.Database,                                 // 连接的database库
		IdleTimeout: 150,                                           // 默认Idle超时时间
		PoolSize:    3,                                             // 连接池
	})
	if _, err := rc.Ping(context.Background()).Result(); err != nil {
		panic(err)
	}
	return rc, nil
}
