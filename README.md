# micro-service

Foradar微服务拆分  版本:go1.19  框架: go-micro/v4






## 模块说明
```shell
apiService #微服务网关
clueService #线索服务 ??  存疑,是否放到core里面
cliService #cli命令工具
coreService #saas核心服务
scanService #扫描服务
webService #业务服务
cronService #定时任务
```



## protoc 生成文件夹
```shell
# 去到服务目录下载执行
protoc --proto_path=./proto --micro_out=. --go_out=. web.proto 
```

## 生成RPM包
```shell
# 添加配置
echo "%_topdir %(echo `dirname $PWD`/bin/rpmbuild)" >> ~/.rpmmacros
#Mac环境添加配置
echo "buildostranslate: Darwin: Linux" >> ~/.rpmrc
# 生成release文件
goreleaser --skip-validate --snapshot --rm-dist
```
# K8S一键部署生成
```shell
# 添加helm仓库地址
helm repo add foradar http://**************/chartrepo/foradar --username= --password=
# 生成Helm包
cd k8s && helm-docs && helm package chart
# 推送到镜像仓库
helm cm-push foradar-0.0.1.tgz foradar
```
# docker安装
```shell
#添加Docker新版源
sudo yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo
#安装Docker
yum install -y docker-ce docker-ce-cli containerd.io docker-compose-plugin
# 添加docker源
vim /etc/docker/daemon.json
# 加入以下配置项
{
    "registry-mirrors": [
        "https://docker.mirrors.ustc.edu.cn",
        "https://registry.docker-cn.com",
        "https://hub-mirror.c.163.com",
        "https://mirror.ccs.tencentyun.com",
        "http://f1361db2.m.daocloud.io",
        "http://**************"
    ],
    "exec-opts": [
        "native.cgroupdriver=systemd"
    ],
    "insecure-registries": [
        "http://**************"
    ],
    "ipv6": true,
    "fixed-cidr-v6": "2001:db8:1::/64"
}
#启动
systemctl start docker && systemctl enable docker
#安装docker-compose
curl -L https://get.daocloud.io/docker/compose/releases/download/v2.14.2/docker-compose-`uname -s`-`uname -m` > /usr/sbin/docker-compose
chmod +x /usr/sbin/docker-compose
#进行登录
docker login ************** --username client+foradar+robot --password 0fJMBIbK1plQmIj0DPy5CoGAHK6PMsFX
```

# docker compose 部署 && docker swarm && docker stack
```shell
#创建foradar网桥,用于docker-compose内部网络通信
#docker network create foradar_network 弃用
#生成集群主节点
docker swarm init --advertise-addr=ManagerIP
#这个时候需要注意观察日志。拿到worker node加入manager node的信息
docker swarm join --token tokenStr
# 生成集群证书
docker swarm ca --rotate
#集群管理工具
docker run -d -p 9443:9443 --name portainer --restart=always -v /var/run/docker.sock:/var/run/docker.sock portainer/portainer-ce:latest
#管理工具Agent
docker network create --driver overlay   portainer_agent_network
docker service create   --name portainer_agent   --network portainer_agent_network   -p 9001:9001/tcp   --mode global   --constraint 'node.platform.os == linux'   --mount type=bind,src=//var/run/docker.sock,dst=/var/run/docker.sock   --mount type=bind,src=//var/lib/docker/volumes,dst=/var/lib/docker/volumes   portainer/agent

docker run -d -p 9001:9001 --name portainer_agent --restart=always -v /var/run/docker.sock:/var/run/docker.sock -v /data/docker/volumes:/var/lib/docker/volumes portainer/agent:latest
#部署服务
docker stack deploy foradar --compose-file=docker-compose.yaml #部署服务
docker stack ps foradar  #查看服务
docker stack rm foradar #删除服务
```
## GoScanner 环境依赖
```
hydra
```
# 镜像中心地址
[http://**************](http://**************)


#develop分支cicd打包不成功crawler镜像解决方案  

1、因为goreleaser的配置项
build_flag_templates:
- "--pull"

      导致永远都会拉去最新的远端镜像，不会使用本地的镜像文件进行go build。chromedp/headless-shell 这个镜像可能会变更信息，导致这个cicd流程不能走通。需要关注这个镜像的变更信息。因为里面有sed的命令，会替换debian的镜像源地址，可能这个地址会变动导致cicd打包镜像失败。
测试方案就是，找个机器，去弄个dockerfile文件，然后再build上。再把这个build出来的镜像起成容器，再进去这个容器，去执行dockerfile里面真正的run命令

dockerfile示例： 创建dockerfile文件        vim dockerfile_crawler
```
FROM chromedp/headless-shell:latest
ENV LANG zh_CN.UTF-8
ENV LANGUAGE zh_CN.UTF-8
WORKDIR /data
VOLUME ['/etc/foradar','/data']
COPY ./foradar_cli_linux_amd64 /bin/foradar_cli
COPY ./foradar_crawler_linux_amd64 /bin/foradar_crawler
RUN sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list.d/debian.sources && apt-get update -y && apt install -y --no-install-recommends ca-certificates curl xfonts-intl-chinese ttf-wqy-microhei xfonts-wqy && apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*
ENTRYPOINT ["/bin/foradar_crawler"]
CMD []
```


根据dockerfile生成镜像
```
docker build -t aaaa  -f dockerfile_crawler .

```



根据dockerfile生成镜像,生成一个aaaa的镜像文件，这个aaaa的镜像就是这个dockerfile执行了上边命令生成的
```
docker build -t aaaa  -f dockerfile_crawler .





```






