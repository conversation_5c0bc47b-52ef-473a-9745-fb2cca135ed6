package log

import (
	"context"
	"fmt"
	"micro-service/pkg/cfg"
	"micro-service/pkg/traces"
	"os"

	logger2 "go-micro.dev/v4/logger"

	"github.com/gin-gonic/gin"
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var (
	// the local logger
	logger       *zap.SugaredLogger
	log          *zap.Logger
	encodeConfig zapcore.EncoderConfig
	Level        = zap.NewAtomicLevel()
)

func Init() {
	loggerConf := cfg.LoadLogger()
	// 设置日志级别
	setLogLevel(loggerConf.Level)
	// 注册Encoder
	_ = zap.RegisterEncoder(ColorConsole, func(config zapcore.EncoderConfig) (zapcore.Encoder, error) {
		return NewColorConsole(config), nil
	})
	encodeConfig = zapcore.EncoderConfig{
		TimeKey:        "time",
		LevelKey:       "level",
		<PERSON><PERSON><PERSON>:        "logger",
		Call<PERSON><PERSON><PERSON>:      "caller",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.CapitalLevelEncoder, // 这里可以指定颜色
		EncodeTime:     zapcore.TimeEncoderOfLayout("2006-01-02 15:04:05.000"),
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder, // 全路径编码器
	}
	var core zapcore.Core
	if loggerConf.OutPutConsole && loggerConf.OutPutFile {
		core = zapcore.NewTee(
			zapcore.NewCore(NewColorConsole(encodeConfig), zapcore.AddSync(os.Stdout), Level),
			zapcore.NewCore(zapcore.NewJSONEncoder(encodeConfig), zapcore.AddSync(&lumberjack.Logger{
				Filename:   loggerConf.FileName,
				MaxSize:    loggerConf.MaxSize,    // MB
				MaxBackups: loggerConf.MaxBackups, // 最多保留5个备份
				MaxAge:     loggerConf.MaxAge,     // days
				LocalTime:  loggerConf.LocalTime,
				Compress:   loggerConf.Compress,
			}), zap.NewAtomicLevelAt(zap.DebugLevel)),
		)
	} else if loggerConf.OutPutConsole {
		core = zapcore.NewTee(zapcore.NewCore(NewColorConsole(encodeConfig), zapcore.AddSync(os.Stdout), Level))
	} else {
		core = zapcore.NewTee(zapcore.NewCore(zapcore.NewJSONEncoder(encodeConfig), zapcore.AddSync(&lumberjack.Logger{
			Filename:   loggerConf.FileName,
			MaxSize:    loggerConf.MaxSize,    // MB
			MaxBackups: loggerConf.MaxBackups, // 最多保留5个备份
			MaxAge:     loggerConf.MaxAge,     // days
			LocalTime:  loggerConf.LocalTime,
			Compress:   loggerConf.Compress,
		}), zap.NewAtomicLevelAt(zap.DebugLevel)))
	}
	log = zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))
	logger = log.Sugar()

	// 输出当前日志级别用于调试
	fmt.Printf("Logger initialized with level: %s, current level: %v\n", loggerConf.Level, Level.Level())
}

func setLogLevel(level string) {
	var levelMap = map[string]zapcore.Level{
		"debug":  zapcore.DebugLevel,
		"info":   zapcore.InfoLevel,
		"warn":   zapcore.WarnLevel,
		"error":  zapcore.ErrorLevel,
		"dpanic": zapcore.DPanicLevel,
		"panic":  zapcore.PanicLevel,
		"fatal":  zapcore.FatalLevel,
	}
	if lv, ok := levelMap[level]; ok {
		Level.SetLevel(lv)
	} else {
		Level.SetLevel(zapcore.InfoLevel)
	}
}

func GetLogger() *zap.Logger {
	return log
}

func GetZapConfig() zap.Config {
	return zap.Config{
		Level:            zap.NewAtomicLevelAt(zap.DebugLevel),
		Development:      false,
		Encoding:         ColorConsole,
		EncoderConfig:    encodeConfig,
		OutputPaths:      []string{"stderr"},
		ErrorOutputPaths: []string{"stderr"},
	}
}

func getTraceString(ctx context.Context) string {
	traceId, _ := traces.GetTraceId(ctx)
	return fmt.Sprintf(" TraceId: %s", traceId)
}
func getTraceMap(ctx context.Context) *map[string]interface{} {
	traceId, _ := traces.GetTraceId(ctx)
	return &map[string]interface{}{"TraceId": traceId}
}
func Debug(args ...interface{}) {
	logger.Debug(args...)
}
func Debugf(template string, args ...interface{}) {
	logger.Debugf(template, args...)
}
func WithContextInfo(ctx context.Context, args ...interface{}) {
	args = append(args, getTraceMap(ctx))
	logger.Info(args...)
}
func Info(args ...interface{}) {
	logger.Info(args...)
}
func WithContextInfof(ctx context.Context, template string, args ...interface{}) {
	logger.Infof(getTraceString(ctx)+" "+template, args...)
}

func Infof(template string, args ...interface{}) {
	logger.Infof(template, args...)
}
func WithContextWarn(ctx context.Context, args ...interface{}) {
	args = append(args, getTraceMap(ctx))
	logger.Warn(args...)
}
func Warn(args ...interface{}) {
	logger.Warn(args...)
}
func WithContextWarnf(ctx context.Context, template string, args ...interface{}) {
	logger.Warnf(getTraceString(ctx)+" "+template, args...)
}
func Warnf(template string, args ...interface{}) {
	logger.Warnf(template, args...)
}
func WithContextError(ctx context.Context, args ...interface{}) {
	args = append(args, getTraceMap(ctx))
	logger.Error(args...)
}
func Error(args ...interface{}) {
	logger.Error(args...)
}
func WithContextErrorf(ctx context.Context, template string, args ...interface{}) {
	logger.Errorf(getTraceString(ctx)+" "+template, args...)
}
func Errorf(template string, args ...interface{}) {
	logger.Errorf(template, args...)
}
func WithContextDPanic(ctx context.Context, args ...interface{}) {
	args = append(args, getTraceMap(ctx))
	logger.DPanic(args...)
}
func DPanic(args ...interface{}) {
	logger.DPanic(args...)
}
func WithContextDPanicf(ctx context.Context, template string, args ...interface{}) {
	logger.DPanicf(getTraceString(ctx)+" "+template, args...)
}
func DPanicf(template string, args ...interface{}) {
	logger.DPanicf(template, args...)
}
func WithContextPanic(ctx context.Context, args ...interface{}) {
	args = append(args, getTraceMap(ctx))
	logger.Panic(args...)
}
func Panic(args ...interface{}) {
	logger.Panic(args...)
}
func WithContextPanicf(ctx context.Context, template string, args ...interface{}) {
	logger.Panicf(getTraceString(ctx)+" "+template, args...)
}
func Panicf(template string, args ...interface{}) {
	logger.Panicf(template, args...)
}
func WithContextFatal(ctx context.Context, args ...interface{}) {
	args = append(args, getTraceMap(ctx))
	logger.Fatal(args...)
}
func Fatal(args ...interface{}) {
	logger.Fatal(args...)
}
func WithContextFatalf(ctx context.Context, template string, args ...interface{}) {
	logger.Fatalf(getTraceString(ctx)+" "+template, args...)
}
func Fatalf(template string, args ...interface{}) {
	logger.Fatalf(template, args...)
}
func WithContextErrorfIf(ctx context.Context, err error, format string, args ...any) {
	if err != nil {
		WithContextErrorf(ctx, format, args...)
	}
}

func ErrorfIf(err error, format string, args ...any) {
	if err != nil {
		Errorf(format, args...)
	}
}

func WithContextApiErrorf(ctx *gin.Context, err error) {
	WithContextErrorf(ctx, "Request api %s: %+v", ctx.Request.URL.Path, err)
}

func Logf(level logger2.Level, format string, v ...interface{}) {
	switch level {
	case logger2.TraceLevel:
		logger.Debugf(format, v)
	case logger2.DebugLevel:
		logger.Debugf(format, v)
	case logger2.InfoLevel:
		logger.Infof(format, v)
	case logger2.WarnLevel:
		logger.Warnf(format, v)
	case logger2.ErrorLevel:
		logger.Errorf(format, v)
	case logger2.FatalLevel:
		logger.Fatalf(format, v)
	}
	logger.Debugf(format, v)
}
