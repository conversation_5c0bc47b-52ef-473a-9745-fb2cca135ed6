package wsocket

import (
	"fmt"
	"micro-service/apiService/response"
	"micro-service/apiService/safe_company"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
	"net/http"
	"reflect"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/tidwall/gjson"
)

var wsocket *WSocket

const CmdError = "error"

// Client 客户端
type Client struct {
	SfId   int64 // 安服ID
	UserId int64 // 用户ID
	Conn   *websocket.Conn
	ConnId string
}

// WriteJSON 安全地写入JSON数据到WebSocket连接
func (c *Client) WriteJSON(data interface{}) (err error) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[WebSocket]: WriteJSON panic recovered: %v, Client: %v", r, c)
			err = fmt.<PERSON><PERSON><PERSON>("websocket write panic: %v", r)
		}
	}()

	if c.Conn == nil {
		return fmt.Errorf("websocket connection is nil")
	}

	return c.Conn.WriteJSON(data)
}
func (c *Client) WriteMessage(message string) (err error) {
	return c.Conn.WriteMessage(websocket.TextMessage, []byte(message))
}

type WsRcvPack struct {
	Cmd  string `json:"cmd"`
	Args string `json:"data"`
}

type WsSendPack struct {
	ToClient *Client     `json:"-"`
	Cmd      string      `json:"cmd"`
	Status   int64       `json:"status"`
	Message  string      `json:"message"`
	Data     interface{} `json:"data"`
}
type TextMsg struct {
	Text   string `json:"text"`
	Client *Client
}
type WSocket struct {
	clients     []*Client // 客户端队列,指针同步同一个client data
	lockClients sync.RWMutex
	broadcast   chan *WsSendPack // broadcast channel
	upgrader    websocket.Upgrader
	msgChan     chan *TextMsg
}

var once = &sync.Once{}

func init() {
	if wsocket == nil {
		once.Do(func() {
			wsocket = &WSocket{
				broadcast: make(chan *WsSendPack),
				msgChan:   make(chan *TextMsg, 1000),
				upgrader: websocket.Upgrader{
					CheckOrigin: func(r *http.Request) bool {
						return true
					},
				},
			}
			// 异步消息推送
			go wsocket.HandleMessage()
			go wsocket.SendMsg()
		})
	}
}

func SocketHandler(ctx *gin.Context) (err error) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[WebSocket]: SocketHandler panic recovered: %v", r)
			err = fmt.Errorf("websocket handler panic: %v", r)
		}
	}()

	userId, sfId, _, err := safe_company.GetSafeCompanyUser(ctx)
	if err != nil {
		return response.Gen(ctx).SendByError("api service", err)
	}

	// 处理 Sec-WebSocket-Protocol Header
	wsocket.upgrader.Subprotocols = []string{ctx.GetHeader("Sec-WebSocket-Protocol")}
	// 升级websocket协议
	ws, err := wsocket.upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		return err
	}
	// websocket请求处理
	go wsocket.WsHandler(ws, int64(userId), int64(sfId)) // 改为异步处理，避免阻塞
	return nil
}

func GetWSocket() *WSocket {
	return wsocket
}

// DelClient 删除离线客户端
func (w *WSocket) DelClient(client *Client) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[WebSocket]: DelClient panic recovered: %v, Client: %v", r, client)
		}
	}()

	w.lockClients.Lock()
	defer w.lockClients.Unlock()
	for k, cli := range w.clients {
		if cli != nil && client != nil && cli.ConnId == client.ConnId {
			log.Infof("[WebSocket]: 清除离线用户: Client:%v", client)
			w.clients = append(w.clients[:k], w.clients[k+1:]...)
			if client.Conn != nil {
				client.Conn.Close()
			}
			break // 找到后立即退出，避免数组越界
		}
	}
}

// WsHandler webSocket处理
func (w *WSocket) WsHandler(ws *websocket.Conn, userId, sfId int64) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[WebSocket]: WsHandler panic recovered: %v, UserId: %d, SfId: %d", r, userId, sfId)
		}
		if ws != nil {
			_ = ws.Close()
		}
	}()

	client := Client{UserId: userId, SfId: sfId, Conn: ws, ConnId: uuid.New().String()}
	w.lockClients.Lock()
	w.clients = append(w.clients, &client)
	w.lockClients.Unlock()
	log.Infof("[WebSocket]: 创建用户连接: Client:%v,token:%s", client, w.upgrader.Subprotocols[0])
	// 消息读取,每个客户端数据
	for {
		_, data, err := ws.ReadMessage()
		if err != nil {
			// 清除离线客户端
			w.DelClient(&client)
			return
		}
		log.Infof("[WebSocket]: 接收用户数据: Client:%v,Data:%v", client, string(data))
		// 检查CMD
		cmd := gjson.Get(string(data), "cmd").String()
		if cmd == "" {
			w.SendMessage(&WsSendPack{ToClient: &client, Status: 400, Message: "cmd协议为空!"})
			continue
		}
		// 心跳
		if cmd == "ping" {
			w.SendMessage(&WsSendPack{ToClient: &client, Cmd: "pong", Message: "Success"})
			continue
		}
		// 异步 调用RPC
		go w.CallFunc(&client, cmd, gjson.Get(string(data), "args").String())
	}
}

func (w *WSocket) CallFunc(client *Client, cmd, args string) {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[WebSocket]: CallFunc panic recovered: %v, Cmd: %s, Client: %v", r, cmd, client)
			w.SendMessage(&WsSendPack{ToClient: client, Status: 500, Message: fmt.Sprintf("协议 %s 处理异常!", cmd)})
		}
	}()

	if method, ok := reflect.TypeOf(unpack).MethodByName(w.CamelString(cmd)); !ok {
		w.SendMessage(&WsSendPack{ToClient: client, Status: 404, Message: fmt.Sprintf("协议 %s 不存在!", cmd)})
	} else {
		method.Func.Call([]reflect.Value{reflect.ValueOf(unpack), reflect.ValueOf(client), reflect.ValueOf(args)})
	}
}

// SendMessage 推送WebSocket消息
func (w *WSocket) SendMessage(sendPack *WsSendPack) {
	if sendPack.Status != 200 && sendPack.Cmd == "" {
		sendPack.Cmd = CmdError
	}
	if sendPack.Cmd == CmdError && sendPack.Status == 0 {
		sendPack.Status = 400
	}
	if sendPack.Cmd != CmdError && sendPack.Status == 0 {
		sendPack.Status = 200
	}
	w.broadcast <- sendPack
}

// HandleMessage WebSocket消息推送
func (w *WSocket) HandleMessage() {
	defer func() {
		if r := recover(); r != nil {
			log.Errorf("[WebSocket]: HandleMessage panic recovered: %v", r)
			// 重新启动消息处理
			go w.HandleMessage()
		}
	}()

	for {
		msg := <-w.broadcast // 广播
		log.Infof("[WebSocket]: 收到广播消息: Message:%v", msg)
		//  检查ToClient Is Empty
		if msg.ToClient == nil {
			log.Warn("[WebSocket]: 推送消息失败: ToClient Empty")
			continue
		}
		w.lockClients.RLock()
		clients := w.clients
		w.lockClients.RUnlock()
		for _, client := range clients {
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Errorf("[WebSocket]: 推送消息时panic recovered: %v, Client: %v, Msg: %v", r, client, msg)
					}
				}()

				//
				if client.UserId != msg.ToClient.UserId &&
					(msg.ToClient.SfId != 0 && client.SfId != msg.ToClient.SfId) {
					return
				}
				w.msgChan <- &TextMsg{
					Text:   utils.AnyToStr(msg),
					Client: client,
				}
			}()
		}
	}
}
func (w *WSocket) SendMsg() {
	for msg := range w.msgChan {
		err := msg.Client.WriteMessage(msg.Text)
		if err != nil {
			log.Warnf("[WebSocket]: 推送消息失败: Client:%v,Info:%v,Error%v", msg.Client, msg.Text, err)
			w.DelClient(msg.Client)
		}
	}
}

// CamelString 蛇形转驼峰
func (w *WSocket) CamelString(s string) string {
	data := make([]byte, 0, len(s))
	j := false
	k := false
	num := len(s) - 1
	for i := 0; i <= num; i++ {
		d := s[i]
		if !k && d >= 'A' && d <= 'Z' {
			k = true
		}
		if d >= 'a' && d <= 'z' && (j || !k) {
			d -= 32
			j = false
			k = true
		}
		if k && d == '_' && num > i && s[i+1] >= 'a' && s[i+1] <= 'z' {
			j = true
			continue
		}
		data = append(data, d)
	}
	return string(data)
}
