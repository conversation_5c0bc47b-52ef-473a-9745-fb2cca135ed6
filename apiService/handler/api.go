package handler

import (
	"context"
	"encoding/json"
	proto "micro-service/apiService/proto"
	"micro-service/apiService/router"
	"micro-service/apiService/wsocket"
	"micro-service/pkg/log"
)

type Api struct{}

// SendWsMsg SendMessage Users 推送WebSocket信息
func (a *Api) SendWsMsg(ctx context.Context, req *proto.MsgRequest, rsp *proto.Response) error {
	log.WithContextInfof(ctx, "[WebSocket]: RPC -> SendMessage data:%v", req)
	var data interface{}
	if err := json.Unmarshal([]byte(req.Data), &data); err != nil {
		data = req.Data
	}
	go wsocket.GetWSocket().SendMessage(&wsocket.WsSendPack{
		ToClient: &wsocket.Client{SfId: req.ClientUser.SfId, UserId: req.ClientUser.UserId, ConnId: req.ClientUser.ConnId},
		Cmd:      req.Cmd,
		Message:  req.Message,
		Data:     data,
	})
	return nil
}

func (a *Api) GetRoutes(_ context.Context, _ *proto.Empty, rsp *proto.RouteResponse) error {
	routes := router.GetRouteInstance().Routes()
	for _, route := range routes {
		rsp.List = append(rsp.List, &proto.Route{Method: route.Method, Path: route.Path, Handler: route.Handler})
	}
	return nil
}
