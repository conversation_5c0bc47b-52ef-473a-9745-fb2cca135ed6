package response

import (
	"encoding/json"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/tidwall/gjson"
)

const (
	MsgSuccess = "Success"
	MsgFailed  = "调用接口失败"
)

const statusSuccess = 0 // 业务码：成功

type Rsp struct {
	ctx *gin.Context
	Basic
}

type Basic struct {
	Server  string `json:"server"`
	Code    int    `json:"code"`
	Message string `json:"message"`
	Data    any    `json:"data"`
}

func Gen(ctx *gin.Context) *Rsp {
	rsp := &Rsp{
		ctx: ctx,
		Basic: Basic{
			Code:    200,
			Message: "Success",
			Data:    nil,
			Server:  "",
		},
	}
	return rsp
}

func (rsp *Rsp) Send() error {
	rsp.ctx.JSON(http.StatusOK, gin.H{
		"server":  rsp.Server,
		"code":    rsp.Code,
		"message": rsp.Message,
		"data":    rsp.Data,
	})
	return nil
}

func (rsp *Rsp) SendSuccess(serviceName, message string, data interface{}) error {
	rsp.ctx.JSON(http.StatusOK, gin.H{
		"server":  serviceName,
		"code":    statusSuccess,
		"message": message,
		"data":    data,
	})
	return nil
}

func (rsp *Rsp) SuccessByData(name string, data any) error {
	rsp.ctx.JSON(http.StatusOK, Basic{
		Server:  name,
		Code:    statusSuccess,
		Message: MsgSuccess,
		Data:    data,
	})
	return nil
}

func (rsp *Rsp) Send400Error(serviceName, message string, data interface{}) error {
	rsp.ctx.JSON(http.StatusOK, gin.H{
		"server":  serviceName,
		"code":    400,
		"message": message,
		"data":    data,
	})
	return nil
}

func (rsp *Rsp) SendError(serviceName string, code int, message string, data interface{}) error {
	rsp.ctx.JSON(http.StatusOK, gin.H{
		"server":  serviceName,
		"code":    code,
		"message": message,
		"data":    data,
	})
	return nil
}

func (rsp *Rsp) SendByError(serviceName string, err error) error {
	if json.Valid([]byte(err.Error())) {
		code := gjson.Get(err.Error(), "code").Int()
		if code == 0 {
			code = 400
		}
		rsp.ctx.JSON(http.StatusOK, gin.H{
			"server":  serviceName,
			"code":    code,
			"message": gjson.Get(err.Error(), "detail").String(),
			"data":    nil,
		})
	} else {
		rsp.ctx.JSON(http.StatusOK, gin.H{
			"server":  serviceName,
			"code":    400,
			"message": err.Error(),
			"data":    nil,
		})
	}
	return nil
}

func (rsp *Rsp) SendByErrorMsg(serviceName, errMsg string) error {
	return rsp.SendError(serviceName, http.StatusBadRequest, errMsg, nil)
}

func (rsp *Rsp) Send403Error(serviceName string) error {
	rsp.ctx.JSON(http.StatusForbidden, gin.H{
		"server":  serviceName,
		"code":    403,
		"message": "无权限",
		"data":    nil,
	})
	return nil
}
