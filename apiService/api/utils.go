package api

import (
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"reflect"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
)

// ParseQuery 解析 URL 查询参数到结构体
// dst: 目标结构体指针
// c: Gin 上下文
// 支持的特性:
// 1. 区分未设置字段和零值字段
// 2. 支持数组参数 (key[0]=a&key[1]=b 或 key=a&key=b)
// 3. 支持嵌套结构体
// 4. 支持通过form标签指定参数名
func ParseQuery(dst interface{}, c *gin.Context) error {
	values := c.Request.URL.Query()
	return parseQueryToStruct(dst, values)
}

func parseQueryToStruct(dst interface{}, values url.Values) error {
	v := reflect.ValueOf(dst)
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return fmt.Errorf("目标必须是非空指针")
	}
	v = v.Elem()
	if v.Kind() != reflect.Struct {
		return fmt.Errorf("目标必须指向结构体")
	}
	t := v.Type()
	// 遍历结构体字段
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fieldValue := v.Field(i)
		tag := strings.Split(field.Tag.Get("form"), ",")[0] // 获取第一个 form 标签
		if tag == "" {
			tag = strings.ToLower(field.Name)
		}
		// 检查字段是否可设置
		if !fieldValue.CanSet() {
			continue
		}
		// 处理数组/slice
		if fieldValue.Kind() == reflect.Slice {
			if err := handleSliceField(fieldValue, values, tag); err != nil {
				return err
			}
			continue
		}
		// 处理指针类型 (用于区分零值和未设置)
		if fieldValue.Kind() == reflect.Ptr {
			if err := handlePointerField(fieldValue, values, tag); err != nil {
				return err
			}
			continue
		}
		// 处理普通字段
		if err := handleBasicField(fieldValue, values, tag); err != nil {
			return err
		}
	}

	return nil
}

func handleSliceField(fieldValue reflect.Value, values url.Values, tag string) error {
	// 检查是否存在数组格式的参数 (key[0], key[1]...)
	hasArrayFormat := false
	maxIndex := -1
	tagPrefix := tag + "["
	for k := range values {
		if strings.HasPrefix(k, tagPrefix) && strings.HasSuffix(k, "]") {
			indexStr := k[len(tagPrefix) : len(k)-1] // 优化字符串操作
			if index, err := strconv.Atoi(indexStr); err == nil {
				hasArrayFormat = true
				if index > maxIndex {
					maxIndex = index
				}
			}
		}
	}
	// 检查是否存在重复key格式的参数 (key=a&key=b)
	hasRepeatedKey := len(values[tag]) > 1
	// 如果没有任何形式的数组参数存在，返回真正的空切片
	if !hasArrayFormat && !hasRepeatedKey {
		fieldValue.Set(reflect.Zero(fieldValue.Type()))
		return nil
	}
	var sliceValues []string
	// 优先处理数组索引格式
	if hasArrayFormat {
		sliceValues = make([]string, maxIndex+1)
		for i := 0; i <= maxIndex; i++ {
			key := tagPrefix + strconv.Itoa(i) + "]" // 优化字符串拼接
			if vals, ok := values[key]; ok && len(vals) > 0 {
				sliceValues[i] = vals[0]
			}
		}
	} else {
		// 处理重复key格式
		sliceValues = values[tag]
	}

	// 预先获取类型信息，避免重复反射调用
	sliceType := fieldValue.Type()
	elemType := sliceType.Elem()
	elemKind := elemType.Kind()

	// 创建适当类型的切片
	newSlice := reflect.MakeSlice(sliceType, len(sliceValues), len(sliceValues))

	// 预先创建零值，避免重复计算
	zeroValue := reflect.Zero(elemType)

	// 填充切片
	for i, val := range sliceValues {
		if val == "" {
			newSlice.Index(i).Set(zeroValue)
			continue
		}
		elemValue := newSlice.Index(i)
		if err := setValue(elemValue, val, elemKind); err != nil {
			return fmt.Errorf("无法设置切片元素 %d: %v", i, err)
		}
	}
	fieldValue.Set(newSlice)
	return nil
}

func handlePointerField(fieldValue reflect.Value, values url.Values, tag string) error {
	// 检查参数是否存在
	if vals, ok := values[tag]; ok {
		if len(vals) == 0 || vals[0] == "" {
			// 空字符串，设置为零值指针
			fieldValue.Set(reflect.Zero(fieldValue.Type()))
			return nil
		}
		// 优化：直接创建指针并设置值，减少反射操作
		elemType := fieldValue.Type().Elem()
		ptr := reflect.New(elemType)
		elemValue := ptr.Elem()

		if err := setValue(elemValue, vals[0], elemType.Kind()); err != nil {
			return err
		}
		fieldValue.Set(ptr)
	}
	// 如果参数不存在，保持指针为 nil
	return nil
}

func handleBasicField(fieldValue reflect.Value, values url.Values, tag string) error {
	if vals, ok := values[tag]; ok && len(vals) > 0 {
		if vals[0] == "" {
			// 空字符串，设置为零值
			fieldValue.Set(reflect.Zero(fieldValue.Type()))
			return nil
		}
		return setValue(fieldValue, vals[0], fieldValue.Kind())
	}
	return nil
}

// setValue 优化版本的setValue，接受预计算的Kind参数
func setValue(fieldValue reflect.Value, value string, kind reflect.Kind) error {
	switch kind {
	case reflect.String:
		fieldValue.SetString(value)
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		intVal, err := strconv.ParseInt(value, 10, 64)
		if err != nil {
			return err
		}
		fieldValue.SetInt(intVal)
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		uintVal, err := strconv.ParseUint(value, 10, 64)
		if err != nil {
			return err
		}
		fieldValue.SetUint(uintVal)
	case reflect.Float32, reflect.Float64:
		floatVal, err := strconv.ParseFloat(value, 64)
		if err != nil {
			return err
		}
		fieldValue.SetFloat(floatVal)
	case reflect.Bool:
		boolVal, err := strconv.ParseBool(value)
		if err != nil {
			return err
		}
		fieldValue.SetBool(boolVal)
	default:
		return fmt.Errorf("不支持的字段类型: %v", kind)
	}
	return nil
}

// ParseJSON 解析 JSON 查询参数到结构体
// 用于处理前端返回不存在的字段为空字符串("")的情况
// dst: 目标结构体指针
// c: Gin 上下文
func ParseJSON(c *gin.Context, dst interface{}) error {
	values, err := c.GetRawData()
	if err != nil {
		return fmt.Errorf("获取请求体失败: %w", err)
	}
	return flexibleUnmarshal(values, dst)
}

// tryStringToNumberConversion 尝试将字符串转换为数字类型
func tryStringToNumberConversion(rawValue json.RawMessage, fieldValue reflect.Value) bool {
	var strVal string
	if err := json.Unmarshal(rawValue, &strVal); err != nil {
		return false // 不是字符串，无法转换
	}

	strVal = strings.TrimSpace(strVal)

	if strVal == "" {
		return false // 空字符串不转换
	}

	fieldType := fieldValue.Type()
	switch fieldType.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		if intVal, err := strconv.ParseInt(strVal, 10, 64); err == nil {
			fieldValue.SetInt(intVal)
			return true
		}
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		if uintVal, err := strconv.ParseUint(strVal, 10, 64); err == nil {
			fieldValue.SetUint(uintVal)
			return true
		}
	case reflect.Float32, reflect.Float64:
		if floatVal, err := strconv.ParseFloat(strVal, 64); err == nil {
			fieldValue.SetFloat(floatVal)
			return true
		}
	case reflect.Bool:
		if boolVal, err := strconv.ParseBool(strVal); err == nil {
			fieldValue.SetBool(boolVal)
			return true
		}
	}
	return false
}

func flexibleUnmarshal(data []byte, v interface{}) error {
	rawMap := make(map[string]json.RawMessage)
	if err := json.Unmarshal(data, &rawMap); err != nil {
		return err
	}
	rv := reflect.ValueOf(v)
	for rv.Kind() == reflect.Ptr && !rv.IsNil() && rv.Elem().Kind() == reflect.Ptr {
		rv = rv.Elem()
	}
	if rv.Kind() != reflect.Ptr || rv.Elem().Kind() != reflect.Struct {
		return errors.New("v must be a pointer to a struct")
	}
	rv = rv.Elem()
	rt := rv.Type()

	// 预先计算字段信息，减少重复计算
	numFields := rt.NumField()
	for i := 0; i < numFields; i++ {
		field := rt.Field(i)
		tag := field.Tag.Get("json")
		if tag == "" {
			tag = field.Name
		} else if commaIdx := strings.IndexByte(tag, ','); commaIdx != -1 {
			tag = tag[:commaIdx]
		}

		// 跳过标记为忽略的字段
		if tag == "-" {
			continue
		}

		rawValue, exists := rawMap[tag]
		if !exists {
			continue
		}
		fieldValue := rv.Field(i)
		if !fieldValue.CanSet() {
			continue
		}

		// 检查是否为空字符串
		var isEmptyString bool
		var strVal string
		if err := json.Unmarshal(rawValue, &strVal); err == nil && strVal == "" {
			isEmptyString = true
		}

		fieldType := field.Type
		fieldKind := fieldType.Kind()

		switch fieldKind {
		case reflect.Ptr:
			if err := handlePointerFieldJSON(fieldValue, rawValue, fieldType, isEmptyString); err != nil {
				return err
			}
		case reflect.Slice:
			if isEmptyString {
				fieldValue.Set(reflect.Zero(fieldType))
			} else {
				if err := flexibleUnmarshalSlice(rawValue, fieldValue); err != nil {
					fieldValue.Set(reflect.Zero(fieldType))
				}
			}
		default:
			// 首先尝试字符串到数字的转换
			if !tryStringToNumberConversion(rawValue, fieldValue) {
				// 如果转换失败，使用标准的JSON反序列化
				_ = json.Unmarshal(rawValue, fieldValue.Addr().Interface())
			}
		}
	}
	return nil
}

// handlePointerFieldJSON 优化的指针字段处理函数
func handlePointerFieldJSON(fieldValue reflect.Value, rawValue json.RawMessage, fieldType reflect.Type, isEmptyString bool) error {
	// 如果是基本类型且为空字符串，设置为nil
	if isEmptyString {
		fieldValue.Set(reflect.Zero(fieldType))
		return nil
	}

	elemType := fieldType.Elem()

	// 创建指针指向的新值
	ptr := reflect.New(elemType)
	elemValue := ptr.Elem()

	// 尝试字符串到数字的转换（针对指针指向的类型）
	if tryStringToNumberConversion(rawValue, elemValue) {
		fieldValue.Set(ptr)
		return nil
	}

	// 对于复杂类型，递归调用flexibleUnmarshal
	if elemType.Kind() == reflect.Struct {
		if err := flexibleUnmarshal(rawValue, ptr.Interface()); err != nil {
			fieldValue.Set(reflect.Zero(fieldType))
		} else {
			fieldValue.Set(ptr)
		}
	} else {
		// 对于基本类型，使用标准JSON反序列化
		if err := json.Unmarshal(rawValue, ptr.Interface()); err != nil {
			fieldValue.Set(reflect.Zero(fieldType))
		} else {
			fieldValue.Set(ptr)
		}
	}
	return nil
}

// flexibleUnmarshalSlice 递归处理切片类型，对每个元素应用灵活的反序列化逻辑
func flexibleUnmarshalSlice(rawValue json.RawMessage, fieldValue reflect.Value) error {
	// 首先尝试解析为 JSON 数组
	var rawArray []json.RawMessage
	if err := json.Unmarshal(rawValue, &rawArray); err != nil {
		return err
	}

	sliceType := fieldValue.Type()
	elemType := sliceType.Elem()
	elemKind := elemType.Kind()

	// 创建新的切片
	newSlice := reflect.MakeSlice(sliceType, len(rawArray), len(rawArray))

	// 预先计算零值，避免重复计算
	zeroValue := reflect.Zero(elemType)

	// 处理每个元素
	for i, rawElem := range rawArray {
		elemValue := newSlice.Index(i)

		// 检查元素是否为空字符串
		var isEmptyString bool
		var strVal string
		if err := json.Unmarshal(rawElem, &strVal); err == nil && strVal == "" {
			isEmptyString = true
		}

		if isEmptyString {
			elemValue.Set(zeroValue)
			continue
		}

		switch elemKind {
		case reflect.Ptr:
			ptrElemType := elemType.Elem()
			ptr := reflect.New(ptrElemType)
			ptrElemValue := ptr.Elem()

			if tryStringToNumberConversion(rawElem, ptrElemValue) {
				elemValue.Set(ptr)
			} else if ptrElemType.Kind() == reflect.Struct {
				if err := flexibleUnmarshal(rawElem, ptr.Interface()); err != nil {
					elemValue.Set(zeroValue)
				} else {
					elemValue.Set(ptr)
				}
			} else {
				if err := json.Unmarshal(rawElem, ptr.Interface()); err != nil {
					elemValue.Set(zeroValue)
				} else {
					elemValue.Set(ptr)
				}
			}
		case reflect.Struct:
			tempPtr := reflect.New(elemType)
			if err := flexibleUnmarshal(rawElem, tempPtr.Interface()); err != nil {
				elemValue.Set(zeroValue)
			} else {
				elemValue.Set(tempPtr.Elem())
			}
		case reflect.Slice:
			// 处理嵌套切片 - 递归调用
			if err := flexibleUnmarshalSlice(rawElem, elemValue); err != nil {
				elemValue.Set(zeroValue)
			}
		default:
			// 处理基本类型元素
			if !tryStringToNumberConversion(rawElem, elemValue) {
				// 如果转换失败，使用标准的JSON反序列化
				_ = json.Unmarshal(rawElem, elemValue.Addr().Interface())
			}
		}
	}

	fieldValue.Set(newSlice)
	return nil
}
