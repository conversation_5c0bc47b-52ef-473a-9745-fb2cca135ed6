package wrapper

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"micro-service/middleware/mysql/request_api_record"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	oauth "github.com/go-oauth2/oauth2/v4"

	"micro-service/middleware/mysql/auth_access_client"
	"micro-service/middleware/redis/client_counter"
	"micro-service/pkg/log"
	"micro-service/pkg/utils"
)

// clientAuthCheck 客户端校验
func clientAuthCheck(ctx *gin.Context, isCounter bool, tokenInfo oauth.TokenInfo) (bool, error) {
	clientId := tokenInfo.GetClientID()
	info, cErr := auth_access_client.NewAuthAccessClientModel().GetClientInfoByID(tokenInfo.GetClientID())
	if cErr != nil {
		return false, fmt.Errorf("获取客户端信息失败:%s", cErr.Error())
	}
	// 本地化请求服务端api请求计数
	if isCounter {
		clientCounter(ctx, clientId, ctx.FullPath())
	}
	//记录请求记录
	if isCounter {
		// 读取请求体
		bodyBytes, _ := io.ReadAll(ctx.Request.Body)
		// 把刚刚读出来的再写回去
		ctx.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		// 转换为字符串
		bodyString := string(bodyBytes)
		// 如果 bodyString 为空，获取所有 URL 路径参数
		if bodyString == "" {
			params := ctx.Params
			for _, param := range params {
				bodyString += param.Key + ": " + param.Value + ", "
			}
		}
		recordApiHistory(ctx, clientId, ctx.FullPath(), ctx.Request.Method, bodyString)
		ctx.Next()
	}
	// is expired
	if info.ExpiredAt.Before(time.Now()) {
		return false, errors.New("客户端账户已过期")
	}
	// is validate ip
	reqIP := ctx.ClientIP()
	if !isValidateIP(info.IpWhitelist, reqIP) {
		return false, fmt.Errorf("客户端主机请求接口的主机ip不在授权白名单里面，非法的请求ip: %s", reqIP)
	}
	return true, nil
}

// isValidateIP 校验IP白名单
func isValidateIP(whitelist, ip string) bool {
	if whitelist == "" {
		return true
	}
	split := strings.Split(whitelist, ",")
	if utils.ListContains(split, "*") {
		return true
	}
	for i := range split {
		if split[i] == "" {
			continue
		}
		if split[i] == ip {
			return true
		}

		if utils.IPContains(split[i], ip) {
			return true
		}
	}
	return false
}

// clientCounter 客户端调用统计-foradar本地化或者fd01本地化调用saas的接口api统计
func clientCounter(ctx context.Context, clientId, reqPath string) {
	key := client_counter.GenKey(clientId)
	err := client_counter.NewClientCounterModel().Set(ctx, key, reqPath)
	if err != nil {
		log.Errorf("[client counter]Client_id: %s request path: %s, %v", clientId, reqPath, err)
	}
}

// 记录请求api的记录，写到数据
func recordApiHistory(ctx context.Context, clientId, reqPath string, method string, params string) {
	// 记录API请求历史
	err := request_api_record.NewRequestApiRecordModel().Create(&request_api_record.ApiRecord{
		ClientId: clientId,
		ApiPath:  reqPath,
		Param:    params,
		Method:   method,
	})
	if err != nil {
		log.Errorf("[recordApiHistory]Client_id: %s request path: %s, %v", clientId, reqPath, err)
	}

	// API配额扣费（异步处理，不影响API响应速度）
	go func() {
		quotaManager := NewApiQuotaManager()

		// 从配置表获取扣费倍率
		cost := quotaManager.GetApiCostMultiplier(ctx, reqPath, method)

		err := quotaManager.DeductApiQuota(ctx, clientId, cost)
		if err != nil {
			log.Errorf("[ApiQuota] 扣费失败 clientId=%s, path=%s, cost=%d, error=%v", clientId, reqPath, cost, err)
		} else {
			log.Debugf("[ApiQuota] 扣费成功 clientId=%s, path=%s, cost=%d", clientId, reqPath, cost)
		}
	}()
}


