package wrapper

import (
	"bytes"
	errstd "errors"
	"github.com/go-oauth2/oauth2/v4/errors"
	"github.com/spf13/cast"
	"io"
	"micro-service/apiService/auth"
	"micro-service/middleware/mysql/user"
	"net/http"
	"strconv"
	"strings"

	"github.com/bytedance/sonic"
	"github.com/gin-gonic/gin"
	oauth "github.com/go-oauth2/oauth2/v4"
	"micro-service/apiService/response"
)

const (
	APIServiceName       = "foradar.api.gateway"
	ginUserIdWithToken   = "user_id_with_token"
	ginClientIdWithToken = "client_id_with_token"
	GinUserInfo          = "user_info"
	ginUserToken         = "user_token"
	ginOpcIdKey          = "operate_company_id"
)

type (
	// 获取安服操作企业ID
	reqOperteCompany struct {
		OptCompanyID int64 `json:"operate_company_id"`
	}
	// AuthFunc 权限校验方法
	AuthFunc func(ctx *gin.Context) error
)

// Auth 权限校验
func Auth(wrapper AuthFunc, scope ...string) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 获取登录Token
		tokenInfo, tokenErr := auth.GetOauth2().ValidationBearerToken(ctx.Request)
		// 保存登录信息
		SetAuthInfo(ctx, tokenInfo)
		// 被调用接口,校验Scope
		if len(scope) != 0 {
			// 被调用接口,为登录即可使用
			if scope[0] != "*" {
				// 检查Token信息是否OK
				if tokenErr != nil {
					if errstd.Is(tokenErr, errors.ErrExpiredAccessToken) {
						_ = response.Gen(ctx).SendError(APIServiceName, http.StatusUnauthorized, tokenErr.Error(), nil)
					} else {
						_ = response.Gen(ctx).SendError(APIServiceName, 401, tokenErr.Error(), nil)
					}
					return
				}
				// 校验Scope权限
				if !checkScope(ctx, scope, tokenInfo) {
					return
				}
				// 客户端用户,校验客户端有效期,IP白名单,api接口调用计数
				if tokenInfo.GetUserID() == "" {
					if ok, clientErr := clientAuthCheck(ctx, true, tokenInfo); !ok {
						_ = response.Gen(ctx).SendByError(APIServiceName, clientErr)
						return
					}
				}
			}
		}
		// 执行后续动作
		if wrapperErr := wrapper(ctx); wrapperErr != nil {
			_ = response.Gen(ctx).SendByError(APIServiceName, wrapperErr)
			return
		}
	}
}

// 检查Scope
func checkScope(ctx *gin.Context, scope []string, tokenInfo oauth.TokenInfo) bool {
	// 获取用户的scope
	userScope := scopeToMap(tokenInfo.GetScope())
	// 用户Scope权限为空
	if userScope == nil {
		_ = response.Gen(ctx).SendError(APIServiceName, 401, "没有相关权限!", nil)
		return false
	}
	// 检查用户是否拥有*权限
	if _, ok := userScope["*"]; ok {
		return true
	}
	// 校验接口的Scope规则
	for _, s := range scope {
		orScope := strings.Split(s, "|")
		if len(orScope) == 1 { // 检查并且
			if _, ok := userScope[s]; !ok {
				_ = response.Gen(ctx).SendError(APIServiceName, 401, "没有相关权限!", nil)
				return false
			}
		} else { // 检查或
			isOk := false
			for _, os := range orScope {
				if _, ok := userScope[os]; ok {
					isOk = true
					continue
				}
			}
			if !isOk {
				_ = response.Gen(ctx).SendError(APIServiceName, 401, "没有相关权限!", nil)
				return false
			}
		}
	}
	return true
}

// SetAuthInfo 设置登录信息
func SetAuthInfo(ctx *gin.Context, tokenInfo oauth.TokenInfo) {
	// 将用户ID保存在上下文中
	ctx.Set(ginUserIdWithToken, "")
	// 将ClientId保存在上下文中
	ctx.Set(ginClientIdWithToken, "")
	// 将Token信息保存在上下文中
	ctx.Set(ginUserToken, tokenInfo)
	// 将用户信息保存在上下文中
	ctx.Set(GinUserInfo, nil)
	if tokenInfo != nil {
		// 将用户ID保存在上下文中
		ctx.Set(ginUserIdWithToken, tokenInfo.GetUserID())
		// 将ClientId保存在上下文中
		ctx.Set(ginClientIdWithToken, tokenInfo.GetClientID())
		// 将Token信息保存在上下文中
		ctx.Set(ginUserToken, tokenInfo)
		// 将用户信息保存在上下文中
		if userId := tokenInfo.GetUserID(); userId != "" {
			u, _ := user.NewUserModel().FindById(userId)
			ctx.Set(GinUserInfo, u)
		}
	}
	// 将operate_company_id保存在上下文中
	setOperateCompanyId(ctx)
}

// scopeToMap
func scopeToMap(scope string) map[string]string {
	if scope == "" {
		return nil
	}
	elements := strings.Split(scope, "|")
	elementMap := make(map[string]string)
	for i := 0; i < len(elements); i++ {
		elementMap[elements[i]] = elements[i]
	}
	return elementMap
}

// UserIDWithGinCtx 获取用户ID
func UserIDWithGinCtx(ctx *gin.Context) uint64 {
	return cast.ToUint64(ctx.GetString(ginUserIdWithToken))
}

// setOperateCompanyId 保存operateCompanyId
func setOperateCompanyId(ctx *gin.Context) {
	var operateCompanyId string
	switch ctx.Request.Method {
	case http.MethodGet:
		operateCompanyId = ctx.Query(ginOpcIdKey)
	case http.MethodPost, http.MethodPut, http.MethodDelete:
		var param reqOperteCompany
		body := &bytes.Buffer{}
		_, _ = io.Copy(body, ctx.Request.Body)
		_ = sonic.Unmarshal(body.Bytes(), &param)
		// write back data to request body
		ctx.Request.Body = io.NopCloser(body)
		operateCompanyId = strconv.FormatInt(param.OptCompanyID, 10)
	}
	ctx.Set(ginOpcIdKey, operateCompanyId)
}

// OperateCompanyId 操作企业ID
func OperateCompanyId(ctx *gin.Context) int64 {
	return cast.ToInt64(ctx.GetString(ginOpcIdKey))
}

func ClientId(ctx *gin.Context) string {
	return ctx.GetString(ginClientIdWithToken)
}
