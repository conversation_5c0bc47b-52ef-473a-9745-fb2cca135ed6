package upload

import (
	"fmt"
	"os"
	"time"

	"micro-service/pkg/utils"
)

const mb = 1 << 20 // 1M

func FileOverLimit(fileSize, maxSize int64) bool {
	return fileSize > maxSize*mb
}

func genFileName(fileName string) string {
	randString := utils.RandString(20)
	timestamp := time.Now().Format("20060102150405")
	return fmt.Sprintf("%s_%s_%s", utils.Md5sHash(fileName, false), randString, timestamp)
}

func Mkdir(path string) (err error) {
	if path == "" {
		return nil
	}

	_, err = os.Stat(path)
	if os.IsNotExist(err) {
		err = os.MkdirAll(path, os.ModePerm)
		return err
	}
	return nil
}
