package upload

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUploadDecrypt(t *testing.T) {
	s := "/api/v1/files/eyJpdiI6IitxUnpnbi8zdGx5QUk2MFE3U3d2OHc9PSIsIm1hYyI6IjliMzUwM2UzNjVmYWZjMzJkYThkZjExNDI4MjNmMTU5MGE1NWZmYjZkODhkOGZlM2YwNzU3NzU0ZjNiODBkOTEiLCJ2YWx1ZSI6IlBsSWlPYythSGhmRU5qSWw0TytkdlllWGFuTUkxTWlMUXQ3bmppc0JIVFNhcytDOW1mODV0OVU5d3V4N3djMHovUVVCc2ZWeVY4bElmS3BhUGRNdTFKbnAvbDdiazRUTW1qTlAwTkthczZNNUZNbU03Qno4c0R2NXRqS05MYll4RjVrTElhR3VqTHNmemY1YnFhaTR4TnNYcnpic0oxR0Q2VkVVWHBRamJpT0NIcEZPSENlRjYvQTA2UzBPbzltWWNyakVkSmhwZWF0b0FYMnhYSzBWOFhpaTFFTE9nMWR0aU9Ib1lRNWZiSlZHTCtZd2o3Y3NqZFA5dkhSYitOb1o5R2FQd3dVa2Y5ZVlzdi9xa0xQdzNWNk5GY3U5VTBNME45YWFLcXg2OFRVPSJ9.xlsx"
	meta, err := UploadDecrypt(s)
	assert.Nil(t, err)
	assert.Equal(t, "app/public/2d085173bb00195f7d406e439dd83c37_eZMKfvdvgYwOzawqDKLH_20230427143738.xlsx", meta.Url)
}
