package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// engineRulesRouterRegister 规则引擎路由注册
func engineRulesRouterRegister(r *gin.RouterGroup) {
	// 规则管理
	r.GET("list", wrapper.Auth(api.EngineRuleList, scopeTenantOrSafe))           // 规则列表
	r.GET("category", wrapper.Auth(api.EngineRuleCategory, scopeTenantOrSafe))   // 规则分类
	r.POST("list", wrapper.Auth(api.EngineRuleCreate, scopeSafe))                // 新建规则
	r.PUT("list", wrapper.Auth(api.EngineRuleUpdate, scopeSafe))                 // 更新规则
	r.DELETE("list", wrapper.Auth(api.EngineRuleDelete, scopeSafe))              // 删除规则
	r.PUT("status", wrapper.Auth(api.EngineRuleUpdateStatus, scopeTenantOrSafe)) // 更新规则状态
	r.POST("match", wrapper.Auth(api.EngineRuleMatch, scopeAll))                 // 规则匹配
	r.GET("sync", wrapper.Auth(api.EngineRuleDataSync, scopeTenantOrSafe))       // 本地化同步线上规则
	r.POST("expose", wrapper.Auth(api.EngineRuleExpose, scopeClient))            // saas平台暴露规则
}
