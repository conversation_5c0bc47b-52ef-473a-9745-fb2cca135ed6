package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// FofaRegister 系统公告路由
func FofaRegister(r *gin.RouterGroup) {
	// Fofa-query
	r.POST("/query", wrapper.Auth(api.Query, "client"))
	// Fofa-count
	r.GET("/query_count", wrapper.Auth(api.QueryCount, "client"))
	// Fofa-host
	r.POST("/host", wrapper.Auth(api.QueryHost, "client"))
	// Fofa-all-assets-count
	r.GET("/all_count", wrapper.Auth(api.QueryAllAssetsCount, "client"))
	// FOfa-子域名解析
	r.GET("/puredns", wrapper.Auth(api.QueryPureDns, "client"))
	// 获取fofa热点信息
	r.GET("/hot/:count", wrapper.Auth(api.Fofa<PERSON>ot, "client"))
	// 空间搜索
	r.GET("/parse_query", wrapper.Auth(api.FOFAQueryParse, scopeTenantOrSafe+"|"+scopeAdmin))
	// 空间搜索-结果聚合
	r.GET("parse_statistics", wrapper.Auth(api.FOFAParseStatistics, scopeTenantOrSafe+"|"+scopeAdmin))
	// FOFA账号详情接口
	r.GET("/account_info", wrapper.Auth(api.FofaAccountInfo, scopeAdmin))
	// fofa扫描
	// FOFA-创建扫描任务
	r.POST("/task/scan", wrapper.Auth(api.FofaCreateScanTask, scopeFofaScan))
	// FOFA-创建探活任务
	r.POST("/task/detection", wrapper.Auth(api.FofaCreateDetectionTask, scopeFofaScan))
	// FOFA-获取任务状态
	r.GET("/task/status/:id", wrapper.Auth(api.FofaGetTaskStatus, scopeFofaScan))
	// FOFA-获取任务结果
	r.GET("/task/result/:id", wrapper.Auth(api.FofaGetTaskResult, scopeFofaScan))
	// FOFA-批量下发给fofa去更新相关的域名数据
	r.POST("/domain/update", wrapper.Auth(api.FofaDomainUpdateTask, scopeFofaScan))
}
