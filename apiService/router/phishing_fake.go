package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 钓鱼仿冒路由
func phishingFakeRouteRegister(r *gin.RouterGroup) {
	r.GET("task", wrapper.Auth(api.PhishingFakeTaskList, scopeTenantOrSafe))      // 任务列表
	r.DELETE("task", wrapper.Auth(api.PhishingFakeTaskDelete, scopeTenantOrSafe)) // 任务删除

	asset := r.Group("assets")
	// 仿冒资产发现
	asset.POST("find", wrapper.Auth(api.PhishingFakeAssetsFind, scopeTenantOrSafe))
	// 资产聚合统计
	asset.GET("agg", wrapper.Auth(api.PhishingFakeAssetsAgg, scopeTenantOrSafe))
	// 仿冒资产统计
	asset.GET("count", wrapper.Auth(api.PhishingFakeAssetsCount, scopeTenantOrSafe))
	// 仿冒资产同步威胁资产
	asset.POST("sync", wrapper.Auth(api.PhishingFakeAssetsSync, scopeTenantOrSafe))
	// 仿冒资产截图重试
	asset.POST("screenshot", wrapper.Auth(api.PhishingFakeAssetsRetryScreenshot, scopeTenantOrSafe))
	// 分页获取仿冒资产
	asset.POST("list", wrapper.Auth(api.PhishingFakeAssetList, scopeTenantOrSafe))
	// 仿冒资产探测
	asset.PUT("detect", wrapper.Auth(api.PhishingFakeAssetsDetect, scopeTenantOrSafe))
	// 仿冒资产入账&深度探测
	asset.PUT("deep/detect", wrapper.Auth(api.PhishingFakeAssetsDeepDetect, scopeTenantOrSafe))
	// 仿冒资产导入
	asset.POST("import", wrapper.Auth(api.PhishingFakeAssetsImport, scopeTenantOrSafe))
	// 仿冒资产数据下载
	asset.GET("download", wrapper.Auth(api.PhishingFakeAssetsDownload, scopeTenantOrSafe))
}
