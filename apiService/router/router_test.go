package router

import (
	"fmt"
	microZap "github.com/go-micro/plugins/v4/logger/zap"
	"github.com/hashicorp/go-hclog"
	toZap "github.com/zaffka/zap-to-hclog"
	"go-micro.dev/v4/logger"
	"go.uber.org/zap"
	"micro-service/initialize/mysql"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/log"
	"net/http/httptest"
	"testing"
)

func Init() {
	cfg.InitLoadCfg()
	// 初始化日志
	log.Init()
	zap.ReplaceGlobals(log.GetLogger())
	hclog.SetDefault(toZap.Wrap(log.GetLogger()))
	z, _ := microZap.NewLogger(microZap.WithConfig(log.GetZapConfig()))
	logger.DefaultLogger = z
	// Mysql&Redis
	_ = mysql.GetInstance(cfg.LoadMysql())
	_ = redis.GetInstance(cfg.LoadRedis())
}

func Test_Intelligence(t *testing.T) {
	Init()
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/intelligence/hot-poc/asset/export?id=aaaa&id=bbbbb&user_id=1&per_page=10&page=1", nil)
	req.Header.Set("Authorization", "Bearer 1518|NJEYMZRMYJCTZJQWMI0ZOWM5LWI3NWQTYWM2Y2MZMZC0YTU3")
	GetRouteInstance().ServeHTTP(w, req)
	fmt.Println(w.Body.String())
	fmt.Println(w.Result())
}
