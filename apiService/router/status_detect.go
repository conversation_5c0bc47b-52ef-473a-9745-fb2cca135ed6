package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 任务管理/资产探知/资产状态检测

// 资产状态检测路由注册
func assetStatusDetectRouterRegister(r *gin.RouterGroup) {
	r.POST("/start", wrapper.Auth(api.AssetsStatusDetectStart, scopeTenantOrSafe))
	r.GET("/info", wrapper.Auth(api.AssetsStatusDetectInfo, scopeTenantOrSafe))
	r.GET("/list", wrapper.Auth(api.AssetsStatusDetectList, scopeTenantOrSafe))
	r.GET("/status", wrapper.Auth(api.AssetsStatusDetectProgress, scopeTenantOrSafe))
	r.DELETE("/del", wrapper.Auth(api.DelAssetsStatusDetectDel, scopeTenantOrSafe))
	r.GET("/download", wrapper.Auth(api.AssetsStatusDownload, scopeTenantOrSafe))
}

// url状态检测路由注册
func urlStatusDetectRouterRegister(r *gin.RouterGroup) {
	r.POST("/start", wrapper.Auth(api.UrlStatusDetectStart, scopeTenantOrSafe))
	r.GET("/info", wrapper.Auth(api.UrlStatusDetectInfo, scopeTenantOrSafe))
	r.GET("/list", wrapper.Auth(api.UrlStatusDetectList, scopeTenantOrSafe))
	r.GET("/status", wrapper.Auth(api.UrlStatusDetectProgress, scopeTenantOrSafe))
	r.DELETE("/del", wrapper.Auth(api.DelUrlStatusDetectDel, scopeTenantOrSafe))
	r.GET("/download", wrapper.Auth(api.UrlStatusDownload, scopeTenantOrSafe))
}
