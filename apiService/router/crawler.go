package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 爬虫路由
func crawlerRouterRegister(r *gin.RouterGroup) {
	// 爬虫
	r.POST("/", wrapper.Auth(api.Crawler, scopeClient))
	// 爬虫
	r.POST("/screenshot", wrapper.Auth(api.Screenshot, scopeClient))
	r.POST("/icon", wrapper.Auth(api.CrawlerIcon, scopeClient))
	r.POST("/chrome_get", wrapper.Auth(api.CrawlerChromeGet, scopeClient))
}
