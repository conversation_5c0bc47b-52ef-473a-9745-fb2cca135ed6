package router

import (
	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"

	"github.com/gin-gonic/gin"
)

// 任务概览路由
func taskOverviewRouterRegister(r *gin.RouterGroup) {
	// 任务统计: 根据任务状态
	r.GET("/count", wrapper.Auth(api.TaskOverviewCount, scopeTenantOrSafe))
	// 任务统计: 根据类别
	r.GET("count_by", wrapper.Auth(api.TaskOverviewCountByCategory, scopeTenantOrSafe))
}

// 资产概览路由
func assetsOverviewRouterRegister(r *gin.RouterGroup) {
	// 组件统计
	r.GET("/rule_count", wrapper.Auth(api.RuleCount, scopeTenantOrSafe))
	r.GET("/aaaaaaa", wrapper.Auth(api.RuleCount, scopeTenantOrSafe))
	// 数字资产
	r.GET("/digital_assets", wrapper.Auth(api.DigitalAssets, scopeTenantOrSafe))
	// 环比
	r.GET("/rate", wrapper.Auth(api.Rate, scopeTenantOrSafe))
	// 资产动态
	r.GET("/assets_dynamic", wrapper.Auth(api.AssetsDynamic, scopeTenantOrSafe))
	// 资产动态变更
	r.GET("/assets_dynamic_change", wrapper.Auth(api.AssetsOverviewAssetsDynamicCount, scopeTenantOrSafe))
}
