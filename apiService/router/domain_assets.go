package router

import (
	"github.com/gin-gonic/gin"

	"micro-service/apiService/api"
	"micro-service/apiService/wrapper"
)

// 域名资产
func domainAssetsRouterRegister(r *gin.RouterGroup) {
	// 获取cron任务信息
	r.GET("cron", wrapper.Auth(api.DomainAssetsCronInfo, scopeTenantOrSafe))
	// 重新解析
	r.POST("reparse", wrapper.Auth(api.DomainAssetsUpdateByCron, scopeTenantOrSafe))
	// 获取域名列表
	r.GET("domains", wrapper.Auth(api.DomainAssetsDomainFilter, scopeTenantOrSafe))
	// 域名文件数据上传
	r.POST("upload", wrapper.Auth(api.DomainAssetsUpload, scopeTenantOrSafe))
	// 域名总资产列表
	r.GET("list", wrapper.Auth(api.DomainAssetsList, scopeAll))
	// 域名总资产列表导出
	r.POST("list/export", wrapper.Auth(api.DomainAssetsExport, scopeAll))
	// 域名资产删除
	r.DELETE("list", wrapper.Auth(api.DomainAssetsDelete, scopeAll))
	// Fofa更新
	r.POST("list/fofa_update", wrapper.Auth(api.FofaUpdate, scopeTenantOrSafe))
	// 上传域名保存
	r.POST("push", wrapper.Auth(api.PushDomainAssets, scopeTenantOrSafe))
}

// 域名搜索
func domainSearchRouterRegister(r *gin.RouterGroup) {
	// 任务详情
	r.GET("", wrapper.Auth(api.DomainSearchTaskInfo, scopeClient))
	// 下发任务
	r.POST("", wrapper.Auth(api.DomainSearchTaskCreate, scopeClient))
	// 任务结果
	r.GET("/result", wrapper.Auth(api.DomainSearchTaskResultList, scopeClient))
}
