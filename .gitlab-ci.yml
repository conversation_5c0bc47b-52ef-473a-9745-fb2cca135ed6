# 定义 stages
stages:
  - lint
  - vuln_test
  - build
  - deploy
lint:
  stage: lint
  script:
    - cd /data/micro-service && git reset --hard HEAD && git fetch --depth 1 --tags && git checkout $CI_COMMIT_TAG
    - cd /data/micro-service && go mod tidy
    # generate proto
    - cd /data/micro-service/coreService && make
    - cd /data/micro-service/apiService && make
    - cd /data/micro-service/webService && make
    - cd /data/micro-service/crawlerService && make
    - cd /data/micro-service/scanService && make
    - cd /data/micro-service/cronService && make
  only:
    - tags
  tags:
    - deploy-11.14
  variables:
    GOROOT: "/usr/local/go"
    DOCKER_REGISTER_ADDR: "**************"
vuln_test:
  stage: vuln_test
  allow_failure: true
  only:
    - tags
  tags:
    - deploy-11.14
  script:
    - cd /data/micro-service
    # unit test
    - ./unit_test.sh api web cli crawler pkg
    # import pkg vuln scan
    - osv-scanner .
build:
  stage: build
  needs: [lint]
  when: on_success
  script:
    - docker_login_pro
    - cd /data/micro-service/scanService && rm -rf foradar_scan_linux_arm64
    - docker volume prune  -f && docker image prune -f && docker system prune -f
    - cd /data/micro-service && git reset --hard HEAD && git tag -l | xargs -r git tag -d
    - cd /data/micro-service && git fetch --depth 1 --tags && git switch $CI_COMMIT_TAG --detach && sudo chmod 777 -Rf /data/micro-service/bin
    - cd /data/micro-service && goreleaser --rm-dist --skip=nfpm
  only:
    - tags
  tags:
    - deploy-11.14
  variables:
    GOROOT: "/usr/local/go"
    DOCKER_REGISTER_ADDR: "**************"
    GORELEASER_CURRENT_TAG: "$CI_COMMIT_TAG"

deploy:
  stage: deploy
  script:
    - docker images|grep foradar|grep $DOCKER_REGISTER_ADDR |grep -v "<none>"|awk '{printf "%s:%s\n",$1,$2}'|xargs -r -t -I {} docker push {}
    - docker images|grep foradar|grep $DOCKER_REGISTER_ADDR |awk '{print $3}'|xargs -r docker rmi -f
    - bash ~/.ssh/deployment.sh micro-service $CI_COMMIT_TAG
  only:
    - tags
  tags:
    - deploy-11.14
  variables:
    GOROOT: "/usr/local/go"
    DOCKER_REGISTER_ADDR: "**************"
    GORELEASER_CURRENT_TAG: "$CI_COMMIT_TAG"
  when: manual
