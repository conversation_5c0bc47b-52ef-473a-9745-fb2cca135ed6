package handlers

import (
	"context"
	"encoding/json"
	"fmt"
	"html"
	"math"
	corePB "micro-service/coreService/proto"
	"micro-service/initialize/es"
	redisInit "micro-service/initialize/redis"
	"micro-service/middleware/elastic"
	"micro-service/middleware/elastic/fofaee_assets"
	"micro-service/middleware/elastic/fofaee_service"
	"micro-service/middleware/elastic/fofaee_subdomain"
	"micro-service/middleware/elastic/fofaee_task_assets"
	"micro-service/middleware/elastic/foradar_assets"
	"micro-service/middleware/elastic/recommend_record"
	"micro-service/middleware/elastic/recommend_result"
	"micro-service/middleware/mysql"
	"micro-service/middleware/mysql/black_keyword_system"
	"micro-service/middleware/mysql/black_keyword_type"
	"micro-service/middleware/mysql/clues"
	"micro-service/middleware/mysql/company"
	"micro-service/middleware/mysql/detect_assets_tasks"
	"micro-service/middleware/mysql/domain_assets"
	"micro-service/middleware/mysql/ip_history"
	"micro-service/middleware/mysql/organization_discover_task"
	"micro-service/middleware/mysql/port_group"
	"micro-service/middleware/mysql/risks"
	"micro-service/middleware/mysql/scan_task"
	"micro-service/middleware/mysql/task"
	"micro-service/middleware/mysql/user"
	"micro-service/middleware/redis"
	"micro-service/pkg/cache"
	"micro-service/pkg/cert"
	"micro-service/pkg/cfg"
	"micro-service/pkg/dns"
	domain_utils "micro-service/pkg/domain"
	"micro-service/pkg/log"
	"micro-service/pkg/microx"
	"micro-service/pkg/mq_pool"
	"micro-service/pkg/network"
	asyncq "micro-service/pkg/queue_helper"
	"micro-service/pkg/rule_engine"
	"micro-service/pkg/utils"
	"micro-service/pkg/websocket_message"
	pb "micro-service/scanService/proto"
	"net"
	"os"
	"reflect"
	"slices"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/spf13/cast"

	"github.com/go-errors/errors"
	"github.com/ipipdotnet/datx-go"

	netUrl "net/url"

	goRedis "github.com/go-redis/redis/v8"
	es2 "github.com/olivere/elastic"
	amqp "github.com/rabbitmq/amqp091-go"
	"gorm.io/gorm"
)

// cleanScanAssetData 清理scanAsset数据，移除可能导致ES更新失败的问题数据
func cleanScanAssetData(scanAsset map[string]interface{}) map[string]interface{} {
	cleaned := make(map[string]interface{})

	for key, value := range scanAsset {
		// 跳过nil值
		if value == nil {
			continue
		}

		// 特殊处理port_list和host_list，确保内部不包含问题字段
		if key == "port_list" || key == "host_list" {
			if reflect.ValueOf(value).Kind() == reflect.Slice {
				sliceValue := reflect.ValueOf(value)
				cleanedSlice := make([]interface{}, 0, sliceValue.Len())

				for i := 0; i < sliceValue.Len(); i++ {
					item := sliceValue.Index(i).Interface()
					if itemMap, ok := item.(map[string]interface{}); ok {
						cleanedItem := make(map[string]interface{})
						for itemKey, itemValue := range itemMap {
							// 移除可能导致ES映射冲突的字段
							if itemKey == "updated_at" || itemKey == "created_at" ||
								itemKey == "source_updated_at" || itemKey == "banner" ||
								itemKey == "header" || itemKey == "cert" ||
								itemKey == "reason" {
								continue // 跳过这些字段
							}

							// 处理字符串长度限制
							if str, ok := itemValue.(string); ok && len(str) > 32766 {
								cleanedItem[itemKey] = str[:32766]
							} else {
								cleanedItem[itemKey] = itemValue
							}
						}
						cleanedSlice = append(cleanedSlice, cleanedItem)
					} else {
						cleanedSlice = append(cleanedSlice, item)
					}
				}
				cleaned[key] = cleanedSlice
			} else {
				cleaned[key] = value
			}
			continue
		}

		// 处理字符串类型
		if str, ok := value.(string); ok {
			// 移除过长的字符串（ES有字段长度限制）
			if len(str) > 32766 { // ES keyword字段的最大长度
				cleaned[key] = str[:32766]
			} else {
				cleaned[key] = str
			}
			continue
		}

		// 处理切片类型
		if reflect.ValueOf(value).Kind() == reflect.Slice {
			sliceValue := reflect.ValueOf(value)
			// 限制数组长度，避免过大的数组
			if sliceValue.Len() > 1000 {
				// 截取前1000个元素
				newSlice := make([]interface{}, 1000)
				for i := 0; i < 1000; i++ {
					newSlice[i] = sliceValue.Index(i).Interface()
				}
				cleaned[key] = newSlice
			} else {
				cleaned[key] = value
			}
			continue
		}

		// 其他类型直接复制
		cleaned[key] = value
	}

	return cleaned
}

// safeStringValue 安全地将interface{}转换为字符串，避免nil变成"<nil>"
func safeStringValue(value interface{}) string {
	if value == nil {
		return ""
	}
	str := utils.SafeString(value)
	// 额外检查，防止"<nil>"字符串
	if str == "<nil>" {
		return ""
	}
	return str
}

// safeNumericValue 安全地处理数字类型字段，nil返回nil而不是"<nil>"
func safeNumericValue(value interface{}) interface{} {
	if value == nil {
		return nil
	}
	// 如果是字符串"<nil>"，返回nil
	if str, ok := value.(string); ok && str == "<nil>" {
		return nil
	}
	return value
}

// safeStringArray 安全地处理字符串数组，过滤nil和空值
func safeStringArray(value interface{}) []string {
	if value == nil {
		return []string{}
	}

	switch v := value.(type) {
	case []string:
		result := make([]string, 0)
		for _, str := range v {
			if str != "" && str != "<nil>" {
				result = append(result, str)
			}
		}
		return result
	case []interface{}:
		result := make([]string, 0)
		for _, item := range v {
			if item != nil {
				str := safeStringValue(item)
				if str != "" && str != "<nil>" {
					result = append(result, str)
				}
			}
		}
		return result
	case string:
		if v != "" && v != "<nil>" {
			return []string{v}
		}
		return []string{}
	default:
		str := safeStringValue(v)
		if str != "" && str != "<nil>" {
			return []string{str}
		}
		return []string{}
	}
}

// safeClueCompanyName 安全地处理clue_company_name字段，返回字符串类型
// 如果输入是数组，取第一个元素；如果是字符串，直接使用
func safeClueCompanyName(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case []string:
		if len(v) > 0 && v[0] != "" && v[0] != "<nil>" {
			return v[0]
		}
		return ""
	case []interface{}:
		if len(v) > 0 && v[0] != nil {
			str := safeStringValue(v[0])
			if str != "" && str != "<nil>" {
				return str
			}
		}
		return ""
	case string:
		if v != "" && v != "<nil>" {
			return v
		}
		return ""
	default:
		str := safeStringValue(v)
		if str != "" && str != "<nil>" {
			return str
		}
		return ""
	}
}

// safeGetClueCompanyName 从map中安全地获取clue_company_name字段
func safeGetClueCompanyName(reasonMap map[string]interface{}) string {
	if reasonMap == nil {
		return ""
	}

	// 尝试多种可能的字段名
	fieldNames := []string{"clue_company_name", "clueCompanyName", "company_name"}

	for _, fieldName := range fieldNames {
		if value, exists := reasonMap[fieldName]; exists && value != nil {
			// 使用现有的 safeClueCompanyName 函数处理各种类型
			if result := safeClueCompanyName(value); result != "" {
				return result
			}
		}
	}

	return ""
}

// getMapKeys 获取map的所有key，用于调试
func getMapKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

// getInt64Pointer 安全地将interface{}转换为*int64
func getInt64Pointer(value interface{}) *int64 {
	if value == nil {
		return nil
	}
	if intVal := utils.GetIntValue(value); intVal != 0 {
		val := int64(intVal)
		return &val
	}
	return nil
}

// getRecommendInfoUrl 安全地获取recommendInfo的URL，用于调试
func getRecommendInfoUrl(recommendInfo *recommend_result.RecommendResult) string {
	if recommendInfo == nil {
		return "<nil>"
	}
	return recommendInfo.Url
}

type ScanForadarAsset struct {
	Task            scan_task.ScanTasks
	DetectTask      *detect_assets_tasks.DetectAssetsTask
	OrganDetectTask *organization_discover_task.OrganizationDiscoverTask // 新增：组织架构测绘任务
	Company         company.Company
	CompanyName     string // 新增：企业名称
	StartAt         time.Time
	isHandle        bool          // 标识是否正在处理中
	isAllPort       bool          // 是否全端口扫描
	portInfoArr     []interface{} // 禁用的端口不获取了，单独设置个参数 - 对应PHP: $this->portInfoArr
}

// UnifiedAsset 定义统一的资产结构
type UnifiedAsset struct {
	IP                 string
	Port               int
	Protocol           string
	Banner             string
	Header             string
	Title              string
	Host               string
	Domain             string
	Subdomain          string
	Body               string // 对应PHP: $subAsset->body
	RuleTags           []fofaee_subdomain.Rule
	IsSubdomain        bool          // 标识是否来自subdomain
	Favicon            *AssetFavicon // 对应PHP: $subAsset->favicon
	Logo               *AssetLogo    // 对应PHP: $subAsset->logo
	IsRecommendResult  bool
	RecommendReason    []RecommendReason // 推荐理由
	AssetsSource       *int64
	OneforallSource    string
	SourceUpdatedAt    string
	AssetsSourceDomain string
	IsCdn              bool
	CloudName          string
	IsIpv6             bool
}

// AssetFavicon 对应PHP中的favicon结构
type AssetFavicon struct {
	Hash   string `json:"hash"`
	Base64 string `json:"base64"`
}

// AssetLogo 对应PHP中的logo结构
type AssetLogo struct {
	Hash    string `json:"hash"`
	Content string `json:"content"`
}

func ScanForadarAssetHandler(ctx context.Context, req *asyncq.Task) error {
	log.Infof("[步骤流转][ScanForadarAssetHandler] 开始处理扫描资产任务")
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]Received ScanForadarAssetHandler request: %v", req.Payload)
	payload := &asyncq.TaskIdPayload{}
	if err := json.Unmarshal([]byte(req.Payload), payload); err != nil {
		return err
	}
	taskId := payload.TaskId
	log.Infof("[步骤流转][ScanForadarAssetHandler] 扫描资产任务id:%d", taskId)
	st, err := mysql.NewDSL[scan_task.ScanTasks]().FindByID(taskId)
	if err != nil {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]获取任务失败，任务ID: %d，错误: %v", taskId, err)
		return nil
	}
	log.Infof("[ScanForadarAssetHandler]扫描任务st:%+v", st)
	if st.Status == scan_task.StatusFinished && st.Step == scan_task.StepFinished {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]任务已完成，任务ID: %d", taskId)
		return nil
	}
	var detectTask *detect_assets_tasks.DetectAssetsTask
	var organDetectTask *organization_discover_task.OrganizationDiscoverTask // 新增：组织架构测绘任务
	var companyData company.Company
	var companyName string

	// 检查是否有检测任务ID，有的话尝试获取 - 对应PHP: if ($detectId = object_get($this->st, 'detect_assets_tasks_id'))
	if st.DetectAssetsTasksId > 0 {
		if dt, err := mysql.NewDSL[detect_assets_tasks.DetectAssetsTask]().FindByID(uint64(st.DetectAssetsTasksId)); err == nil {
			detectTask = &dt
		} else {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取检测任务失败，任务ID: %d，错误: %v", st.DetectAssetsTasksId, err)
		}
	} else {
		log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]没有检测任务ID，任务ID: %d", st.ID)
	}

	// 检查是否有组织架构测绘任务ID，有的话尝试获取 - 对应PHP: if ($detectOrgId = object_get($this->st, 'organization_discover_task_id'))
	if st.OrganizationDiscoverTaskId != nil && *st.OrganizationDiscoverTaskId > 0 {
		if odt, err := mysql.NewDSL[organization_discover_task.OrganizationDiscoverTask]().FindByID(*st.OrganizationDiscoverTaskId); err == nil {
			organDetectTask = &odt
		}
	}

	// 检查是否有公司ID，有的话尝试获取 - 对应PHP: if ($companyId = object_get($st, 'company_id'))
	if st.CompanyId > 0 {
		if comp, err := mysql.NewDSL[company.Company]().FindByID(uint64(st.CompanyId)); err == nil {
			companyData = comp
			companyName = comp.Name // 对应PHP: $this->companyName = object_get(Company::query()->where('id', $companyId)->first(), 'name');
		}
	}

	scanForadarAsset := &ScanForadarAsset{
		Task:            st,
		DetectTask:      detectTask,
		OrganDetectTask: organDetectTask, // 新增：组织架构测绘任务
		Company:         companyData,
		CompanyName:     companyName, // 新增：企业名称
	}
	scanForadarAsset.Handle(ctx)
	return nil
}

func (s *ScanForadarAsset) Handle(ctx context.Context) error {
	s.StartAt = time.Now()
	s.isHandle = true // 标记为正在处理

	log.Infof("[ScanForadarAssetHandler]Handle: 开始处理扫描资产任务，任务ID: %d,detectTask:%+v", s.Task.ID, s.DetectTask)

	// 重新获取任务详情，对应PHP中的$taskInfo = Task::query()->where('id', $this->task->id)->first();
	// 不是必要的，上面的ScanForadarAssetHandler中已经获取过了
	// taskInfo, err := mysql.NewDSL[scan_task.ScanTasks]().FindByID(uint64(s.Task.ID))
	// if err != nil {
	// 	log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]重新获取任务详情失败，任务ID: %d，错误: %v", s.Task.ID, err)
	// 	return nil
	// }
	// if taskInfo.ID > 0 {
	// 	log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]UpdateA-ScanForadarAssets TaskId:%d %d进度：%.2f", s.Task.ID, taskInfo.Status, taskInfo.Progress)
	// }

	// 更新任务状态为进行中
	_, err := mysql.NewDSL[scan_task.ScanTasks]().UpdateByID(
		uint64(s.Task.ID),
		map[string]interface{}{
			"status": scan_task.StatusDoing,
			"step":   scan_task.StepSyncData,
		},
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新任务状态失败，任务ID: %d，错误: %v", s.Task.ID, err)
		return nil
	}
	//if rowsAffected == 0 {
	//	log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新任务状态失败，任务ID: %d，影响行数: %d", s.Task.ID, rowsAffected)
	//	return nil
	//}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-同步数据,开始执行，任务id=%d", s.Task.ID)
	s.syncToAsset(ctx)

	err = s.updateUserInfo(ctx)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新用户信息失败，任务ID: %d，错误: %v", s.Task.ID, err)
		return nil
	}
	log.Infof("[ScanForadarAssetHandler]Handle: 更新用户信息完成，任务ID: %d", s.Task.ID)

	// 对应PHP的__destruct方法，在处理完成后执行清理逻辑
	s.finalizeTask(ctx)

	return nil
}

func (s *ScanForadarAsset) updateUserInfo(ctx context.Context) error {
	if s.DetectTask == nil {
		log.Infof("[ScanForadarAssetHandler]updateUserInfo: 没有测绘任务，不更新用户信息")
		return nil
	}

	// 获取用户信息
	userModel := user.NewUserModel()
	userInfo, err := userModel.FindById(s.Task.UserId)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]updateUserInfo: 获取用户信息失败，用户ID: %d，错误: %v", s.Task.UserId, err)
		return err
	}
	if userInfo == nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]updateUserInfo: 用户不存在，用户ID: %d", s.Task.UserId)
	}

	// 判断是否是首次测绘任务
	if s.DetectTask == nil || userInfo.FirstDetectTaskId != uint64(s.DetectTask.ID) {
		var currentDetectTaskId uint64 = 0
		if s.DetectTask != nil {
			currentDetectTaskId = uint64(s.DetectTask.ID)
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]updateUserInfo: 不是首次测绘任务，用户ID: %d，首次测绘任务ID: %d，当前测绘任务ID: %d", s.Task.UserId, userInfo.FirstDetectTaskId, currentDetectTaskId)
		return nil
	}

	// 此处需要获取最新的测绘任务信息，起面的步骤可能更新了其中的值
	// 这里s.DetectTask不会为nil，因为前面已经检查过了，但为了代码健壮性再次检查
	if s.DetectTask == nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]updateUserInfo: DetectTask为nil，无法获取最新信息")
		return fmt.Errorf("DetectTask为nil")
	}

	detectTask, err := mysql.NewDSL[detect_assets_tasks.DetectAssetsTask]().FindByID(uint64(s.DetectTask.ID))
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]updateUserInfo: 获取测绘任务失败，任务ID: %d，错误: %v", s.DetectTask.ID, err)
		return err
	}
	s.DetectTask = &detectTask
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]updateUserInfo: 更新用户信息，任务ID: %d，最新的测绘任务信息: %+v", s.Task.ID, s.DetectTask)

	// 更新用户信息
	updateData := make(map[string]interface{})

	// 检查当前扫描任务是否是测绘任务的最后一个任务
	isLastScanTask := s.isLastScanTaskOfDetectTask(ctx)

	// step=4,step_detail=401,step_status=1,status=1/2/3
	// 只有当前扫描任务是测绘任务最后一个任务时，才进行这个判断
	if isLastScanTask && s.DetectTask.Step == detect_assets_tasks.StepFour && s.DetectTask.StepDetail == detect_assets_tasks.StepFourScan && s.DetectTask.StepStatus == detect_assets_tasks.StepStatusDone && slices.Contains([]int{detect_assets_tasks.StatusDoing, detect_assets_tasks.StatusExceptionFinished, detect_assets_tasks.StatusFinished}, s.DetectTask.Status) {
		updateData["detect_tasks_step"] = user.DetectTasksStepSuccess
	} else {
		updateData["detect_tasks_step"] = user.DetectTasksStepFailed
	}
	err = userModel.UpdateAny(userInfo.Id, updateData)
	if err != nil {
		return err
	}

	return nil
}

func (s *ScanForadarAsset) syncToAsset(ctx context.Context) bool {
	// 声明函数级别的变量，避免重定义
	var progress float64
	var target, message string
	var subdomainAll []*fofaee_subdomain.FofeeSubdomain
	var serviceAll []*fofaee_service.FofaeeService
	var err error
	var totalCount int64
	var current int
	var emptyPortTaskAssets []*fofaee_task_assets.FofaeeTaskAssets

	// 刷新索引可见
	es.GetInstance().Refresh(
		new(fofaee_task_assets.FofaeeTaskAssets).IndexName(),
		new(fofaee_subdomain.FofeeSubdomain).IndexName(),
		new(fofaee_service.FofaeeService).IndexName(),
	).Do(ctx)
	time.Sleep(5 * time.Second)

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-打印时间，tid:%d，start_time:%d，setp:微内核第四步开始同步数据打印时间，now:%d，cost:%d",
		s.Task.ID, s.Task.StartAt.Unix(), time.Now().Unix(), time.Now().Unix()-s.Task.StartAt.Unix())
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-同步数据,进到syncToAsset方法，任务id=%d", s.Task.ID)

	// 判断当前任务是否被删除
	if !s.taskExists(ctx) {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-同步数据-该任务已经被删除，任务ID:%d", s.Task.ID)
		return true
	}

	defer func() {
		if r := recover(); r != nil {
			// 安全地获取任务ID，避免空指针异常
			var taskID uint64 = 0
			if s != nil {
				// 使用defer函数内的recover来捕获可能的空指针异常
				func() {
					defer func() {
						if innerR := recover(); innerR != nil {
							log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取任务ID时发生异常: %v", innerR)
						}
					}()
					taskID = uint64(s.Task.ID)
				}()
			}

			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]微内核-资产扫描-资产数据同步异常，任务ID:%d，错误:%v", taskID, r)

			// 只有在s不为nil且taskID有效时才更新任务状态
			if s != nil && taskID > 0 {
				// 使用defer函数来安全地执行清理操作
				func() {
					defer func() {
						if innerR := recover(); innerR != nil {
							log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]清理操作时发生异常: %v", innerR)
						}
					}()

					// 更新任务状态为失败
					mysql.NewDSL[scan_task.ScanTasks]().UpdateByID(
						taskID,
						map[string]interface{}{
							"status": scan_task.StatusFailed,
						},
					)
					progress = 0.0
					target = ""
					message = ""
					// 安全调用sendProgress，确保Task结构体有效
					if s.Task.ID > 0 {
						s.sendProgress(&s.Task, &progress, &target, &message)
						s.clearFailedAssets(ctx)
					}
				}()
			}
			// 重新抛出异常
			panic(r)
		}
	}()

	// 更新用户&企业ID
	err = elastic.UpdateByParams[fofaee_task_assets.FofaeeTaskAssets](
		[][]interface{}{
			{"task_id", "=", s.Task.ID},
		},
		map[string]interface{}{
			"user_id":    s.Task.UserId,
			"company_id": s.Task.CompanyId,
		},
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新任务资产用户和企业ID失败: %v", err)
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-更新ES中的任务扫描资产用户&企业ID成功，任务ID:%d", s.Task.ID)

	// 补充扫描任务IP
	fmt.Printf("=== 准备调用 fillTaskIpByOffLine ===\n")
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]=== 准备调用 fillTaskIpByOffLine ===")
	s.fillTaskIpByOffLine(ctx)
	fmt.Printf("=== fillTaskIpByOffLine 调用完成 ===\n")
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]=== fillTaskIpByOffLine 调用完成 ===")
	time.Sleep(5 * time.Second)

	// 任务总数&IP端口列表
	totalCount, err = elastic.CountByParams[fofaee_task_assets.FofaeeTaskAssets]([][]interface{}{
		{"task_id", "=", s.Task.ID},
	})
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取任务资产总数失败: %v", err)
		return false
	}
	total := int(totalCount)
	current = 1

	// 获取任务端口信息 - 对应PHP: list($taskPorts, $isAllPort, $probePorts) = $this->getTaskScanPortInfo();
	taskPorts, isAllPort, probePorts := s.getTaskScanPortInfo()
	s.isAllPort = isAllPort // 对应PHP: $this->isAllPort = $isAllPort;

	// 禁用的端口不获取了，单独设置个参数 - 对应PHP: $this->portInfoArr = $this->getTaskScanPortInfo(true)[0];
	// 禁用的端口不获取了，单独设置个参数 - 对应PHP: $this->portInfoArr = $this->getTaskScanPortInfo(true);
	taskPortsInfo, isAllPortInfo, probePortsInfo := s.getTaskScanPortInfo(true)
	s.portInfoArr = []interface{}{taskPortsInfo, isAllPortInfo, probePortsInfo}

	// 处理task_assets部分port为空情况，用subdomain和service补全
	emptyPortTaskAssets, err = elastic.AllByParams[fofaee_task_assets.FofaeeTaskAssets](
		1000, // 一次性获取所有端口为空的记录
		[][]interface{}{
			{"task_id", "=", s.Task.ID},
			{"MUST_NOT", []interface{}{"ports", "exists"}}, // 端口为空
		},
		[]es2.Sorter{es2.NewFieldSort("_id").Desc(), es2.NewFieldSort("ip").Desc()},
	)
	if err == nil && len(emptyPortTaskAssets) > 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]emptyPortTaskAssets，任务id:%d，开始补全TaskAsset端口为空的数据", s.Task.ID)

		// 处理限制时间逻辑
		var limitTime time.Time
		if s.Task.CreatedAt.After(*s.Task.StartAt) {
			limitTime = *s.Task.StartAt
		} else {
			limitTime = s.Task.CreatedAt
		}

		// 获取所有空端口IP
		emptyPortIPs := make([]interface{}, 0, len(emptyPortTaskAssets))
		for _, asset := range emptyPortTaskAssets {
			emptyPortIPs = append(emptyPortIPs, asset.Ip)
		}

		if len(emptyPortIPs) > 0 {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]emptyPortTaskAssets: %v", emptyPortIPs)
		}

		// 获取subdomain和service数据
		subdomainEmptyPortAll, err := elastic.AllByParams[fofaee_subdomain.FofeeSubdomain](
			1000,
			[][]interface{}{
				{"ip", "in", emptyPortIPs},
				{"extra.user_id", "=", s.Task.UserId},
				{"lastupdatetime", ">=", limitTime.Format("2006-01-02 15:04:05")},
			},
			[]es2.Sorter{es2.NewFieldSort("_id").Desc(), es2.NewFieldSort("ip").Desc()},
		)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取空端口subdomain数据失败: %v", err)
		}

		serviceEmptyPortAll, err := elastic.AllByParams[fofaee_service.FofaeeService](
			1000,
			[][]interface{}{
				{"ip", "in", emptyPortIPs},
				{"extra.user_id", "=", s.Task.UserId},
				{"lastupdatetime", ">=", limitTime.Format("2006-01-02 15:04:05")},
			},
			[]es2.Sorter{es2.NewFieldSort("gid").Desc(), es2.NewFieldSort("ip").Desc()},
		)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取空端口service数据失败: %v", err)
		}

		// 按IP分组
		subdomainByIP := make(map[string][]*fofaee_subdomain.FofeeSubdomain)
		for _, subdomain := range subdomainEmptyPortAll {
			subdomainByIP[subdomain.Ip] = append(subdomainByIP[subdomain.Ip], subdomain)
		}

		serviceByIP := make(map[string][]*fofaee_service.FofaeeService)
		for _, service := range serviceEmptyPortAll {
			serviceByIP[service.IP] = append(serviceByIP[service.IP], service)
		}

		// 处理每个空端口资产
		for _, em := range emptyPortTaskAssets {
			currentTaskPorts := taskPorts
			if len(probePorts) > 0 {
				if probeInfo, exists := probePorts[em.Ip]; exists {
					currentTaskPorts = []int{}
					for _, info := range probeInfo {
						currentTaskPorts = append(currentTaskPorts, info.Port)
					}
				}
				if len(currentTaskPorts) == 0 && !isAllPort {
					// 从原有端口解析
					if len(em.Ports) > 0 {
						for _, port := range em.Ports {
							if portInt, ok := port.(int); ok {
								currentTaskPorts = append(currentTaskPorts, portInt)
							}
						}
					}
					if len(currentTaskPorts) > 0 {
						log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]列表端口扫描解析出的IP,补充扫出来的端口: %s", em.Ip)
					} else {
						log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]端口为空: %s", em.Ip)
						continue
					}
				}
			}

			subdomains := subdomainByIP[em.Ip]
			services := serviceByIP[em.Ip]
			allAssets, _ := s.getSubdomainAndServiceResult(subdomains, services, currentTaskPorts, em.Ip, isAllPort)

			if len(allAssets) > 0 {
				ports := []int{}
				protocols := []string{}
				portList := []map[string]interface{}{}

				for _, asset := range allAssets {
					if asset.Port > 0 {
						// 检查端口是否已存在
						found := false
						for _, p := range ports {
							if p == asset.Port {
								found = true
								break
							}
						}
						if !found {
							ports = append(ports, asset.Port)
						}
					}

					if asset.Protocol != "" {
						// 检查协议是否已存在
						found := false
						for _, p := range protocols {
							if p == asset.Protocol {
								found = true
								break
							}
						}
						if !found {
							protocols = append(protocols, asset.Protocol)
						}
					}

					// 组装port_list
					banner := asset.Banner
					if banner == "" {
						banner = asset.Header
					}

					portInfo := map[string]interface{}{
						"protocol": asset.Protocol,
						"port":     asset.Port,
						"banner":   banner,
						"certs":    map[string]interface{}{},
					}

					// 检查portList中是否已存在该端口
					found := false
					for _, existing := range portList {
						if existing["port"] == asset.Port {
							found = true
							break
						}
					}
					if !found {
						portList = append(portList, portInfo)
					}
				}

				// 更新task_assets数据
				updateData := map[string]interface{}{
					"ip":        s.completeIPV6(em.Ip),
					"port_size": len(ports),
					"port_list": portList,
					"ports":     ports,
					"protocols": protocols,
				}

				err = elastic.UpdateByParams[fofaee_task_assets.FofaeeTaskAssets](
					[][]interface{}{
						{"_id", "=", em.Id},
						{"task_id", "=", s.Task.ID},
					},
					updateData,
				)
				if err != nil {
					log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新空端口任务资产失败: %v", err)
				} else {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]emptyPortTaskAssets，任务id:%d，补充了一条数据，ip:%s", s.Task.ID, em.Ip)
				}
			} else {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]emptyPortTaskAssets，任务id:%d，allAssets为空，ip:%s", s.Task.ID, em.Ip)
			}
		}

		// 刷新索引
		es.GetInstance().Refresh(new(fofaee_task_assets.FofaeeTaskAssets).IndexName()).Do(ctx)
		time.Sleep(6 * time.Second)
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]emptyPortTaskAssets，任务id:%d，结束补全TaskAsset端口为空的数据", s.Task.ID)
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-打印时间，tid:%d，start_time:%d，setp:微内核第五步打印时间，now:%d，cost:%d",
		s.Task.ID, s.StartAt.Unix(), time.Now().Unix(), time.Now().Unix()-s.StartAt.Unix())

	// 开始处理资产数据 - 分块处理
	pageNum := 1
	pageSize := 100

	// 使用SearchBuilder构建查询条件
	var builder elastic.SearchBuilder

	// 添加任务ID条件
	builder.AddMust([]interface{}{"task_id", s.Task.ID})
	// 设置排序
	var sorts []es2.Sorter
	sorts = append(sorts, es2.NewFieldSort("updated_at").Desc(), es2.NewFieldSort("_id").Desc())
	for {
		// 分页获取任务资产，只选择IP和ports字段
		_, taskAssets, err2 := elastic.ListByParams[fofaee_task_assets.FofaeeTaskAssets](
			pageNum,
			pageSize,
			builder.Build(),
			sorts,
		)
		if err2 != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取任务资产失败: %v", err2)
			break
		}

		if len(taskAssets) == 0 {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]taskAssets为空，task_id:%d", s.Task.ID)
			break
		}

		// 处理限制时间逻辑
		var limitTime time.Time
		if s.Task.CreatedAt.After(*s.Task.StartAt) {
			limitTime = *s.Task.StartAt
		} else {
			limitTime = s.Task.CreatedAt
		}

		// 构建IP到端口的映射
		ipAndPorts := make(map[string][]interface{})
		allIPs := make([]string, 0, len(taskAssets))
		for _, asset := range taskAssets {
			var ports []interface{}
			if len(asset.Ports) > 0 {
				ports = asset.Ports
			} else {
				ports = []interface{}{}
			}

			// 同时为原始IP和压缩IP建立映射，确保后续查找不会失败
			ipAndPorts[asset.Ip] = ports

			compressIP := s.compressIPV6(asset.Ip)
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 原始IP: %s, 压缩IP: %s, 相等: %t", asset.Ip, compressIP, asset.Ip == compressIP)

			// 如果压缩IP与原始IP不同，也为压缩IP建立映射
			if asset.Ip != compressIP {
				ipAndPorts[compressIP] = ports
			}

			completeIP := s.completeIPV6(asset.Ip)
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 原始IP: %s, 补全IP: %s, 相等: %t", asset.Ip, completeIP, asset.Ip == completeIP)
			if asset.Ip != completeIP {
				ipAndPorts[completeIP] = ports
			}

			allIPs = append(allIPs, asset.Ip)
			allIPs = append(allIPs, compressIP)
			allIPs = append(allIPs, completeIP)
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 去重前allIPs长度: %d, 内容: %v", len(allIPs), allIPs)
		// 去重
		allIPs = utils.ListDistinctNonZero(allIPs)
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 去重后allIPs长度: %d, 内容: %v", len(allIPs), allIPs)

		// 调试ipAndPorts映射
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - ipAndPorts映射数量: %d", len(ipAndPorts))
		for ip, ports := range ipAndPorts {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - ipAndPorts[%s] = %v", ip, ports)
		}

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]最终结果 - allIPs数量: %d, 内容: %v, ipAndPorts映射数量: %d", len(allIPs), allIPs, len(ipAndPorts))

		// 批量获取subdomain和service数据
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 开始查询subdomain，查询条件IP: %v, user_id: %d, limitTime: %s", allIPs, s.Task.UserId, limitTime.Format("2006-01-02 15:04:05"))
		subdomainAll, err2 = elastic.AllByParams[fofaee_subdomain.FofeeSubdomain](
			1000,
			[][]interface{}{
				{"ip", "in", allIPs},
				{"extra.user_id", "=", s.Task.UserId},
				{"lastupdatetime", ">=", limitTime.Format("2006-01-02 15:04:05")},
			},
			[]es2.Sorter{es2.NewFieldSort("_id").Desc(), es2.NewFieldSort("ip").Desc()},
		)
		if err2 != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取subdomain数据失败: %v", err2)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - subdomain查询成功，返回数据量: %d", len(subdomainAll))
			for i, subdomain := range subdomainAll {
				if i < 3 { // 只打印前3条数据作为示例
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - subdomain[%d]: IP=%s, Domain=%s, UpdateTime=%s", i, subdomain.Ip, subdomain.Domain, subdomain.LastUpdateTime)
				}
			}
		}

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 开始查询service，查询条件IP: %v, user_id: %d, limitTime: %s", allIPs, s.Task.UserId, limitTime.Format("2006-01-02 15:04:05"))
		serviceAll, err2 = elastic.AllByParams[fofaee_service.FofaeeService](
			1000,
			[][]interface{}{
				{"ip", "in", allIPs},
				{"extra.user_id", "=", s.Task.UserId},
				{"lastupdatetime", ">=", limitTime.Format("2006-01-02 15:04:05")},
			},
			[]es2.Sorter{es2.NewFieldSort("_id").Desc(), es2.NewFieldSort("ip").Desc()},
		)
		if err2 != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取service数据失败: %v", err2)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - service查询成功，返回数据量: %d", len(serviceAll))
			for i, service := range serviceAll {
				if i < 3 { // 只打印前3条数据作为示例
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - service[%d]: IP=%s, Port=%d, Protocol=%s, UpdateTime=%s", i, service.IP, service.Port, service.Protocol, service.Lastupdatetime)
				}
			}
		}

		// 按IP分组
		subdomainByIP := make(map[string][]*fofaee_subdomain.FofeeSubdomain)
		for _, subdomain := range subdomainAll {
			subdomainByIP[subdomain.Ip] = append(subdomainByIP[subdomain.Ip], subdomain)
		}

		serviceByIP := make(map[string][]*fofaee_service.FofaeeService)
		for _, service := range serviceAll {
			serviceByIP[service.IP] = append(serviceByIP[service.IP], service)
		}

		// 重新处理subdomainByIP和serviceByIP，使用补全的IPv6地址作为key
		// 因为subdomain和service索引中的IPv6没有补全，需要重新映射
		newSubdomainByIP := make(map[string][]*fofaee_subdomain.FofeeSubdomain)
		for originalIP, subdomains := range subdomainByIP {
			completeIP := s.completeIPV6(originalIP)
			log.WithContextInfof(ctx, "[DEBUG] IPv6补全处理 - subdomain原始IP: %s, 补全后IP: %s, subdomain数量: %d", originalIP, completeIP, len(subdomains))
			newSubdomainByIP[completeIP] = subdomains
		}
		subdomainByIP = newSubdomainByIP

		newServiceByIP := make(map[string][]*fofaee_service.FofaeeService)
		for originalIP, services := range serviceByIP {
			completeIP := s.completeIPV6(originalIP)
			newServiceByIP[completeIP] = services
		}
		serviceByIP = newServiceByIP

		// 写入Ip+Port数据
		for ip, ports := range ipAndPorts {

			st, err := mysql.NewDSL[scan_task.ScanTasks]().FindByID(uint64(s.Task.ID))
			if err != nil {
				log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取scan_task失败,task_id:%d,ip:%s,err:%v", s.Task.ID, ip, err)
				continue
			}

			progress = float64(current)/float64(total)*25 + 50
			crawlerProgress := float64(current)/float64(total)*25 + 75
			current++

			// 推荐任务,默认数据端口扫描
			currentTaskPorts := taskPorts
			if len(probePorts) > 0 {
				if probeInfo, exists := probePorts[ip]; exists {
					currentTaskPorts = []int{}
					for _, info := range probeInfo {
						currentTaskPorts = append(currentTaskPorts, info.Port)
					}
					if len(currentTaskPorts) == 0 {
						// 使用ports字段中的端口
						for _, port := range ports {
							if portInt, ok := port.(int); ok {
								currentTaskPorts = append(currentTaskPorts, portInt)
							}
						}
					}
				}
			}

			if len(currentTaskPorts) == 0 && !isAllPort {
				log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]微内核-资产扫描，端口为空: %s", ip)
				continue
			}

			// 处理用户资产
			subdomains := subdomainByIP[ip]
			services := serviceByIP[ip]
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 第841行：开始处理IP资产，IP: %s, subdomains数量: %d, services数量: %d", ip, len(subdomains), len(services))
			allAssets, ipByPortService := s.getSubdomainAndServiceResult(subdomains, services, currentTaskPorts, ip, isAllPort)

			// 写入Ip+Port数据
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 第844行：开始写入IP+Port数据，IP: %s, allAssets数量: %d, ports: %v", ip, len(allAssets), ports)
			s.writeIpAndPortAsset(ctx, allAssets, total, crawlerProgress, ipByPortService, currentTaskPorts, ip, ports, isAllPort)

			if progress >= st.Progress {
				if progress > 87.5 && progress <= 89 {
					progress = 89
				} else if progress > 89 && progress <= 91 {
					progress = 91
				} else if progress > 91 && progress <= 93 {
					progress = 93
				} else if progress > 93 && progress <= 96 {
					progress = 96
				} else if progress >= 100 {
					progress = 99.9
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]当前任务进度超了100了啊，task_id=%d", st.ID)
				}

				// 更新任务进度
				_, err = mysql.NewDSL[scan_task.ScanTasks]().UpdateByID(
					uint64(s.Task.ID),
					map[string]interface{}{
						"progress":    progress,
						"use_seconds": time.Now().Unix() - s.StartAt.Unix(),
					},
				)
				if err == nil {
					s.Task.Progress = progress
					target = s.completeIPV6(ip)
					message = ""
					s.sendProgress(&s.Task, &progress, &target, &message)
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-资产扫描，更新扫描进度:TaskId:%d，进度:%f", s.Task.ID, progress)
				}
			}

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]NotScanIp，handle Task IP:%s，ports:%v", ip, ports)
		}

		time.Sleep(1 * time.Second)

		// 更新规则信息
		for ip, ports := range ipAndPorts {
			if !s.taskExists(ctx) {
				return false
			}

			// 推荐任务,默认数据端口扫描
			currentTaskPorts := taskPorts
			if len(probePorts) > 0 {
				if probeInfo, exists := probePorts[ip]; exists {
					currentTaskPorts = []int{}
					for _, info := range probeInfo {
						currentTaskPorts = append(currentTaskPorts, info.Port)
					}
					if len(currentTaskPorts) == 0 {
						// 使用ports字段中的端口
						for _, port := range ports {
							if portInt, ok := port.(int); ok {
								currentTaskPorts = append(currentTaskPorts, portInt)
							}
						}
					}
				}
			}

			if len(currentTaskPorts) == 0 && !isAllPort {
				log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]微内核-资产扫描，端口为空-更新规则信息: %s", ip)
				continue
			}

			// 处理用户资产
			subdomains := subdomainByIP[ip]
			services := serviceByIP[ip]
			allAssets, _ := s.getSubdomainAndServiceResult(subdomains, services, currentTaskPorts, ip, isAllPort)
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]allAssets: %v", allAssets)
			if len(allAssets) == 0 {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]allAssets为空，task_id:%d,ip:%s", s.Task.ID, ip)
				continue
			}

			// 更新任务资产的Rule规则和IPV6补全
			portInts := make([]int, 0, len(ports))
			for _, port := range ports {
				if portInt, ok := port.(int); ok {
					portInts = append(portInts, portInt)
				}
			}
			s.updateTaskAssetRules(ctx, ip, allAssets, portInts)
		}

		pageNum++
		if len(taskAssets) < pageSize {
			break
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-打印时间，tid:%d，start_time:%d，setp:微内核第六步打印时间，now:%d，cost:%d",
		s.Task.ID, s.StartAt.Unix(), time.Now().Unix(), time.Now().Unix()-s.StartAt.Unix())

	// 等待爬虫完成工作
	redisKey := fmt.Sprintf("crawler:%d:wait", s.Task.ID)
	for {
		exists, err := redis.GetClient().Exists(context.Background(), redisKey).Result()
		if err != nil || exists == 0 {
			break
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]我在等待爬虫完成工作------------------------->%d", s.Task.ID)
		time.Sleep(2 * time.Second)
	}

	if !s.taskExists(ctx) {
		s.clearFailedAssets(ctx)
		return false
	}

	//
	es.GetInstance().Refresh(new(foradar_assets.ForadarAsset).IndexName()).Do(ctx)
	time.Sleep(5 * time.Second)

	// 生成ip维度的数据
	s.genIpAssets(ctx, uint64(s.Task.ID))

	// 更新任务完成状态（对应PHP的完整任务状态更新逻辑）
	// PHP原文：Task::query()->where('id', $this->task->id)->update([...])
	taskAssetCount, err := elastic.CountByParams[fofaee_task_assets.FofaeeTaskAssets]([][]interface{}{
		{"task_id", "=", s.Task.ID},
	})
	if err != nil {
		log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取任务资产数量失败: %v", err)
		taskAssetCount = 0
	}

	ruleCount := s.getRuleCount(ctx)

	_, err = mysql.NewDSL[scan_task.ScanTasks]().UpdateByID(
		uint64(s.Task.ID),
		map[string]interface{}{
			"end_at":      time.Now(),
			"status":      scan_task.StatusFinished,
			"step":        scan_task.StepFinished,
			"progress":    100.00,
			"use_seconds": time.Now().Unix() - s.StartAt.Unix(),
			"asset_num":   taskAssetCount,
			"rule_num":    ruleCount,
		},
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新任务完成状态失败: %v", err)
		return false
	}

	// 更新DetectTask统计信息（对应PHP: 计算sure_ip_num, unsure_ip_num, threaten_ip_num）
	if s.DetectTask != nil && s.DetectTask.ID > 0 {
		// 获取已确认资产数量
		sureCount, err := elastic.CountByParams[fofaee_assets.FofaeeAssets]([][]interface{}{
			{"user_id", "=", s.Task.UserId},
			{"task_id", "=", s.Task.ID},
			{"status", "=", fofaee_assets.STATUS_CLAIMED},
		})
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取已确认资产数量失败: %v", err)
			sureCount = 0
		}

		// 获取疑似资产数量
		unsureCount, err := elastic.CountByParams[fofaee_assets.FofaeeAssets]([][]interface{}{
			{"user_id", "=", s.Task.UserId},
			{"task_id", "=", s.Task.ID},
			{"status", "=", fofaee_assets.STATUS_DEFAULT},
		})
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取疑似资产数量失败: %v", err)
			unsureCount = 0
		}

		// 获取风险资产数量
		threatenCount, err := elastic.CountByParams[fofaee_assets.FofaeeAssets]([][]interface{}{
			{"user_id", "=", s.Task.UserId},
			{"task_id", "=", s.Task.ID},
			{"status", "=", fofaee_assets.STATUS_THREATEN},
		})
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取风险资产数量失败: %v", err)
			threatenCount = 0
		}

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]sure_ip_num------------------------->%d, unsure_ip_num: %d, threaten_ip_num: %d", sureCount, unsureCount, threatenCount)

		// 更新DetectTask统计信息
		_, err = mysql.NewDSL[detect_assets_tasks.DetectAssetsTask]().UpdateByID(
			uint64(s.DetectTask.ID),
			map[string]interface{}{
				"sure_ip_num":     sureCount,
				"unsure_ip_num":   unsureCount,
				"threaten_ip_num": threatenCount,
			},
		)
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]更新DetectTask统计信息失败: %v", err)
		}
	}

	// 处理之前ip在线，然后扫描完成以后，有些ip已经离线了，但是本地扫描的任务不会补充结果到task_assets数据，导致ip不会把原来的在线状态改为离线状态
	// 对应PHP: if ($this->task->asset_type == \App\Models\MySql\Task::TASK_ASSET_MANUAL) { UpdateIpAndPortOfflineState::dispatchSync($this->task->user_id, $this->task); }
	if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]处理本地扫描任务的离线状态更新开始-task：task_id=%d", s.Task.ID)
		// 虽然PHP版本使用dispatchSync同步处理，但Go版本中已经有完整的UpdateIpAndPortOfflineStateJob实现
		// 为了保持一致性，这里直接调用已有的处理函数，而不是分发到队列
		payload := asyncq.UpdateIpAndPortOfflineStateJobPayload{
			UserId: int64(s.Task.UserId),
			TaskId: uint64(s.Task.ID),
			Task:   &s.Task,
		}

		// 直接调用处理函数，等效于PHP的dispatchSync同步处理
		t := asyncq.Task{} // 创建空的task对象
		payloadBytes, _ := json.Marshal(payload)
		t.Payload = string(payloadBytes)

		err := UpdateIpAndPortOfflineStateJob(ctx, &t)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]UpdateIpAndPortOfflineState处理失败: %v, task_id=%d, user_id=%d", err, s.Task.ID, s.Task.UserId)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]处理本地扫描任务的离线状态更新完成-task：task_id=%d", s.Task.ID)
		}
	}

	progress = 100.0
	target = ""
	message = "扫描任务执行完成"
	s.sendProgress(&s.Task, &progress, &target, &message)
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]扫描任务完成啦------------------------->%d", s.Task.ID)
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]TaskId:%d", s.Task.ID)
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]扫描任务完成啦------------------------->%d", s.Task.ID)
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-打印时间，tid:%d，start_time:%d，setp:微内核最后一步打印时间，now:%d，cost:%d",
		s.Task.ID, s.StartAt.Unix(), time.Now().Unix(), time.Now().Unix()-s.StartAt.Unix())

	if !s.taskExists(ctx) {
		s.clearFailedAssets(ctx)
		return false
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]自动提取线索任务下发开始，扫描任务id：%d", s.Task.ID)
	search := map[string]interface{}{
		"user_id": s.Task.UserId,
		"status":  foradar_assets.STATUS_CLAIMED, // ForadarAssets::STATUS_CLAIMED
		"is_all":  1,
		"task_id": s.Task.ID,
	}

	var deteId uint64 = 0
	var groupId uint64 = 0
	if s.DetectTask != nil {
		deteId = uint64(s.DetectTask.ID)
		// 获取group_id
		if s.DetectTask.ID > 0 {
			detectTask, err := mysql.NewDSL[detect_assets_tasks.DetectAssetsTask]().FindByID(uint64(s.DetectTask.ID))
			if err == nil {
				groupId = uint64(detectTask.GroupId)
			}
		}
	}

	// 判断是否需要自动提取线索
	if s.Task.Flag == "" || (s.DetectTask != nil && s.DetectTask.ID > 0 && s.DetectTask.ExpandSource == detect_assets_tasks.ExpandSourceDetect) {
		// 自动提取线索
		s.dispatchExtractAssetCluesJob(ctx, uint64(s.Task.UserId), search, deteId, groupId)
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]自动提取线索任务下发完成，扫描任务id：%d", s.Task.ID)
	}

	// 统计线索已认领资产数量
	s.dispatchCountClueAssetTotalJob(ctx, uint64(s.Task.UserId))

	// 将ip+端口维度的数据，如果一个ip上某个端口有企业名称的话，那么该ip下所有端口都归属于这个公司
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]genIpAssets，更新ip端口数据维度的企业名称开始：task_id=%d，user_id=%d", s.Task.ID, s.Task.UserId)
	s.dispatchUpdateIpAndPortAssetsCompanyNameJob(ctx, uint64(s.Task.UserId), uint64(s.Task.ID), uint64(s.Task.CompanyId))
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]genIpAssets，更新ip端口数据维度的企业名称结束：task_id=%d，user_id=%d", s.Task.ID, s.Task.UserId)

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]自动更新风险ip下发任务开始，扫描任务id：%d", s.Task.ID)
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]自动更新风险ip下发完成，扫描任务id：%d", s.Task.ID)

	// 推荐资产来的扫描任务不进行自动计算风险规则资产
	if s.Task.Flag == "" {
		// 计算风险规则资产
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]genIpAssets，计算风险事件资产开始-task：task_id=%d", s.Task.ID)
		s.dispatchUpdateRiskTypeAssetsJob(ctx, uint64(s.Task.UserId), uint64(s.Task.ID), uint64(s.Task.CompanyId), 0)
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]genIpAssets，计算风险事件资产结束-task：task_id=%d", s.Task.ID)
	}

	if s.DetectTask != nil && s.DetectTask.ID > 0 && s.DetectTask.IsCheckRisk > 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]genIpAssets，单位测绘自动-计算风险事件资产开始-task：task_id=%d，detect_task_id=%d", s.Task.ID, s.DetectTask.ID)
		s.dispatchUpdateRiskTypeAssetsJob(ctx, uint64(s.Task.UserId), uint64(s.Task.ID), uint64(s.Task.CompanyId), uint64(s.DetectTask.ID))
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]genIpAssets，单位测绘自动-计算风险事件资产结束-task：task_id=%d，detect_task_id=%d", s.Task.ID, s.DetectTask.ID)
	}

	// 识别登录入口，统计登录入口的数据，写到mysql
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]StatisticsLoginAssetsJob，统计登录入口数据开始-task：task_id=%d", s.Task.ID)
	s.dispatchStatisticsLoginAssetsJob(ctx, uint64(s.Task.UserId), uint64(s.Task.ID))
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]StatisticsLoginAssetsJob，统计登录入口数据结束-task：task_id=%d", s.Task.ID)

	// 标记资产tags字段
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]TagAssetsJob，标记tag开始-task：task_id=%d", s.Task.ID)
	s.dispatchTagAssetsJob(ctx, uint64(s.Task.UserId), uint64(s.Task.ID))
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]TagAssetsJob，标记tag结束-task：task_id=%d", s.Task.ID)

	// 处理同ip和端口一样情况下，ip端口维度数据的online_state状态不一致问题
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]TagAssetsJob，ip端口维度数据的online_state状态不一致问题-task：task_id=%d", s.Task.ID)
	//s.dispatchUpdateIpAndPortOnlineStateJob(ctx, uint64(s.Task.UserId), uint64(s.Task.ID))
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]TagAssetsJob，ip端口维度数据的online_state状态不一致问题-task：task_id=%d", s.Task.ID)

	// 缓存台账和疑似资产的高级筛选条件
	s.dispatchCacheTableIpsConditionJob(ctx, uint64(s.Task.UserId))

	//域名同步逻辑
	// 对应PHP: TableAssetsDoaminsSync::dispatch($this->task->user_id, $this->task->id, DomainAssets::TABLE_DOMAIN, null, null, null, null, $this->task->detect_assets_tasks_id ?? null,$this->task->organization_discover_task_id ?? null);
	log.Infof("ScanForadarAssetHandler: 台账域名数据同步开始 - task_id=%d", s.Task.ID)

	// 获取organization_discover_task_id
	var organizationDiscoverTaskId uint64
	if s.OrganDetectTask != nil && s.OrganDetectTask.ID > 0 {
		organizationDiscoverTaskId = uint64(s.OrganDetectTask.ID)
	}
	// 将台账的域名同步到domain_assets表中
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]TableAssetsDoaminsSync，台账域名数据同步开始-task：task_id=%d", s.Task.ID)

	detectAssetsTasksId := uint64(0)
	if s.Task.DetectAssetsTasksId > 0 {
		detectAssetsTasksId = uint64(s.Task.DetectAssetsTasksId)
	}
	log.Infof("ScanForadarAssetHandler: 准备下发PHP域名同步任务 - user_id=%d, task_id=%d, detect_task_id=%d, org_task_id=%d",
		s.Task.UserId, s.Task.ID, detectAssetsTasksId, organizationDiscoverTaskId)

	taskId := uint64(s.Task.ID)
	err = asyncq.TableAssetsDoaminsSyncPhpJob.Dispatch(
		uint64(s.Task.UserId),      // user_id ($this->task->user_id)
		taskId,                     // task_id ($this->task->id)
		domain_assets.TABLE_DOMAIN, // from (DomainAssets::TABLE_DOMAIN = 1)
		nil,                        // groupId (null)
		nil,                        // domain_task_id (null)
		nil,                        // import_domains (null)
		nil,                        // flag (null)
		detectAssetsTasksId,        // detect_task_id ($this->task->detect_assets_tasks_id ?? null)
		organizationDiscoverTaskId, // organization_discover_task_id ($this->task->organization_discover_task_id ?? null)
	)
	if err != nil {
		log.Errorf("ScanForadarAssetHandler: 下发PHP域名同步任务失败 - user_id=%d, task_id=%d, error=%v",
			s.Task.UserId, s.Task.ID, err)
	} else {
		log.Infof("ScanForadarAssetHandler: 下发PHP域名同步任务成功 - user_id=%d, task_id=%d",
			s.Task.UserId, s.Task.ID)
	}
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]TableAssetsDoaminsSync，台账域名数据同步结束-task：task_id=%d", s.Task.ID)

	// 异步处理哪些资产应该标记影子资产标签
	if s.DetectTask != nil && s.DetectTask.ID > 0 && s.DetectTask.ExpandSource == detect_assets_tasks.ExpandFromDetect {
		sureIps := s.DetectTask.ImportSureIps
		if sureIps != "" {
			var sureIpsArr []string
			err := json.Unmarshal([]byte(sureIps), &sureIpsArr)
			if err == nil && len(sureIpsArr) > 0 {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ShadowAssets，异步处理哪些资产应该标记影子资产标签-task：task_id=%d，detect_task_id=%d", s.Task.ID, s.DetectTask.ID)
				s.dispatchShadowAssetsTagJob(ctx, uint64(s.Task.UserId), uint64(s.Task.ID), uint64(s.DetectTask.ID))
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ShadowAssets，异步处理哪些资产应该标记影子资产标签-task：task_id=%d，detect_task_id=%d", s.Task.ID, s.DetectTask.ID)
			}
		}
	}

	// 计算疑似资产新增的数量 - 对应PHP: $originUnserIps = IpAssets::query()...
	originUnsureIps, err := elastic.AllByParams[fofaee_assets.FofaeeAssets](
		1000,
		[][]interface{}{
			{"user_id", "=", s.Task.UserId},
			{"task_id", "!=", s.Task.ID},
			{"status", "=", fofaee_assets.STATUS_DEFAULT},
		},
		[]es2.Sorter{es2.NewFieldSort("id").Desc(), es2.NewFieldSort("ip").Desc()},
	)
	if err != nil {
		log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取原有疑似资产失败: %v", err)
	}

	nowUnsureIps, err := elastic.AllByParams[fofaee_assets.FofaeeAssets](
		1000,
		[][]interface{}{
			{"user_id", "=", s.Task.UserId},
			{"task_id", "=", s.Task.ID},
			{"status", "=", fofaee_assets.STATUS_DEFAULT},
		},
		[]es2.Sorter{es2.NewFieldSort("id").Desc(), es2.NewFieldSort("ip").Desc()},
	)
	if err != nil {
		log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取当前疑似资产失败: %v", err)
	}

	// 计算差集
	originIpMap := make(map[string]bool)
	for _, asset := range originUnsureIps {
		originIpMap[asset.Ip] = true
	}

	var addUnsureIps []string
	for _, asset := range nowUnsureIps {
		if !originIpMap[asset.Ip] {
			addUnsureIps = append(addUnsureIps, asset.Ip)
		}
	}

	// 把台账的资产里面url同步到业务系统里面(单位测绘和云端推荐)--本地扫描出来的url也自动同步到业务系统
	// 对应PHP: if ((($this->detectTask->id ?? null) && ($this->detectTask->is_auto_business_api ?? null)) || ($this->task->asset_type == Task::TASK_ASSET_MANUAL))
	if (s.DetectTask != nil && s.DetectTask.ID > 0 && s.DetectTask.IsAutoBusinessApi > 0) ||
		(s.Task.AssetType == scan_task.TASK_ASSET_MANUAL) {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]把台账的资产里面url同步到业务系统里面开始-task：task_id=%d，detect_task_id=%v", s.Task.ID, s.DetectTask)

		// 分发 BussinessAssets 任务 - 对应PHP: BussinessAssets::dispatch($this->task->user_id, $this->task->id, $this->task->company_id, ($this->detectTask->id ?? null));
		var detectTaskId uint64 = 0
		if s.DetectTask != nil && s.DetectTask.ID > 0 {
			detectTaskId = uint64(s.DetectTask.ID)
		}

		// 查看task_relate.go第158行的实现：err = asyncq.BussinessAssets.Dispatch(req.UserId, tasks, req.CompanyId, deteId, 0)
		// 但是这里的任务参数应该是单个任务ID，参考PHP原始逻辑：BussinessAssets::dispatch($this->task->user_id, $this->task->id, ...)
		err := asyncq.BussinessAssets.Dispatch(uint64(s.Task.UserId), []uint64{uint64(s.Task.ID)}, uint64(s.Task.CompanyId), detectTaskId, 1)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]把台账的资产里面url同步到业务系统失败: %v", err)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]把台账的资产里面url同步到业务系统里面结束-task：task_id=%d，detect_task_id=%d", s.Task.ID, detectTaskId)
		}
	}

	if len(addUnsureIps) > 0 {
		riskIpNum := len(addUnsureIps)
		contentData := map[string]interface{}{
			"content":    fmt.Sprintf("发现%d个疑似资产", riskIpNum),
			"realted_ip": addUnsureIps,
		}
		contentJSON, _ := json.Marshal(contentData)
		s.recordRiskEvent(1, string(contentJSON), int64(s.Task.UserId), int64(s.Task.CompanyId)) // Risk::TYPE_ASSET_INFO = 1
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-同步数据完成，任务ID: %d", s.Task.ID)
	return true
}

func (s *ScanForadarAsset) fillTaskIpByOffLine(ctx context.Context) []string {
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]开始填充任务IP，任务ID: %d, Flag: '%s', PortRange: %d", s.Task.ID, s.Task.Flag, s.Task.PortRange)

	// 检查任务flag - 对应PHP: if (!$this->task->flag) { return []; }
	if len(s.Task.Flag) == 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]任务没有标记，跳过填充任务IP，任务ID: %d", s.Task.ID)
		return []string{}
	}

	fillIps := make([]string, 0)
	var ips []string

	// 根据port_range判断IP来源 - 对应PHP逻辑
	if s.Task.PortRange == 1 {
		// 从probeInfos获取IP - 对应PHP: $this->task?->probeInfos?->pluck('ip')?->unique()?->values()?->toArray()

		db := mysql.GetDbClient()
		var probeInfos []task.TaskProbeInfo
		err := db.Table("task_probe_infos").Where("task_id = ?", s.Task.ID).Find(&probeInfos).Error
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取探测信息失败，任务ID: %d，错误: %v", s.Task.ID, err)
		} else {
			// 去重并收集IP
			ipMap := make(map[string]bool)
			for _, probeInfo := range probeInfos {
				// 检查IP字段的有效性
				if probeInfo.Ip.Valid && probeInfo.Ip.String != "" {
					ipMap[probeInfo.Ip.String] = true
				}
			}
			for ip := range ipMap {
				ips = append(ips, ip)
			}
		}
	} else {
		// 从task ips获取IP - 对应PHP: $this->task?->ips?->pluck('ip')?->toArray()
		taskIps, err := mysql.NewDSL[task.TaskIps]().QueryByParams([][]interface{}{
			{"task_id", "=", s.Task.ID},
		})
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取任务IP失败，任务ID: %d，错误: %v", s.Task.ID, err)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]从task_ips获取到IP数量: %d", len(taskIps))
			for _, taskIp := range taskIps {
				// 检查IP字段的有效性
				if taskIp.Ip.Valid && taskIp.Ip.String != "" {
					ips = append(ips, taskIp.Ip.String)
				}
			}
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]总共获取到IP数量: %d ，任务ID: %d", len(ips), s.Task.ID)

	// 分批处理IP（每批500个）- 对应PHP: collect($ips)->chunk(500)->each
	chunkSize := 500
	for i := 0; i < len(ips); i += chunkSize {
		end := i + chunkSize
		if end > len(ips) {
			end = len(ips)
		}
		itemIps := ips[i:end]

		// 构建RecommendResult查询条件
		queryParams := [][]interface{}{
			{"MUST", [][]interface{}{
				{"flag", "=", s.Task.Flag},
				{"ip", "in", itemIps},
				{"user_id", "=", s.Task.UserId},
			}},
		}

		// 如果有detectTask，添加task_id条件 - 对应PHP: ->when($this->detectTask ?? null, function ($q) { $q->where('task_id', $this->task->id); })
		//if s.DetectTask != nil {
		//	queryParams[0][1] = append(queryParams[0][1].([][]interface{}), []interface{}{"task_id", "=", s.Task.ID})
		//}
		// 如果有organDetectTask，添加task_id条件 - 对应PHP: ->when($this->organDetectTask ?? null, function ($q) { $q->where('task_id', $this->task->id); })
		//if s.OrganDetectTask != nil {
		//	queryParams[0][1] = append(queryParams[0][1].([][]interface{}), []interface{}{"task_id", "=", s.Task.ID})
		//}

		// 添加详细日志
		log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] 查询参数 - TaskID: %d, Flag: %s, IP数量: %d, DetectTask: %v, OrganDetectTask: %v",
			s.Task.ID, s.Task.Flag, len(itemIps), s.DetectTask != nil, s.OrganDetectTask != nil)
		log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] 当前批次IP列表: %v, TaskID: %d", itemIps, s.Task.ID)
		log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] 查询条件: %+v TaskID: %d", queryParams, s.Task.ID)

		// 查询RecommendResult中的IP
		recommendResults, err := elastic.AllByParams[recommend_result.RecommendResult](
			1000, // 大页面获取所有结果
			queryParams,
			[]es2.Sorter{es2.NewFieldSort("id").Desc(), es2.NewFieldSort("ip").Desc()},
		)
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]查询推荐结果失败，任务ID: %d，错误: %v", s.Task.ID, err)
			continue
		}

		log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] 查询结果 - 当前批次返回数量: %d , TaskID: %d", len(recommendResults), s.Task.ID)

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]查询推荐结果数量: %d, TaskID: %d，Flag: %s，IP批次大小: %d", len(recommendResults), s.Task.ID, s.Task.Flag, len(itemIps))

		// 获取推荐结果中的唯一IP
		recommendIpMap := make(map[string]bool)
		for _, result := range recommendResults {
			if result.Ip != "" {
				recommendIpMap[result.Ip] = true
			}
		}

		var recommendIps []string
		for ip := range recommendIpMap {
			recommendIps = append(recommendIps, ip)
		}

		// 查询TaskAsset中已存在的IP
		taskAssets, err := elastic.AllByParams[fofaee_task_assets.FofaeeTaskAssets](
			1000,
			[][]interface{}{
				{"task_id", "=", s.Task.ID},
			},
			[]es2.Sorter{es2.NewFieldSort("_id").Desc(), es2.NewFieldSort("ip").Desc()},
		)
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]查询任务资产失败，任务ID: %d，错误: %v", s.Task.ID, err)
			continue
		}

		// 获取已存在的IP
		existingIpMap := make(map[string]bool)
		for _, asset := range taskAssets {
			if asset.Ip != "" {
				existingIpMap[asset.Ip] = true
			}
		}

		// 计算差集：在推荐结果中但不在TaskAsset中的IP - 对应PHP: array_diff
		for _, ip := range recommendIps {
			if !existingIpMap[ip] {
				fillIps = append(fillIps, ip)
			}
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]待补充扫描任务的IP，taskId: %d，IP数量: %d", s.Task.ID, len(fillIps))

	// 分批处理需要填充的IP（每批500个）并创建TaskAsset记录
	for i := 0; i < len(fillIps); i += chunkSize {
		end := i + chunkSize
		if end > len(fillIps) {
			end = len(fillIps)
		}
		batchIps := fillIps[i:end]

		// 构建查询条件获取推荐数据
		queryParams := [][]interface{}{
			{"MUST", [][]interface{}{
				{"flag", "=", s.Task.Flag},
				{"ip", "in", batchIps},
				{"user_id", "=", s.Task.UserId},
			}},
		}
		recommends, err := elastic.AllByParams[recommend_result.RecommendResult](
			1000,
			queryParams,
			[]es2.Sorter{es2.NewFieldSort("id").Desc(), es2.NewFieldSort("ip").Desc()},
		)
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取推荐数据失败，任务ID: %d，错误: %v", s.Task.ID, err)
			continue
		}

		// 添加调试日志：打印获取到的推荐数据
		log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 获取推荐数据 - TaskID: %d, 推荐数据数量: %d", s.Task.ID, len(recommends))
		for idx, recommend := range recommends {
			if idx < 3 { // 只打印前3条数据样本
				recommendJson, _ := json.Marshal(recommend)
				log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 推荐数据样本[%d] - TaskID: %d, 数据: %s", idx, s.Task.ID, string(recommendJson))
			}
		}

		// 按IP分组 - 对应PHP: $ipGroup = $recommends->groupBy('ip')
		ipGroup := make(map[string][]recommend_result.RecommendResult)
		for _, recommend := range recommends {
			if recommend.Ip != "" {
				ipGroup[recommend.Ip] = append(ipGroup[recommend.Ip], *recommend)
			}
		}

		// 添加调试日志：打印IP分组信息
		log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] IP分组统计 - TaskID: %d, IP数量: %d", s.Task.ID, len(ipGroup))
		for ip, ipData := range ipGroup {
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] IP分组详情 - TaskID: %d, IP: %s, 该IP下数据条数: %d", s.Task.ID, ip, len(ipData))
			if len(ipData) > 0 {
				// 打印第一条数据作为样本
				firstDataJson, _ := json.Marshal(ipData[0])
				log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] IP分组样本数据 - TaskID: %d, IP: %s, 样本: %s", s.Task.ID, ip, string(firstDataJson))
			}
		}

		// 处理每个IP（不是每个推荐结果）- 对应PHP: foreach ($ipGroup as $ip => $ipInfo)
		// 虽然PHP代码看起来是foreach ($recommends as $recommend)，但实际效果是每个IP只处理一次
		// 因为同一个IP的多次插入会覆盖，最终每个IP只有一条记录
		processedIps := make(map[string]bool)
		log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 开始处理推荐数据 - TaskID: %d, 推荐数据数量: %d", s.Task.ID, len(recommends))

		for _, recommend := range recommends {
			if recommend.Ip == "" {
				log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]推荐资产IP数据为空，taskId: %d", s.Task.ID)
				continue
			}

			// 如果这个IP已经处理过了，跳过
			if processedIps[recommend.Ip] {
				log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] IP已处理，跳过 - TaskID: %d, IP: %s", s.Task.ID, recommend.Ip)
				continue
			}
			processedIps[recommend.Ip] = true

			// 获取该IP的所有端口信息 - 对应PHP: $ipInfo = $ipGroup->get($recommend->ip)
			ipInfo := ipGroup[recommend.Ip]
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 获取IP信息 - TaskID: %d, IP: %s, ipInfo数量: %d", s.Task.ID, recommend.Ip, len(ipInfo))

			// 按端口分组 - 对应PHP: $ports = $ipInfo->keyBy('port')
			portMap := make(map[string]recommend_result.RecommendResult)
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 开始端口分组 - TaskID: %d, IP: %s, ipInfo数量: %d", s.Task.ID, recommend.Ip, len(ipInfo))

			for i, info := range ipInfo {
				portStr := cast.ToString(info.Port)
				log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 处理端口[%d] - TaskID: %d, IP: %s, 原始Port: %v(%T), 转换后: %s, Protocol: %s",
					i, s.Task.ID, recommend.Ip, info.Port, info.Port, portStr, info.Protocol)

				if portStr != "" && portStr != "0" {
					portMap[portStr] = info
					log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 端口添加成功 - TaskID: %d, IP: %s, Port: %s", s.Task.ID, recommend.Ip, portStr)
				} else {
					log.WithContextWarnf(ctx, "[DEBUG_TASK_ASSET] 端口跳过 - TaskID: %d, IP: %s, 原始Port: %v, 转换后: %s", s.Task.ID, recommend.Ip, info.Port, portStr)
				}
			}

			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 端口分组完成 - TaskID: %d, IP: %s, portMap数量: %d", s.Task.ID, recommend.Ip, len(portMap))

			// 构建端口和协议列表 - 对应PHP: $ports->keys()->toArray() 和 $ports->pluck('protocol')->toArray()
			var ports []interface{}
			var protocols []string
			protocolMap := make(map[string]bool)

			for portStr := range portMap {
				ports = append(ports, portStr)
				if protocol := portMap[portStr].Protocol; protocol != "" && !protocolMap[protocol] {
					protocols = append(protocols, protocol)
					protocolMap[protocol] = true
				}
			}

			// 添加调试日志：检查端口和协议数据
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 端口协议数据 - TaskID: %d, IP: %s, ports: %v, protocols: %v", s.Task.ID, recommend.Ip, ports, protocols)

			// 添加调试日志：打印原始 ipInfo 数据
			ipInfoJson, _ := json.Marshal(ipInfo)
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 原始ipInfo数据 - TaskID: %d, IP: %s, ipInfo数量: %d, 数据: %s", s.Task.ID, recommend.Ip, len(ipInfo), string(ipInfoJson))

			// 构建title_list - 对应PHP: $ports->map(function ($item) { return ['port' => $item->port ?? null,'host' => $item->url ?? $item->ip,'title' => $item->title ?? '']; })
			var titleList []map[string]interface{}
			for _, info := range ipInfo {
				host := info.Url
				if host == "" {
					host = info.Ip
				}
				titleList = append(titleList, map[string]interface{}{
					"port":  info.Port,
					"host":  host,
					"title": info.Title,
				})
			}

			// 构建port_list - 严格对应PHP: $ports->map(function ($item) { return ['port' => $item->port ?? null,'protocol' => $item->protocol ?? '','banner' => '','certs' => null]; })
			var portList []map[string]interface{}
			for _, info := range ipInfo {
				portList = append(portList, map[string]interface{}{
					"port":     info.Port,     // 对应PHP: 'port' => $item->port ?? null
					"protocol": info.Protocol, // 对应PHP: 'protocol' => $item->protocol ?? ''
					"banner":   "",            // 对应PHP: 'banner' => ''
					"certs":    nil,           // 对应PHP: 'certs' => null
				})
			}

			// 添加调试日志：打印构建后的数据
			titleListJson, _ := json.Marshal(titleList)
			portListJson, _ := json.Marshal(portList)
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 构建后数据 - TaskID: %d, IP: %s, titleList: %s", s.Task.ID, recommend.Ip, string(titleListJson))
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 构建后数据 - TaskID: %d, IP: %s, portList: %s", s.Task.ID, recommend.Ip, string(portListJson))

			// 检查是否为IPv6 - 对应PHP: (bool) filter_var($recommend->ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)
			isIpv6 := strings.Contains(recommend.Ip, ":")

			// 构建TaskAsset记录 - 严格对应PHP: $record = [...]
			now := time.Now().Format("2006-01-02 15:04:05")

			// 使用 map 结构来避免字段数量限制问题
			record := map[string]interface{}{
				"id":             fmt.Sprintf("%d_%s", s.Task.ID, recommend.Ip),
				"task_id":        s.Task.ID,
				"user_id":        s.Task.UserId,
				"company_id":     uint64(s.Task.CompanyId),
				"createtime":     now,
				"lastupdatetime": now,
				"updated_at":     now,
				"ip":             recommend.Ip,
				"port_size":      uint(len(ports)),
				"ports":          ports,                      // 对应PHP: 'ports' => $ports->keys()->toArray()
				"protocols":      protocols,                  // 对应PHP: 'protocols' => $ports->pluck('protocol')->toArray()
				"is_ipv6":        isIpv6,                     // 对应PHP: 'is_ipv6' => (bool) filter_var($recommend->ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)
				"state":          0,                          // 对应PHP: 'state' => 0
				"online_state":   0,                          // 对应PHP: 'online_state' => 0
				"title_list":     titleList,                  // 对应PHP: 'title_list' => $ports->map(...)
				"port_list":      portList,                   // 对应PHP: 'port_list' => $ports->map(...)
				"rules":          []map[string]interface{}{}, // 对应PHP: 'rules' => []
				"is_php_fill":    1,                          // 对应PHP: 'is_php_fill'=>1
				"hosts":          nil,                        // 对应PHP: 'hosts' => null
			}

			// 添加详细的数据日志用于调试字段数量问题
			recordJson, _ := json.Marshal(record)
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 准备插入TaskAsset数据 - TaskID: %d, IP: %s, 数据: %s", s.Task.ID, recommend.Ip, string(recordJson))

			// 统计各个字段的数量
			log.WithContextInfof(ctx, "[DEBUG_TASK_ASSET] 字段统计 - TaskID: %d, IP: %s, TitleList长度: %d, PortList长度: %d, Ports长度: %d, Protocols长度: %d, Rules长度: %d",
				s.Task.ID, recommend.Ip, len(titleList), len(portList), len(ports), len(protocols), len(record["rules"].([]map[string]interface{})))

			// 插入TaskAsset记录 - 对应PHP: TaskAsset::query()->insert([$record])
			client := es.GetInstance()
			_, err = client.Index().
				Index("fofaee_task_assets").
				Type("ips").
				Id(record["id"].(string)).
				BodyJson(record).
				Refresh("true").
				Do(context.Background())
			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]插入TaskAsset失败，任务ID: %d，IP: %s，错误: %v", s.Task.ID, recommend.Ip, err)
			} else {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]从推荐数据中补充任务IP，task_id: %d，ip: %s，端口数: %d", s.Task.ID, recommend.Ip, len(ports))
			}
		}
	}

	// 添加总计日志
	log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] 批量查询完成 - TaskID: %d, Flag: %s, 总IP数量: %d, 最终填充IP数量: %d",
		s.Task.ID, s.Task.Flag, len(ips), len(fillIps))

	// 刷新索引 - 对应PHP: TaskAsset::query()->refresh()
	es.GetInstance().Refresh(new(fofaee_task_assets.FofaeeTaskAssets).IndexName()).Do(ctx)

	return fillIps
}

// getTaskScanPortInfo 获取任务扫描端口信息
// isQudiaoForbiddenPort: false=默认获取所有端口，不区分禁用不禁用; true=区分禁用端口，只获取真正扫描的端口
func (s *ScanForadarAsset) getTaskScanPortInfo(isQudiaoForbiddenPort ...bool) ([]int, bool, map[string][]task.TaskProbeInfo) {
	ctx := context.Background()

	// 处理可选参数
	excludeForbidden := false
	if len(isQudiaoForbiddenPort) > 0 {
		excludeForbidden = isQudiaoForbiddenPort[0]
	}

	// 区分是否自定义端口扫描
	var taskPorts []int
	if s.Task.IsDefinePort == 1 {
		// 获取自定义端口 - 对应PHP: DefinePort::query()->where('task_id', $this->task->id)->pluck('port')->toArray()
		definePorts, err := mysql.NewDSL[port_group.DefinePort]().QueryByParams([][]interface{}{
			{
				"task_id",
				"=",
				s.Task.ID,
			},
		})
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取自定义端口失败，任务ID: %d，错误: %v", s.Task.ID, err)
			return nil, false, nil
		}
		// 去重并转换为int类型
		portMap := make(map[int]bool)
		for _, port := range definePorts {
			portMap[port.Port] = true
		}
		for port := range portMap {
			taskPorts = append(taskPorts, port)
		}

		// 如果是0-65535个自定义端口扫描，那么扫描类型必须是mascan，nmap不支持
		// 对应PHP: if (($taskPorts[0] == \App\Models\MySql\Task::ALL_PORT_DEFINE_PORT) && count($taskPorts) == 1)
		if len(taskPorts) == 1 && taskPorts[0] == 65535 {
			probePorts := s.getProbePortsGroupByIP()
			return []int{}, true, probePorts
		}

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]获取自定义端口成功，任务ID: %d，端口数量: %d", s.Task.ID, len(taskPorts))
	} else {
		if !excludeForbidden {
			// 获取任务端口 - 对应PHP: Task::query()->where('id', $this->task->id)->getAllPort()->pluck('port')->toArray()
			scanTaskModel := scan_task.NewScanTasksModel()
			_, portValues, err := scanTaskModel.GetAllPorts(uint64(s.Task.ID))
			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取任务端口失败，任务ID: %d，错误: %v", s.Task.ID, err)
				return nil, false, nil
			}

			// 转换为int类型
			for _, port := range portValues {
				taskPorts = append(taskPorts, int(port))
			}
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]获取任务端口成功，任务ID: %d，端口数量: %d", s.Task.ID, len(taskPorts))
		} else {
			// 对应PHP: $taskPorts = Task::query()->where('id', $this->task->id)->getAllPort()->pluck('port')->unique()->values()->toArray();
			scanTaskModel := scan_task.NewScanTasksModel()
			_, portValues, err := scanTaskModel.GetAllPorts(uint64(s.Task.ID))
			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取任务端口失败，任务ID: %d，错误: %v", s.Task.ID, err)
				return nil, false, nil
			}

			// 转换为int类型
			for _, port := range portValues {
				taskPorts = append(taskPorts, int(port))
			}

			// 判断当前是否存在禁用的端口 - 对应PHP: if ($taskPorts) {
			if len(taskPorts) > 0 {
				var allRealPort []int

				// 判断端口是否存在禁用的,如果存在的话,就处理,不存在不需要处理
				// 对应PHP: $hasForbiddenPort = Port::query()->where('user_id', $this->task->user_id)->where('status', Port::STATUS_DISABLE)->first();
				hasForbiddenPort, err := mysql.NewDSL[port_group.Port]().QueryByParams([][]interface{}{
					{"user_id", "=", s.Task.UserId},
					{"status", "=", port_group.StatusDisable},
				})
				if err != nil {
					log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]查询禁用端口失败，任务ID: %d，错误: %v", s.Task.ID, err)
				} else if len(hasForbiddenPort) > 0 {
					// 扫描端口里面去掉禁用的端口
					// 对应PHP: $taskPortInfo = TaskPorts::query()->where('task_id', $this->task->id)->first();
					taskPortInfo, err := mysql.NewDSL[task.TaskPorts]().QueryByParams([][]interface{}{
						{"task_id", "=", s.Task.ID},
					})
					if err != nil {
						log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取任务端口配置失败，任务ID: %d，错误: %v", s.Task.ID, err)
					} else if len(taskPortInfo) > 0 {
						portsId := taskPortInfo[0].PortsId

						// 对应PHP: $allRealPortIdArr = PortPortGroup::query()->where('port_group_id', $taskPortInfo['ports_id'])->pluck('port_id')->toArray();
						allRealPortIdArr, err := mysql.NewDSL[port_group.PortPortGroup]().QueryByParams([][]interface{}{
							{"port_group_id", "=", portsId},
						})
						if err != nil {
							log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取端口组关联失败，任务ID: %d，端口组ID: %d，错误: %v", s.Task.ID, portsId, err)
						} else if len(allRealPortIdArr) > 0 {
							// 提取端口ID
							var portIds []interface{}
							for _, item := range allRealPortIdArr {
								portIds = append(portIds, item.PortId)
							}

							// 对应PHP: $allRealPort = Port::query()->whereIn('id', $allRealPortIdArr)->where('status', Port::STATUS_DEFAULT)->pluck('port')->toArray();
							allRealPortData, err := mysql.NewDSL[port_group.Port]().QueryByParams([][]interface{}{
								{"id", "in", portIds},
								{"status", "=", port_group.StatusDefault},
							})
							if err != nil {
								log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取有效端口失败，任务ID: %d，错误: %v", s.Task.ID, err)
							} else {
								for _, port := range allRealPortData {
									allRealPort = append(allRealPort, int(port.Port))
								}

								if len(allRealPort) > 0 {
									log.WithContextInfof(ctx, "[ScanForadarAssetHandler]当前用户存在禁用端口,直接按照真正扫描的端口作为判断依据，user_id: %d，task_id: %d", s.Task.UserId, s.Task.ID)
									taskPorts = allRealPort
								}
							}
						}
					}
				} else {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]当前用户不存在禁用端口，取默认的端口分组的端口数据，user_id: %d，task_id: %d", s.Task.UserId, s.Task.ID)
				}
			}
		}
	}

	// 检查是否为全端口扫描 - 对应PHP逻辑
	isAllPort := false
	if len(taskPorts) == 0 {
		// 获取端口组ID - 对应PHP: object_get(TaskPorts::query()->where('task_id', $this->task->id)->first(), 'ports_id')
		taskPortsModel := task.NewTaskPortsModel()
		taskPortsInfo, err := taskPortsModel.FindByTaskID(uint64(s.Task.ID))
		if err != nil {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取任务端口配置失败，任务ID: %d，错误: %v", s.Task.ID, err)
		} else if taskPortsInfo != nil {
			groupId := taskPortsInfo.PortsId
			// 检查是否为全端口扫描 - 对应PHP: PortGroup::query()->where('id', $groupId)->isAllPort()
			portGroupModel := port_group.NewPortGroupModel()
			portGroup, err := portGroupModel.FindById(groupId)
			if err != nil {
				log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取端口组失败，任务ID: %d，端口组ID: %d，错误: %v", s.Task.ID, groupId, err)
			} else if portGroup != nil {
				// 判断是否为全端口扫描 - 对应PHP的isAllPort()方法
				// 根据常量定义，全端口扫描的端口组ID为3
				isAllPort = portGroup.Name == "0-65535"
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]端口组信息，任务ID: %d，端口组ID: %d，端口组名称: %s，是否全端口: %v", s.Task.ID, portGroup.Id, portGroup.Name, isAllPort)
			}
		}
	}

	// 获取探测端口信息并按IP分组 - 对应PHP: collect($this->task->probeInfos)->groupBy('ip')
	probePorts := s.getProbePortsGroupByIP()

	return taskPorts, isAllPort, probePorts
}

// getProbePortsGroupByIP 获取探测端口信息并按IP分组
func (s *ScanForadarAsset) getProbePortsGroupByIP() map[string][]task.TaskProbeInfo {
	ctx := context.Background()
	probePorts := make(map[string][]task.TaskProbeInfo)

	// 从数据库查询探测信息
	db := mysql.GetDbClient()
	var probeInfos []task.TaskProbeInfo
	err := db.Table("task_probe_infos").Where("task_id = ?", s.Task.ID).Find(&probeInfos).Error
	if err != nil {
		log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取探测信息失败，任务ID: %d，错误: %v", s.Task.ID, err)
	} else {
		// 按IP分组 - 对应PHP: collect($this->task->probeInfos)->groupBy('ip')
		for _, probeInfo := range probeInfos {
			// 检查IP字段的有效性
			if probeInfo.Ip.Valid && probeInfo.Ip.String != "" {
				ip := probeInfo.Ip.String
				probePorts[ip] = append(probePorts[ip], probeInfo)
			}
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]获取探测信息成功，任务ID: %d，IP数量: %d", s.Task.ID, len(probePorts))
	}

	return probePorts
}

// updateTaskAssetRules 更新任务资产组件信息
func (s *ScanForadarAsset) updateTaskAssetRules(ctx context.Context, ip string, assets []*UnifiedAsset, ports []int) error {
	taskAssetId := fmt.Sprintf("%d_%s", s.Task.ID, ip)
	taskAssetId2 := fmt.Sprintf("%d_%s", s.Task.ID, s.completeIPV6(ip))
	taskAssetId3 := fmt.Sprintf("%d_%s", s.Task.ID, s.compressIPV6(ip))
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]updateTaskAssetRules: taskAssetId: %s, taskAssetId2: %s, taskAssetId3: %s", taskAssetId, taskAssetId2, taskAssetId3)

	// 合并规则 - 从assets中提取规则并去重
	rulesMap := make(map[string]fofaee_subdomain.Rule)

	// 添加asset的规则
	for _, asset := range assets {
		for _, rule := range asset.RuleTags {
			rulesMap[rule.RuleId] = rule
		}
	}

	// 转换为规则列表
	var rules []fofaee_subdomain.Rule
	for _, rule := range rulesMap {
		rules = append(rules, rule)
	}

	// 收集所有端口
	allPortsMap := make(map[int]bool)
	for _, asset := range assets {
		if asset.Port > 0 {
			allPortsMap[asset.Port] = true
		}
	}

	var allPorts []int
	for port := range allPortsMap {
		allPorts = append(allPorts, port)
	}

	// 计算端口差异
	var diffPorts []int
	existingPortsMap := make(map[int]bool)
	for _, port := range ports {
		existingPortsMap[port] = true
	}

	for _, port := range allPorts {
		if !existingPortsMap[port] {
			diffPorts = append(diffPorts, port)
		}
	}

	var res interface{}

	if len(diffPorts) > 0 {
		// 有新端口，需要更新更多信息

		// 先获取现有的TaskAsset - 使用ListByParams
		results, err := elastic.AllByParams[fofaee_task_assets.FofaeeTaskAssets](
			1000,
			[][]interface{}{{"id", "in", []interface{}{taskAssetId, taskAssetId2, taskAssetId3}}},
			[]es2.Sorter{es2.NewFieldSort("_id").Desc(), es2.NewFieldSort("ip").Desc()},
		)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取TaskAsset失败: %v", err)
			return err
		}

		var taskAsset *fofaee_task_assets.FofaeeTaskAssets
		if len(results) > 0 {
			taskAsset = results[0]
		}

		// 合并端口
		finalPortsMap := make(map[int]bool)
		for _, port := range ports {
			finalPortsMap[port] = true
		}
		for _, port := range diffPorts {
			finalPortsMap[port] = true
		}

		var finalPorts []interface{}
		for port := range finalPortsMap {
			finalPorts = append(finalPorts, port)
		}

		// 合并协议
		protocolsMap := make(map[string]bool)
		if taskAsset != nil {
			for _, protocol := range taskAsset.Protocols {
				protocolsMap[protocol] = true
			}
		}

		// 创建端口到协议的映射
		portToProtocol := make(map[int]string)
		for _, asset := range assets {
			if asset.Port > 0 && asset.Protocol != "" {
				portToProtocol[asset.Port] = asset.Protocol
			}
		}

		// 添加新端口的协议
		for _, port := range diffPorts {
			if protocol, exists := portToProtocol[port]; exists && protocol != "" {
				protocolsMap[protocol] = true
			}
		}

		var finalProtocols []string
		for protocol := range protocolsMap {
			finalProtocols = append(finalProtocols, protocol)
		}

		// 构建新的端口列表
		var newPortList []fofaee_task_assets.PortListInfo
		if taskAsset != nil {
			newPortList = append(newPortList, taskAsset.PortList...)
		}

		// 添加新端口的信息
		for _, port := range diffPorts {
			// 查找对应的banner信息
			var banner string

			// 从assets中查找
			for _, asset := range assets {
				if asset.Port == port {
					banner = asset.Banner
					if banner == "" {
						banner = asset.Header
					}
					break
				}
			}

			portInfo := fofaee_task_assets.PortListInfo{
				Protocol: portToProtocol[port],
				Port:     port,
				Banner:   banner,
			}
			newPortList = append(newPortList, portInfo)
		}

		// 构建更新映射
		updateMap := map[string]interface{}{
			"rules":     rules,
			"ip":        utils.CompleteIPV6(ip),
			"ports":     finalPorts,
			"protocols": finalProtocols,
			"port_size": len(finalPorts),
			"port_list": newPortList,
		}

		fofaeeTaskAssetsModel := fofaee_task_assets.FofaeeTaskAssets{}
		// 执行更新
		err = elastic.UpdateByIds[fofaee_task_assets.FofaeeTaskAssets](
			ctx,
			fofaeeTaskAssetsModel.IndexName(),
			fofaeeTaskAssetsModel.TypeName(),
			[]string{taskAssetId, taskAssetId2, taskAssetId3},
			updateMap,
		)

		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]updateTaskAssetRules: 更新端口信息失败: %s 失败: %v", taskAssetId, err)
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]updateTaskAssetRules: 更新端口信息失败: %s 失败端口: %v", taskAssetId, diffPorts)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]updateTaskAssetRules: 更新端口信息: %s 完成", taskAssetId)
			log.Debugf("[ScanForadarAssetHandler]updateTaskAssetRules: 端口: %v, 更新数据: %+v", diffPorts, updateMap)
		}

		res = nil
	} else {
		// 没有新端口，只更新规则和IP
		err := elastic.UpdateByParams[fofaee_task_assets.FofaeeTaskAssets](
			[][]interface{}{{"_id", "in", []interface{}{taskAssetId, taskAssetId2, taskAssetId3}}},
			map[string]interface{}{
				"rules": rules,
				"ip":    utils.CompleteIPV6(ip),
			},
		)

		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]updateTaskAssetRules: 更新规则失败: %s: %v", taskAssetId, err)
			return err
		}

		res = fmt.Sprintf("更新规则成功: %s", taskAssetId)
	}

	// 刷新索引 - 对应PHP的 TaskAsset::query()->refresh()
	_, err := es.GetInstance().Refresh(new(fofaee_task_assets.FofaeeTaskAssets).IndexName()).Do(ctx)
	if err != nil {
		log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]刷新TaskAsset索引失败: %v", err)
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]updateTaskAssetRules: 更新规则: taskAssetId: %s, taskAssetId2: %s, taskAssetId3: %s, 完成: %v", taskAssetId, taskAssetId2, taskAssetId3, res)

	return nil
}

func (s *ScanForadarAsset) getRuleCount(ctx context.Context) int {
	query := es2.NewBoolQuery()
	query.Must(es2.NewTermQuery("task_id", s.Task.ID))

	results, err := elastic.All[fofaee_task_assets.FofaeeTaskAssets](1000, query, []es2.Sorter{es2.NewFieldSort("_id").Desc()}, "rules")
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]getRuleCount: %v", err)
		return 0
	}
	total := 0
	for _, result := range results {
		if result.Rules != nil {
			total += len(result.Rules)
		}
	}
	return total
}

// clearFailedAssets 删除扫描失败的数据
func (s *ScanForadarAsset) clearFailedAssets(ctx context.Context) {
	// PHP原始逻辑：Timer::after(5*1000, function () {
	// 延迟5秒执行
	time.AfterFunc(5*time.Second, func() {
		if s.Task.ID == 0 {
			return
		}

		// 删除IP+端口资产数据（ForadarAssets）
		// PHP代码：ForadarAssets::query()->where('task_id', $this->task->id)->chunk(100, function ($assets) {
		taskIdConditions := [][]interface{}{
			{"task_id", "=", s.Task.ID},
		}

		// 分批查询ForadarAssets，每批100条
		const batchSize = 100
		var lastSortValues []interface{}

		for {
			// 查询ForadarAssets
			_, foradarAssets, newLastSortValues, err := elastic.ListSearchAfterByParams[foradar_assets.ForadarAsset](
				batchSize, taskIdConditions, []es2.Sorter{es2.NewFieldSort("id").Asc()}, lastSortValues, "id", "task_id",
			)

			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]查询ForadarAssets失败: %v", err)
				break
			}

			if len(foradarAssets) == 0 {
				break
			}

			// 处理每个资产
			foradarAssetModel := foradar_assets.NewForadarAssetModel()
			for _, asset := range foradarAssets {
				// PHP逻辑：if (count($asset->task_id) == 1) { ForadarAssets::query()->where('_id', $asset->_id)->delete(); }
				if len(asset.TaskID) == 1 {
					if err := foradarAssetModel.DeleteById(ctx, asset.ID); err != nil {
						log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]删除ForadarAssets失败: %v, ID: %s", err, asset.ID)
					}
				}
			}

			// 如果返回的数据少于批次大小，说明已经是最后一批
			if len(foradarAssets) < batchSize {
				break
			}

			lastSortValues = newLastSortValues
		}

		// 删除IP维度数据（FofaeeAssets）
		// PHP代码：IpAssets::query()->where('task_id', $this->task->id)->chunk(100, function ($assets) {
		lastSortValues = nil // 重置分页参数

		// 用于批量删除IP历史记录
		var userAndIPs []map[uint64]string

		for {
			// 查询FofaeeAssets
			_, fofaeeAssets, newLastSortValues, err := elastic.ListSearchAfterByParams[fofaee_assets.FofaeeAssets](
				batchSize, taskIdConditions, []es2.Sorter{es2.NewFieldSort("id").Asc()}, lastSortValues, "id", "task_id", "user_id", "ip",
			)

			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]查询FofaeeAssets失败: %v", err)
				break
			}

			if len(fofaeeAssets) == 0 {
				break
			}

			// 处理每个IP资产
			for _, asset := range fofaeeAssets {
				// PHP逻辑：if (count($asset->task_id) == 1) {
				if len(asset.TaskId) == 1 {
					// 收集需要删除历史记录的用户ID和IP
					// PHP代码：IpHistory::query()->where('user_id', $asset->user_id)->where('ip', $asset->ip)->delete();
					userAndIPs = append(userAndIPs, map[uint64]string{
						uint64(asset.UserId): asset.Ip,
					})

					// 删除资产数据
					// PHP代码：IpAssets::query()->where('user_id', $this->task->user_id)->where('_id', $asset->_id)->delete();
					conditions := [][]interface{}{
						{"user_id", "=", s.Task.UserId},
						{"id", "=", asset.Id},
					}
					query := elastic.ParseQuery(conditions)
					if err := elastic.Delete[fofaee_assets.FofaeeAssets](query); err != nil {
						log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]删除FofaeeAssets失败: %v, ID: %s", err, asset.Id)
					}
				}
			}

			// 如果返回的数据少于批次大小，说明已经是最后一批
			if len(fofaeeAssets) < batchSize {
				break
			}

			lastSortValues = newLastSortValues
		}

		// 批量删除IP历史记录
		if len(userAndIPs) > 0 {
			ipHistoryModel := ip_history.NewModel()
			if err := ipHistoryModel.DeleteByUserAndIP(userAndIPs); err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]批量删除IP历史记录失败: %v", err)
			}
		}

		// PHP代码：LogService::info("任务中断,清除任务数据:", $this->task->id);
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]任务中断,清除任务数据:", fmt.Sprintf("%d", s.Task.ID))
	})
}

// taskExists 检查任务是否存在 改造，缓存
func (s *ScanForadarAsset) taskExists(ctx context.Context) bool {
	st, err := mysql.NewDSL[scan_task.ScanTasks]().FindByID(uint64(s.Task.ID))
	if err != nil || st.ID == 0 {
		return false
	}
	return true
}

func (s *ScanForadarAsset) completeIPV6(ipv6 string) string {
	// 验证是否为IPv6地址
	parsedIP := net.ParseIP(ipv6)
	if parsedIP == nil || parsedIP.To4() != nil {
		// 不是有效的IPv6地址，返回原始IP
		return ipv6
	}

	// 处理 :: 缩写
	if strings.Contains(ipv6, "::") {
		suppleChar := "0000"
		colonCount := strings.Count(ipv6, ":")
		for i := 7 - colonCount; i > 0; i-- {
			suppleChar += ":0000"
		}
		ipv6 = strings.Replace(ipv6, "::", ":"+suppleChar+":", 1)
	}

	// 分割并补全每个段
	charArr := strings.Split(ipv6, ":")
	for i, char := range charArr {
		if len(char) < 4 {
			// 左侧补0到4位
			charArr[i] = fmt.Sprintf("%04s", char)
		}
	}

	return strings.Join(charArr, ":")
}

// compressIPV6 将完整的IPv6地址压缩为精简格式
func (s *ScanForadarAsset) compressIPV6(ipv6 string) string {
	// 验证是否为IPv6地址
	parsedIP := net.ParseIP(ipv6)
	if parsedIP == nil || parsedIP.To4() != nil {
		// 不是有效的IPv6地址，返回原始IP
		return ipv6
	}

	// 使用net包的标准压缩方法
	return parsedIP.String()
}

func (s *ScanForadarAsset) sendWarnLogEmail(ctx context.Context, message string) {
	log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]发送告警: %s", message)
}

// sendProgress 发送进度
// 对应PHP中的sendProgress方法
// 参数：$task, $progress = null, $target = null, $message = null
func (s *ScanForadarAsset) sendProgress(task *scan_task.ScanTasks, progress *float64, target *string, message *string) {
	// 对应PHP: $step = $task->step ?? null;
	step := task.Step
	// 对应PHP: $status = $task->status ?? null;
	status := task.Status

	// 对应PHP: if (!$progress) { $progress = $task->progress; }
	var actualProgress float64
	if progress == nil {
		actualProgress = task.Progress
	} else {
		actualProgress = *progress
	}

	// 对应PHP: if ($progress == 100) { $step = Task::STEP_FINISHED; $status = Task::STATUS_FINISHED; }
	if actualProgress == 100 {
		step = scan_task.StepFinished
		status = scan_task.StatusFinished
	}

	// 对应PHP: if (in_array($task->status, [Task::STATUS_RUNNING, Task::STATUS_FINISHED]) && $progress < 100) { $status = Task::STATUS_RUNNING; }
	if (task.Status == scan_task.StatusDoing || task.Status == scan_task.StatusFinished) && actualProgress < 100 {
		status = scan_task.StatusDoing
	}

	// 对应PHP: if (($progress < 100) && ($status == Task::STATUS_FINISHED) && ($step == Task::STEP_FINISHED)) { $status = Task::STATUS_RUNNING; }
	if actualProgress < 100 && status == scan_task.StatusFinished && step == scan_task.StepFinished {
		status = scan_task.StatusDoing
	}

	// 对应PHP: 区分推荐资产扫描和本地扫描的cmd
	var cmd string
	if task.AssetType == scan_task.TASK_ASSET_CLOUD {
		cmd = "scan_task_recpmmand_progress"
	} else {
		cmd = "scan_task_progress"
	}

	// 计算使用时间
	useSeconds := int(time.Since(*task.StartAt).Seconds())

	// 对应PHP: if ($status == Task::STATUS_FINISHED) {
	if status == scan_task.StatusFinished {
		// 对应PHP: Cache::put('finish:'.$task->id, 1, 3600*24);
		finishKey := fmt.Sprintf("finish:%d", task.ID)
		redis.Set(context.Background(), finishKey, 1, 24*time.Hour)

		// 对应PHP: try { WsServer::sendClientSuccess(...) } catch (\Throwable $e) { LogService::warn(...) }
		progressTarget := ""
		if target != nil {
			progressTarget = *target
		}
		progressMessage := ""
		if message != nil {
			progressMessage = *message
		}

		data := map[string]interface{}{
			"task_id":     task.ID,
			"status":      status,
			"step":        step,
			"progress":    math.Round(actualProgress*100) / 100,
			"target":      progressTarget,
			"type":        0,
			"name":        task.Name,
			"use_seconds": useSeconds,
			"user_id":     task.UserId,
			"message":     progressMessage,
			"start_at":    task.StartAt.Format("2006-01-02 15:04:05"),
		}

		err := websocket_message.PublishSuccess(int64(task.UserId), cmd, data)
		if err != nil {
			log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---1", "ScanForadarAssetJob", map[string]interface{}{
				"error": err.Error(),
			})
		}

		// 对应PHP: 判断当前任务是否为资产测绘下发的扫描任务，如果是的话需要推送进度
		expendId := task.DetectAssetsTasksId
		if expendId > 0 {
			// 对应PHP: 查询总共多少个任务
			totalExpend, err := scan_task.NewScanTasksModel().FindAll(
				mysql.WithWhere("user_id = ?", task.UserId),
				mysql.WithWhere("detect_assets_tasks_id = ?", expendId),
			)
			if err != nil {
				log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询总任务失败: %v", err)
				return
			}

			// 对应PHP: $finishNum = $totalExpend->where('status', Task::STATUS_FINISHED)->count();
			finishNum := 0
			for _, t := range totalExpend {
				if t.Status == scan_task.StatusFinished {
					finishNum++
				}
			}

			totalExpendNum := len(totalExpend)

			// 对应PHP: 查询包括删除的扫描任务的数量
			var totalInlcueDeleteTaskNum int64
			mysql.GetDbClient().Model(&scan_task.ScanTasks{}).Where(
				"user_id = ? AND detect_assets_tasks_id = ?",
				task.UserId, expendId,
			).Unscoped().Count(&totalInlcueDeleteTaskNum)

			var expendProgress float64
			var finishStatus int

			if totalExpendNum == finishNum {
				// 对应PHP: 资产测绘任务全部扫描完成
				expendProgress = 100
				if int64(totalExpendNum) == totalInlcueDeleteTaskNum {
					finishStatus = 1 // 第五步还存在，2改为1
				} else {
					finishStatus = 3
				}
			} else {
				// 对应PHP: 资产测绘任务没全部扫描完成
				expendProgress = (float64(finishNum) / float64(totalExpendNum)) * 100
			}

			log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]expendProgress: %f", expendProgress)
			if expendProgress == 100 {
				// 对应PHP: $info = DetectAssetsTask::query()->where('id', $expendId)->where('user_id', $task->user_id)->first();
				info, err := detect_assets_tasks.NewModel().First(
					mysql.WithWhere("id = ? AND user_id = ?", expendId, task.UserId),
				)
				if err != nil {
					log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询测绘任务失败: %v", err)
					return
				}

				// 对应PHP: $wsType = ($info->expand_source??0)?'detect_assets_tasks_cloud':'detect_assets_tasks';
				wsType := "detect_assets_tasks"
				if info.ExpandSource != 0 {
					wsType = "detect_assets_tasks_cloud"
				}

				// 对应PHP: DetectAssetsTask::query()->where('id', $expendId)->where('user_id', $task->user_id)->update([...]);
				updateData := map[string]interface{}{
					"progress":    100,
					"status":      finishStatus,
					"step_status": detect_assets_tasks.StepStatusDone,
				}
				err = detect_assets_tasks.NewModel().UpdateAny(updateData, mysql.WithWhere("id = ? AND user_id = ?", expendId, task.UserId))
				if err != nil {
					log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]更新测绘任务失败: %v", err)
				}
				log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]更新测绘任务完成，任务ID: %d，检测任务: %+v,update:%+v", expendId, info, updateData)

				// 对应PHP: try { WsServer::sendClientSuccess(...) } catch (\Throwable $e) { LogService::warn(...) }
				wsData := map[string]interface{}{
					"detect_assets_tasks_id": expendId,
					"status":                 2,
					"step_status":            info.StepStatus,
					"progress":               100,
					"step":                   info.Step,
					"step_detail":            info.StepDetail,
					"name":                   info.Name,
					"user_id":                s.Task.UserId,
				}
				wsTypeNew := "finish_auto_detect"
				err = websocket_message.PublishSuccess(int64(task.UserId), wsType, wsData)
				if err != nil {
					log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---2", "ScanForadarAssetJob", map[string]interface{}{
						"error": err.Error(),
					})
				}
				err = websocket_message.PublishSuccess(int64(task.UserId), wsTypeNew, wsData)
				if err != nil {
					log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---3", "ScanForadarAssetJob-finish_auto_detect", map[string]interface{}{
						"error": err.Error(),
					})
				}
			} else {
				// 对应PHP: 测绘任务中扫描资产的整体进度
				info, err := detect_assets_tasks.NewModel().First(
					mysql.WithWhere("id = ? AND user_id = ?", expendId, task.UserId),
				)
				if err != nil {
					log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询测绘任务失败: %v", err)
					return
				}

				wsType := "detect_assets_tasks"
				if info.ExpandSource != 0 {
					wsType = "detect_assets_tasks_cloud"
				}

				// 对应PHP: if (($info->progress != $expendProgress) && ($info->progress < $expendProgress)) {
				if info.Progress != expendProgress && info.Progress < expendProgress {
					err = detect_assets_tasks.NewModel().UpdateAny(map[string]interface{}{
						"progress": expendProgress,
					}, mysql.WithWhere("id = ? AND user_id = ?", expendId, task.UserId))
					if err != nil {
						log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]更新测绘任务进度失败: %v", err)
					}
				}

				// 对应PHP: try { WsServer::sendClientSuccess(...) } catch (\Throwable $e) { LogService::warn(...) }
				wsData := map[string]interface{}{
					"detect_assets_tasks_id": expendId,
					"status":                 info.Status,
					"step_status":            info.StepStatus,
					"progress":               math.Round(expendProgress*100) / 100,
					"step":                   info.Step,
					"step_detail":            info.StepDetail,
					"name":                   info.Name,
					"user_id":                s.Task.UserId,
				}

				err = websocket_message.PublishSuccess(int64(task.UserId), wsType, wsData)
				if err != nil {
					log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---3", "ScanForadarAssetJob", map[string]interface{}{
						"error": err.Error(),
					})
				}
			}
		}

		// 对应PHP: 判断当前任务是否为组织架构测绘下发的扫描任务，如果是的话需要推送进度
		orgExpendId := task.OrganizationDiscoverTaskId
		if orgExpendId != nil && *orgExpendId > 0 {
			// 对应PHP: 查询总共多少个任务
			totalExpend, err := scan_task.NewScanTasksModel().FindAll(
				mysql.WithWhere("user_id = ?", task.UserId),
				mysql.WithWhere("organization_discover_task_id = ?", *orgExpendId),
			)
			if err != nil {
				log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询组织架构任务失败: %v", err)
				return
			}

			// 对应PHP: $finishNum = $totalExpend->where('status', Task::STATUS_FINISHED)->count();
			finishNum := 0
			for _, t := range totalExpend {
				if t.Status == scan_task.StatusFinished {
					finishNum++
				}
			}

			totalExpendNum := len(totalExpend)

			var expendProgress float64
			if totalExpendNum == finishNum {
				// 对应PHP: 资产测绘任务全部扫描完成
				expendProgress = 100
			} else {
				// 对应PHP: 资产测绘任务没全部扫描完成
				expendProgress = (float64(finishNum) / float64(totalExpendNum)) * 100
			}

			if expendProgress == 100 {
				// 对应PHP: $info = OrganizationTask::query()->where('id', $expendId)->where('user_id', $task->user_id)->first();
				info, err := organization_discover_task.NewModel().First(
					organization_discover_task.WithUserID(task.UserId),
					organization_discover_task.WithID(*orgExpendId),
				)
				if err != nil {
					log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询组织架构测绘任务失败: %v", err)
					return
				}

				wsType := "org_detect_assets_tasks"

				// 对应PHP: OrganizationTask::query()->where('id', $expendId)->where('user_id', $task->user_id)->update([...]);
				info.ScanProgress = 100
				info.StepStatus = organization_discover_task.StepStatusFinish
				err = organization_discover_task.NewModel().Update(info)
				if err != nil {
					log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]更新组织架构测绘任务失败: %v", err)
				}

				// 对应PHP: try { WsServer::sendClientSuccess(...) } catch (\Throwable $e) { LogService::warn(...) }
				wsData := map[string]interface{}{
					"organization_discover_task_id": *orgExpendId,
					"step_status":                   organization_discover_task.StepStatusFinish,
					"progress":                      100,
					"name":                          info.Name,
					"user_id":                       s.Task.UserId,
				}

				err = websocket_message.PublishSuccess(int64(task.UserId), wsType, wsData)
				if err != nil {
					log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---org-1", "ScanForadarAssetJob", map[string]interface{}{
						"error": err.Error(),
					})
				}
			} else {
				// 对应PHP: 测绘任务中扫描资产的整体进度
				info, err := organization_discover_task.NewModel().First(
					organization_discover_task.WithUserID(task.UserId),
					organization_discover_task.WithID(*orgExpendId),
				)
				if err != nil {
					log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询组织架构测绘任务失败: %v", err)
					return
				}

				wsType := "org_detect_assets_tasks"

				// 对应PHP: if (($info->progress != $expendProgress) && ($info->progress < $expendProgress)) {
				if info.ScanProgress != expendProgress && info.ScanProgress < expendProgress {
					info.ScanProgress = expendProgress
					err = organization_discover_task.NewModel().Update(info)
					if err != nil {
						log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]更新组织架构测绘任务进度失败: %v", err)
					}
				}

				// 对应PHP: try { WsServer::sendClientSuccess(...) } catch (\Throwable $e) { LogService::warn(...) }
				wsData := map[string]interface{}{
					"organization_discover_task_id": *orgExpendId,
					"step_status":                   organization_discover_task.StepStatusNotFinish,
					"progress":                      math.Round(expendProgress*100) / 100,
					"name":                          info.Name,
					"user_id":                       s.Task.UserId,
				}

				err = websocket_message.PublishSuccess(int64(task.UserId), wsType, wsData)
				if err != nil {
					log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---org-2", "ScanForadarAssetJob", map[string]interface{}{
						"error": err.Error(),
					})
				}
			}
		}
		return
	}

	// 对应PHP: if (!Cache::get('finish:'.$task->id)) {
	finishKey := fmt.Sprintf("finish:%d", task.ID)
	var finishValue int
	err := redis.Get(context.Background(), finishKey, &finishValue)
	if err != nil || finishValue == 0 {
		// 对应PHP: try { WsServer::sendClientSuccess(...) } catch (\Throwable $e) { LogService::warn(...) }
		progressTarget := ""
		if target != nil {
			progressTarget = *target
		}
		progressMessage := ""
		if message != nil {
			progressMessage = *message
		}

		data := map[string]interface{}{
			"task_id":     task.ID,
			"status":      status,
			"step":        step,
			"progress":    math.Round(actualProgress*100) / 100,
			"target":      progressTarget,
			"type":        0,
			"name":        task.Name,
			"use_seconds": useSeconds,
			"user_id":     task.UserId,
			"message":     progressMessage,
			"start_at":    task.StartAt.Format("2006-01-02 15:04:05"),
		}

		err := websocket_message.PublishSuccess(int64(task.UserId), cmd, data)
		if err != nil {
			log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---4", "ScanForadarAssetJob", map[string]interface{}{
				"error": err.Error(),
			})
		}

		// 对应PHP: 判断当前任务是否为资产测绘下发的扫描任务，如果是的话需要推送进度
		expendId := task.DetectAssetsTasksId
		if expendId > 0 {
			// 对应PHP: 查询总共多少个任务
			totalExpend, err := scan_task.NewScanTasksModel().FindAll(
				mysql.WithWhere("user_id = ?", task.UserId),
				mysql.WithWhere("detect_assets_tasks_id = ?", expendId),
			)
			if err != nil {
				log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询总任务失败: %v", err)
				return
			}

			// 对应PHP: $finishNum = $totalExpend->where('status', Task::STATUS_FINISHED)->count();
			finishNum := 0
			for _, t := range totalExpend {
				if t.Status == scan_task.StatusFinished {
					finishNum++
				}
			}

			totalExpendNum := len(totalExpend)

			// 对应PHP: $expendProgress = 100*(($finishNum/$totalExpendNum))+$progress*(1/$totalExpendNum);
			expendProgress := 100*(float64(finishNum)/float64(totalExpendNum)) + actualProgress*(1/float64(totalExpendNum))

			// 对应PHP: 测绘任务中扫描资产的整体进度
			info, err := detect_assets_tasks.NewModel().First(
				mysql.WithWhere("id = ? AND user_id = ?", expendId, task.UserId),
			)
			if err != nil {
				log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询测绘任务失败: %v", err)
				return
			}

			wsType := "detect_assets_tasks"
			if info.ExpandSource != 0 {
				wsType = "detect_assets_tasks_cloud"
			}

			// 对应PHP: if (($info->progress != $expendProgress) && ($info->progress < $expendProgress)) {
			if info.Progress != expendProgress && info.Progress < expendProgress {
				err = detect_assets_tasks.NewModel().UpdateAny(map[string]interface{}{
					"progress": expendProgress,
				}, mysql.WithWhere("id = ? AND user_id = ?", expendId, task.UserId))
				if err != nil {
					log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]更新测绘任务进度失败: %v", err)
				}
			}

			// 对应PHP: try { WsServer::sendClientSuccess(...) } catch (\Throwable $e) { LogService::warn(...) }
			wsData := map[string]interface{}{
				"detect_assets_tasks_id": expendId,
				"status":                 info.Status,
				"step_status":            info.StepStatus,
				"progress":               math.Round(expendProgress*100) / 100,
				"step":                   info.Step,
				"step_detail":            info.StepDetail,
				"name":                   info.Name,
				"user_id":                s.Task.UserId,
			}

			err = websocket_message.PublishSuccess(int64(task.UserId), wsType, wsData)
			if err != nil {
				log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---5", "ScanForadarAssetJob", map[string]interface{}{
					"error": err.Error(),
				})
			}
		}

		// 对应PHP: 判断当前任务是否为组织架构测绘下发的扫描任务，如果是的话需要推送进度
		orgExpendId := task.OrganizationDiscoverTaskId
		if orgExpendId != nil && *orgExpendId > 0 {
			// 对应PHP: 查询总共多少个任务
			totalExpend, err := scan_task.NewScanTasksModel().FindAll(
				mysql.WithWhere("user_id = ?", task.UserId),
				mysql.WithWhere("organization_discover_task_id = ?", *orgExpendId),
			)
			if err != nil {
				log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询组织架构任务失败: %v", err)
				return
			}

			// 对应PHP: $finishNum = $totalExpend->where('status', Task::STATUS_FINISHED)->count();
			finishNum := 0
			for _, t := range totalExpend {
				if t.Status == scan_task.StatusFinished {
					finishNum++
				}
			}

			totalExpendNum := len(totalExpend)

			// 对应PHP: $expendProgress = 100*(($finishNum/$totalExpendNum))+$progress*(1/$totalExpendNum);
			expendProgress := 100*(float64(finishNum)/float64(totalExpendNum)) + actualProgress*(1/float64(totalExpendNum))

			// 对应PHP: 测绘任务中扫描资产的整体进度
			info, err := organization_discover_task.NewModel().First(
				organization_discover_task.WithUserID(task.UserId),
				organization_discover_task.WithID(*orgExpendId),
			)
			if err != nil {
				log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]查询组织架构测绘任务失败: %v", err)
				return
			}

			wsType := "org_detect_assets_tasks"

			// 对应PHP: if (($info->progress != $expendProgress) && ($info->progress < $expendProgress)) {
			if info.ScanProgress != expendProgress && info.ScanProgress < expendProgress {
				info.ScanProgress = expendProgress
				err = organization_discover_task.NewModel().Update(info)
				if err != nil {
					log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]更新组织架构测绘任务进度失败: %v", err)
				}
			}

			// 对应PHP: try { WsServer::sendClientSuccess(...) } catch (\Throwable $e) { LogService::warn(...) }
			wsData := map[string]interface{}{
				"organization_discover_task_id": *orgExpendId,
				"step_status":                   1, // 对应PHP中的硬编码值
				"progress":                      math.Round(expendProgress*100) / 100,
				"step":                          info.Step,
				"name":                          info.Name,
				"user_id":                       s.Task.UserId,
			}

			err = websocket_message.PublishSuccess(int64(task.UserId), wsType, wsData)
			if err != nil {
				log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]mq的websocket报错了啊---org-3", "ScanForadarAssetJob", map[string]interface{}{
					"error": err.Error(),
				})
			}
		}
	} else {
		// 对应PHP: LogService::info('completeAssetData', "TaskId:{$this->task->id}", '已经推送完成了，又在推送');
		log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]completeAssetData", fmt.Sprintf("TaskId:%d", s.Task.ID), "已经推送完成了，又在推送")
	}
}

// 从subdomain创建统一资产
func (s *ScanForadarAsset) createUnifiedAssetFromSubdomain(subdomain *fofaee_subdomain.FofeeSubdomain) *UnifiedAsset {
	port := 0
	if subdomain.Port != nil {
		if portFloat, ok := subdomain.Port.(float64); ok {
			port = int(portFloat)
		}
	}

	// 处理 Favicon 字段
	var favicon *AssetFavicon
	if subdomain.Favicon.Base64 != "" || subdomain.Favicon.Hash != 0 {
		favicon = &AssetFavicon{
			Base64: subdomain.Favicon.Base64,
			Hash:   cast.ToString(subdomain.Favicon.Hash),
		}
	}

	return &UnifiedAsset{
		IP:              subdomain.Ip,
		Port:            port,
		Protocol:        subdomain.Protocol,
		Banner:          subdomain.Header,
		Header:          subdomain.Header,
		Title:           subdomain.Title,
		Host:            subdomain.Host,
		Domain:          subdomain.Domain,
		Subdomain:       subdomain.Subdomain,
		Body:            subdomain.Body, // 对应PHP: $subAsset->body
		RuleTags:        subdomain.RuleTags,
		IsSubdomain:     true,
		Favicon:         favicon,                    // 设置 favicon 字段
		RecommendReason: make([]RecommendReason, 0), // 初始化推荐理由为空切片
	}
}

// 从service创建统一资产
func (s *ScanForadarAsset) createUnifiedAssetFromService(service *fofaee_service.FofaeeService) *UnifiedAsset {
	return &UnifiedAsset{
		IP:              service.IP,
		Port:            service.Port,
		Protocol:        service.Protocol,
		Banner:          service.Banner,
		Header:          "", // service没有Header字段
		Title:           "", // service没有Title字段
		Host:            "", // service没有Host字段
		Domain:          "", // service没有Domain字段
		Subdomain:       "", // service没有Subdomain字段
		Body:            "", // service没有Body字段，设为空
		RuleTags:        convertServiceRulesToSubdomainRules(service.RuleTags),
		IsSubdomain:     false,
		RecommendReason: make([]RecommendReason, 0), // 初始化推荐理由为空切片
	}
}

// 转换service的RuleTags到subdomain的Rule格式
func convertServiceRulesToSubdomainRules(serviceRules []fofaee_service.Rule) []fofaee_subdomain.Rule {
	rules := make([]fofaee_subdomain.Rule, 0, len(serviceRules))
	for _, rule := range serviceRules {
		rules = append(rules, fofaee_subdomain.Rule{
			Category:         rule.Category,
			CnCategory:       rule.CnCategory,
			CnCompany:        rule.CnCompany,
			CnParentCategory: rule.CnParentCategory,
			CnProduct:        rule.CnProduct,
			Company:          rule.Company,
			Level:            rule.Level,
			ParentCategory:   rule.ParentCategory,
			Product:          rule.Product,
			RuleId:           rule.RuleID,
			SoftHard:         rule.SoftHard,
		})
	}
	return rules
}

// 写入Ip+Port数据
func (s *ScanForadarAsset) writeIpAndPortAsset(ctx context.Context, allAssets []*UnifiedAsset, total int, crawlerProgress float64, ipByPortService map[int]*fofaee_service.FofaeeService, taskPorts []int, ip string, assetPorts []interface{}, isAllPort bool) {
	// 对应PHP: [$allAssets, $isOffOnline,$offLinePorts] = $this->appendRecommendResultToAssets($allAssets, $ip, $ports,$taskPorts,$isAllPort);
	allAssets, isOffOnline, offLinePorts, err := s.appendRecommendResultToAssets(ctx, allAssets, ip, assetPorts, taskPorts, isAllPort)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]appendRecommendResultToAssets失败: %v", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]DEBUG - 第2795行：appendRecommendResultToAssets处理完成，allAssets长度: %d", len(allAssets))

	// 对应PHP: $allAssets->each(function ($subAsset) use ($total, $crawlerProgress, $ipByPortService, $taskPorts, $allAssets, $isOffOnline, $offLinePorts) {
	for _, subAsset := range allAssets {
		// 对应PHP: if (!Task::query()->where('id', $this->task->id)->exists()) { return; }
		if !s.taskExists(ctx) {
			return
		}

		// 对应PHP: $getLiveSubdoamnin = getSubdomain($subAsset->url ?? null);
		getLiveSubdomain := utils.GetSubdomain(subAsset.Host)

		// 对应PHP: $id = md5(completeIPV6($subAsset->ip).$subAsset->port.$subAsset->protocol.$getLiveSubdoamnin.$this->task->user_id);
		// id := s.generateAssetId(s.completeIPV6(subAsset.IP), subAsset.Port, subAsset.Protocol, getLiveSubdomain)

		// 对应PHP: $scanAsset = $this->completeAssetData($id, $subAsset, isset($subAsset->body) ? $ipByPortService->get($subAsset->port, null) : null, $isOffOnline, $offLinePorts);
		var service *fofaee_service.FofaeeService
		if subAsset.Banner != "" {
			service = ipByPortService[subAsset.Port]
		}
		id := utils.SafeString(s.generateAssetId(s.completeIPV6(subAsset.IP), subAsset.Port, subAsset.Protocol, getLiveSubdomain))
		log.WithContextInfof(ctx, "[LOGO_DEBUG] 资产信息 - IP: %s, Port: %d, Protocol: %s, Subdomain: %s, 生成ID: %s", subAsset.IP, subAsset.Port, subAsset.Protocol, getLiveSubdomain, id)
		scanAsset := s.completeAssetData(ctx, id, subAsset, service, isOffOnline, offLinePorts)
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]writeIpAndPortAsset 最终当前ip端口的status信息: ip=%s, assets_id=%s, port=%d, status=%v",
			scanAsset["ip"], scanAsset["id"], scanAsset["port"], scanAsset["status"])

		func() {
			defer func() {
				if r := recover(); r != nil {
					log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]writeIpAndPortAsset处理资产时发生panic: %v", r)
				}
			}()

			// 查找现有资产
			// 对应PHP: if (!$asset = ForadarAssets::query()->where('_id', $id)->first()) {
			foradarAssetModel := foradar_assets.NewForadarAssetModel()
			existingAssets, err := foradarAssetModel.ListAll(ctx, es2.NewBoolQuery().Must(
				es2.NewTermQuery("id", id),
			))
			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]查询现有资产失败: %v", err)
				return
			}

			if len(existingAssets) == 0 {
				// 新增资产 - 对应PHP中的新增逻辑
				// CDN检测 - 对应PHP的完整CDN检测逻辑
				// 对应PHP: if(!($scanAsset['is_ipv6'] ?? false)){ $scanAsset['is_cdn'] = isCdn($scanAsset['ip']); }
				if !getBoolValue(scanAsset["is_cdn"]) {
					if isIPv6, ok := scanAsset["is_ipv6"].(bool); !ok || !isIPv6 {
						isCdnResult := network.IsCdn(scanAsset["ip"], 0)
						scanAsset["is_cdn"] = isCdnResult
						// 如果检测为CDN，立即设置缓存
						if isCdnResult {
							s.setCdnCache(ctx, scanAsset)
						}
					}
				}
				// 对应PHP: if(empty($scanAsset['is_cdn'])){ $scanAsset['is_cdn'] = isCdn([($scanAsset['cname'] ?? '')],1); }
				if !getBoolValue(scanAsset["is_cdn"]) {
					if cname, ok := scanAsset["cname"]; ok {
						if cnameSlice, ok := cname.([]string); ok {
							isCdnResult := network.IsCdn(cnameSlice, 1)
							scanAsset["is_cdn"] = isCdnResult
							// 如果检测为CDN，立即设置缓存
							if isCdnResult {
								s.setCdnCache(ctx, scanAsset)
							}
						}
					}
				}
				// 对应PHP: if(empty($scanAsset['is_cdn'])){ if(($scanAsset['rule_tags'] ?? [])){ $scanAsset['is_cdn'] = ruleIsCdn($scanAsset['rule_tags']); } }
				if !getBoolValue(scanAsset["is_cdn"]) {
					if ruleTags, ok := scanAsset["rule_tags"]; ok {
						if ruleTagsSlice, ok := ruleTags.([]interface{}); ok {
							isCdnResult := network.RuleIsCdn(ruleTagsSlice)
							scanAsset["is_cdn"] = isCdnResult
							// 如果检测为CDN，立即设置缓存
							if isCdnResult {
								s.setCdnCache(ctx, scanAsset)
							}
						}
					}
				}
				// 对应PHP: if(empty($scanAsset['is_cdn']) && ($scanAsset['cert']['subject_key'] ?? '')){ $cdnNum = count(explode('cdn',($scanAsset['cert']['subject_key'] ?? ''))); if($cdnNum > 1){ $scanAsset['is_cdn'] = true; } }
				if !getBoolValue(scanAsset["is_cdn"]) {
					if certData, ok := scanAsset["cert"]; ok && certData != nil {
						if certMap, ok := certData.(map[string]interface{}); ok {
							if subjectKey, exists := certMap["subject_key"]; exists {
								subjectKeyStr := safeStringValue(subjectKey)
								if subjectKeyStr != "" {
									cdnParts := strings.Split(strings.ToLower(subjectKeyStr), "cdn")
									if len(cdnParts) > 1 {
										scanAsset["is_cdn"] = true
										// 如果检测为CDN，立即设置缓存
										s.setCdnCache(ctx, scanAsset)
									}
								}
							}
						}
					}
				}

				// 证书处理 - 对应PHP: if($scanAsset['status'] == ForadarAssets::STATUS_CLAIMED){ CertAssetService::dealCertAsset($scanAsset,$this->task->detect_assets_tasks_id ?? null); }
				status := utils.GetIntValue(scanAsset["status"])
				if status == fofaee_assets.STATUS_CLAIMED {
					// 构建证书处理需要的ForadarAsset结构体，对应PHP的弱类型转换
					certAsset := &cert.ForadarAsset{
						ID:        safeStringValue(scanAsset["id"]),
						IP:        safeStringValue(scanAsset["ip"]),
						UserID:    s.Task.UserId,
						CompanyID: uint64(s.Task.CompanyId),
						Status:    status,
						Type:      utils.GetIntValue(scanAsset["type"]),
					}

					// 类型转换处理
					if port, ok := scanAsset["port"]; ok {
						certAsset.Port = utils.GetIntValue(port)
					}
					if protocol, ok := scanAsset["protocol"]; ok {
						certAsset.Protocol = safeStringValue(protocol)
					}
					if subdomain, ok := scanAsset["subdomain"]; ok {
						certAsset.Subdomain = safeStringValue(subdomain)
					}
					if domain, ok := scanAsset["domain"]; ok {
						certAsset.Domain = safeStringValue(domain)
					}

					// 企业名称处理 - 对应PHP的数组处理逻辑
					if clueCompanyName, ok := scanAsset["clue_company_name"]; ok {
						certAsset.ClueCompanyName = safeStringArray(clueCompanyName)
					}

					// 处理证书信息
					if certData, ok := scanAsset["cert"]; ok && certData != nil {
						if certMap, ok := certData.(map[string]interface{}); ok {
							certAsset.Cert = &cert.CertInfo{
								SN:        safeStringValue(certMap["sn"]),
								IssuerCN:  safeStringValue(certMap["issuer_cn"]),
								SubjectCN: safeStringValue(certMap["subject_cn"]),
								Raw:       safeStringValue(certMap["raw"]),
								IsValid:   getBoolValue(certMap["is_valid"]),
								Version:   safeStringValue(certMap["v"]),
								CertDate:  safeStringValue(certMap["cert_date"]),
								NotBefore: safeStringValue(certMap["not_before"]),
								NotAfter:  safeStringValue(certMap["not_after"]),
							}

							// 处理数组字段
							if issuerCns, ok := certMap["issuer_cns"]; ok {
								if cnsSlice, ok := issuerCns.([]string); ok {
									strs := make([]string, 0, len(cnsSlice))
									for _, cn := range cnsSlice {
										strs = append(strs, safeStringValue(cn))
									}
									certAsset.Cert.IssuerCNs = strs
								}
							}
							if issuerOrg, ok := certMap["issuer_org"]; ok {
								if orgSlice, ok := issuerOrg.([]string); ok {
									strs := make([]string, 0, len(orgSlice))
									for _, org := range orgSlice {
										strs = append(strs, safeStringValue(org))
									}
									certAsset.Cert.IssuerOrg = strs
								}
							}
							if subjectOrg, ok := certMap["subject_org"]; ok {
								if orgSlice, ok := subjectOrg.([]string); ok {
									strs := make([]string, 0, len(orgSlice))
									for _, org := range orgSlice {
										strs = append(strs, safeStringValue(org))
									}
									certAsset.Cert.SubjectOrg = strs
								}
							}

							// 其他字段
							if certNum, ok := certMap["cert_num"]; ok {
								certAsset.Cert.CertNum = utils.GetIntValue(certNum)
							}
							if sigAlth, ok := certMap["sig_alth"]; ok {
								certAsset.Cert.SigAlth = safeStringValue(sigAlth)
							}
							if subjectKey, ok := certMap["subject_key"]; ok {
								certAsset.Cert.SubjectKey = safeStringValue(subjectKey)
							}
							if validType, ok := certMap["valid_type"]; ok {
								certAsset.Cert.ValidType = safeStringValue(validType)
							}
						}
					}

					// 调用证书处理服务
					certService := cert.NewCertAssetService()
					var detectTaskID *uint64
					if s.Task.DetectAssetsTasksId > 0 {
						detectTaskIDValue := uint64(s.Task.DetectAssetsTasksId)
						detectTaskID = &detectTaskIDValue
					}
					err := certService.DealCertAsset(ctx, certAsset, detectTaskID)
					if err != nil {
						log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]处理证书资产失败: %v", err)
					}
				}

				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]我是新增数据: id=%s, ip=%s, port=%d, protocol=%s, status=%d, subdomain=%s, user_id=%d",
					id, s.completeIPV6(subAsset.IP), subAsset.Port, subAsset.Protocol, utils.GetIntValue(scanAsset["status"]), getLiveSubdomain, s.Task.UserId)

				// 插入数据库 - 对应PHP: ForadarAssets::query()->insert($scanAsset);
				// 注意：这里应该使用ES的插入方法，而不是MySQL
				// 先将map转换为适合ES的结构体，根据mapping修正字段名
				newAsset := &foradar_assets.ForadarAsset{
					ID:             id,
					Fid:            safeStringValue(scanAsset["fid"]),
					Protocol:       subAsset.Protocol,
					Port:           subAsset.Port,
					Ip:             s.completeIPV6(subAsset.IP),
					UserID:         int(s.Task.UserId),
					Status:         utils.GetIntValue(scanAsset["status"]),
					Type:           utils.GetIntValue(scanAsset["type"]),
					CompanyID:      int(s.Task.CompanyId),
					OnlineState:    utils.GetIntValue(scanAsset["online_state"]),
					HTTPStatusCode: utils.GetIntValue(scanAsset["http_status_code"]), // 修正字段名
					Url:            safeStringValue(scanAsset["url"]),
					Title:          safeStringValue(scanAsset["title"]),
					Domain:         safeStringValue(scanAsset["domain"]),
					Subdomain:      safeStringValue(scanAsset["subdomain"]),
					Level:          utils.GetIntValue(scanAsset["level"]),
					IsIpv6:         getBoolValue(scanAsset["is_ipv6"]),
					OpenParse:      getBoolValue(scanAsset["open_parse"]),
					// 根据mapping添加存在的字段
					CloudName:        safeStringValue(scanAsset["cloud_name"]),  // mapping中是cloud_name，类型是text
					IsFakeAssets:     getBoolValue(scanAsset["is_fake_assets"]), // 修正类型，mapping中是boolean
					ReliabilityScore: utils.GetIntValue(scanAsset["reliability_score"]),
					IsLogin:          utils.GetIntValue(scanAsset["is_login"]),
					LoginScreenshot:  safeStringValue(scanAsset["login_screenshot"]),
					Screenshot:       safeStringValue(scanAsset["screenshot"]),
					Header:           safeStringValue(scanAsset["header"]),
					Banner:           safeStringValue(scanAsset["banner"]),
					Body:             safeStringValue(scanAsset["body"]),
					CreatedAt:        utils.CurrentTime(), // 使用工具函数生成时间
					UpdatedAt:        utils.CurrentTime(),
					// 添加更多字段
					AssetsSource:       getInt64Pointer(scanAsset["assets_source"]),
					OneforallSource:    safeStringValue(scanAsset["oneforall_source"]),
					SourceUpdatedAt:    safeStringValue(scanAsset["source_updated_at"]),
					AssetsSourceDomain: safeStringValue(scanAsset["assets_source_domain"]),
					IsCdn:              getBoolValue(scanAsset["is_cdn"]),
					ThreatenType:       utils.SafeInt(scanAsset["threaten_type"]),
				}

				// 处理数组字段
				if taskId, ok := scanAsset["task_id"]; ok {
					if taskIdSlice, ok := taskId.([]uint64); ok {
						taskIdInt := make([]int, len(taskIdSlice))
						for i, v := range taskIdSlice {
							taskIdInt[i] = int(v)
						}
						newAsset.TaskID = taskIdInt
					}
				}

				if detectTasksId, ok := scanAsset["detect_assets_tasks_id"]; ok {
					if detectTasksIdSlice, ok := detectTasksId.([]uint64); ok {
						detectTasksIdInt := make([]int, len(detectTasksIdSlice))
						for i, v := range detectTasksIdSlice {
							detectTasksIdInt[i] = int(v)
						}
						newAsset.DetectAssetsTasksID = detectTasksIdInt
					}
				}

				if tags, ok := scanAsset["tags"]; ok {
					if tagsSlice, ok := tags.([]int); ok {
						newAsset.Tags = tagsSlice
					}
				}

				if cname, ok := scanAsset["cname"]; ok {
					if cnameSlice, ok := cname.([]string); ok {
						newAsset.Cname = cnameSlice
					}
				}

				// 处理ClueCompanyName字段 - 确保入库时是数组类型
				if clueCompanyName, ok := scanAsset["clue_company_name"]; ok {
					newAsset.ClueCompanyName = safeStringArray(clueCompanyName)
				} else {
					newAsset.ClueCompanyName = []string{} // 默认为空数组
				}

				// 处理Logo结构体
				log.WithContextInfof(ctx, "[LOGO_DEBUG] 开始处理Logo - ID: %s, scanAsset[logo]: %+v", id, scanAsset["logo"])
				if logoData, ok := scanAsset["logo"]; ok {
					if logoMap, ok := logoData.(map[string]interface{}); ok {
						// 只有当Logo数据有效时才赋值，避免空值覆盖已有数据
						hashValue := logoMap["hash"]
						contentValue := safeStringValue(logoMap["content"])

						if hashValue != nil || contentValue != "" {
							newAsset.Logo.Hash = hashValue
							newAsset.Logo.Content = contentValue
							log.WithContextInfof(ctx, "[LOGO_DEBUG] Logo处理成功 - ID: %s, Hash: %v, Content: %s", id, hashValue, contentValue)
							log.WithContextInfof(ctx, "[LOGO_DEBUG] newAsset.Logo设置后 - ID: %s, newAsset.Logo.Hash: %v, newAsset.Logo.Content: %s", id, newAsset.Logo.Hash, newAsset.Logo.Content)
						} else {
							log.WithContextInfof(ctx, "[LOGO_DEBUG] Logo数据为空，跳过赋值 - ID: %s, Hash: %v, Content: %s", id, hashValue, contentValue)
						}
					} else {
						log.WithContextInfof(ctx, "[LOGO_DEBUG] Logo数据类型转换失败 - ID: %s, logoData: %+v", id, logoData)
					}
				} else {
					log.WithContextInfof(ctx, "[LOGO_DEBUG] scanAsset中无logo字段 - ID: %s", id)
				}

				// 处理Reason数组 - 兼容多种数据类型
				if reasonData, ok := scanAsset["reason"]; ok {
					var reasons []foradar_assets.AssetReason

					// 尝试不同的类型转换
					switch v := reasonData.(type) {
					case []map[string]interface{}:
						// 原来的处理方式
						reasons = make([]foradar_assets.AssetReason, len(v))
						for i, reasonMap := range v {
							reasons[i] = foradar_assets.AssetReason{
								ID:              utils.GetIntValue(reasonMap["id"]),
								GroupID:         utils.GetIntValue(reasonMap["group_id"]),
								Source:          utils.GetIntValue(reasonMap["source"]),
								Type:            utils.GetIntValue(reasonMap["type"]),
								Content:         safeStringValue(reasonMap["content"]),
								ClueCompanyName: reasonMap["clue_company_name"],
							}
						}
					case []interface{}:
						// 新的处理方式 - 处理 []interface{} 类型
						reasons = make([]foradar_assets.AssetReason, 0, len(v))
						for _, item := range v {
							if reasonMap, ok := item.(map[string]interface{}); ok {
								reason := foradar_assets.AssetReason{
									ID:              utils.GetIntValue(reasonMap["id"]),
									GroupID:         utils.GetIntValue(reasonMap["group_id"]),
									Source:          utils.GetIntValue(reasonMap["source"]),
									Type:            utils.GetIntValue(reasonMap["type"]),
									Content:         safeStringValue(reasonMap["content"]),
									ClueCompanyName: reasonMap["clue_company_name"],
								}
								reasons = append(reasons, reason)
							}
						}
					case []foradar_assets.AssetReason:
						// 如果已经是正确的类型，直接使用
						reasons = v
					default:
						log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]无法解析reason数据，类型: %T, 值: %+v", reasonData, reasonData)
					}

					if len(reasons) > 0 {
						newAsset.Reason = reasons
					}
				}

				// 处理规则标签
				// 首先检查 rule_tags 的实际类型和内容
				if ruleTagsRaw, exists := scanAsset["rule_tags"]; exists {
					log.WithContextInfof(ctx, "[DEBUG] rule_tags 存在，类型: %T, 值: %+v", ruleTagsRaw, ruleTagsRaw)

					// 尝试多种可能的类型转换
					if ruleTags, ok := ruleTagsRaw.([]interface{}); ok {
						log.WithContextInfof(ctx, "[DEBUG] 成功转换为 []interface{}，长度: %d", len(ruleTags))
						ruleTagsArray := make([]foradar_assets.RuleTag, 0, len(ruleTags))
						for i, tag := range ruleTags {
							if tagMap, ok := tag.(map[string]interface{}); ok {
								ruleTag := foradar_assets.RuleTag{
									CnProduct:        safeStringValue(tagMap["cn_product"]),
									RuleID:           safeStringValue(tagMap["rule_id"]), // 注意：这里是 RuleID，不是 RuleId
									Product:          safeStringValue(tagMap["product"]),
									CnCategory:       safeStringValue(tagMap["cn_category"]),
									Level:            safeStringValue(tagMap["level"]),
									ParentCategory:   safeStringValue(tagMap["parent_category"]),
									Softhard:         safeStringValue(tagMap["softhard"]),
									Company:          safeStringValue(tagMap["company"]),
									CnParentCategory: safeStringValue(tagMap["cn_parent_category"]),
									Category:         safeStringValue(tagMap["category"]),
									CnCompany:        safeStringValue(tagMap["cn_company"]),
								}
								ruleTagsArray = append(ruleTagsArray, ruleTag)
								log.WithContextInfof(ctx, "[DEBUG] 处理第 %d 个 rule_tag，rule_id: %s, product: %s", i+1, ruleTag.RuleID, ruleTag.Product)
							} else {
								log.WithContextInfof(ctx, "[DEBUG] 第 %d 个 rule_tag 不是 map[string]interface{}，类型: %T, 值: %+v", i+1, tag, tag)
							}
						}
						newAsset.RuleTags = ruleTagsArray
						log.WithContextInfof(ctx, "[DEBUG] 最终 RuleTags 数量: %d", len(newAsset.RuleTags))
					} else if ruleTagsSlice, ok := ruleTagsRaw.([]foradar_assets.RuleTag); ok {
						// 如果已经是正确的类型，直接使用
						log.WithContextInfof(ctx, "[DEBUG] rule_tags 已经是 []foradar_assets.RuleTag 类型，长度: %d", len(ruleTagsSlice))
						newAsset.RuleTags = ruleTagsSlice
					} else if ruleTagsSubdomain, ok := ruleTagsRaw.([]fofaee_subdomain.Rule); ok {
						// 如果是 fofaee_subdomain.Rule 类型，需要转换
						log.WithContextInfof(ctx, "[DEBUG] rule_tags 是 []fofaee_subdomain.Rule 类型，长度: %d", len(ruleTagsSubdomain))
						ruleTagsArray := make([]foradar_assets.RuleTag, 0, len(ruleTagsSubdomain))
						for i, rule := range ruleTagsSubdomain {
							ruleTag := foradar_assets.RuleTag{
								CnProduct:        rule.CnProduct,
								RuleID:           rule.RuleId, // 注意字段名差异
								Product:          rule.Product,
								CnCategory:       rule.CnCategory,
								Level:            rule.Level,
								ParentCategory:   rule.ParentCategory,
								Softhard:         rule.SoftHard, // 注意字段名差异
								Company:          rule.Company,
								CnParentCategory: rule.CnParentCategory,
								Category:         rule.Category,
								CnCompany:        rule.CnCompany,
							}
							ruleTagsArray = append(ruleTagsArray, ruleTag)
							log.WithContextInfof(ctx, "[DEBUG] 转换第 %d 个 fofaee_subdomain.Rule，rule_id: %s, product: %s", i+1, ruleTag.RuleID, ruleTag.Product)
						}
						newAsset.RuleTags = ruleTagsArray
					} else {
						log.WithContextInfof(ctx, "[DEBUG] rule_tags 类型不匹配，实际类型: %T, 值: %+v", ruleTagsRaw, ruleTagsRaw)
					}
				} else {
					log.WithContextInfof(ctx, "[DEBUG] scanAsset 中不存在 rule_tags 字段")
				}

				// 处理证书信息
				if certData, ok := scanAsset["cert"]; ok && certData != nil {
					if certMap, ok := certData.(map[string]interface{}); ok {
						newAsset.Cert = foradar_assets.AssetCert{
							Sn:         safeStringValue(certMap["sn"]),
							IssuerCn:   safeStringValue(certMap["issuer_cn"]),
							SubjectCn:  safeStringValue(certMap["subject_cn"]),
							Raw:        safeStringValue(certMap["raw"]),
							IsValid:    getBoolValue(certMap["is_valid"]),
							V:          safeStringValue(certMap["v"]),
							CertDate:   certMap["cert_date"],
							NotBefore:  certMap["not_before"],
							NotAfter:   certMap["not_after"],
							SigAlth:    safeStringValue(certMap["sig_alth"]),
							SubjectKey: safeStringValue(certMap["subject_key"]),
							ValidType:  safeStringValue(certMap["valid_type"]),
						}

						// 处理数组字段
						if issuerCns, ok := certMap["issuer_cns"]; ok {
							if cnsSlice, ok := issuerCns.([]string); ok {
								strs := make([]string, 0, len(cnsSlice))
								for _, cn := range cnsSlice {
									strs = append(strs, safeStringValue(cn))
								}
								newAsset.Cert.IssuerCns = strs
							}
						}
						if issuerOrg, ok := certMap["issuer_org"]; ok {
							if orgSlice, ok := issuerOrg.([]string); ok {
								strs := make([]string, 0, len(orgSlice))
								for _, org := range orgSlice {
									strs = append(strs, safeStringValue(org))
								}
								newAsset.Cert.IssuerOrg = strs
							}
						}

						if certNum, ok := certMap["cert_num"]; ok {
							newAsset.Cert.CertNum = utils.GetIntValue(certNum)
						}
					}
				}

				// 处理地理位置信息
				if geo, ok := scanAsset["geo"]; ok && geo != nil {
					if geoMap, ok := geo.(map[string]interface{}); ok {
						newAsset.Geo = foradar_assets.AssetGeo{
							Continent: safeStringValue(geoMap["continent"]),
							Country:   safeStringValue(geoMap["country"]),
							City:      safeStringValue(geoMap["city"]),
							Province:  safeStringValue(geoMap["province"]),
							District:  safeStringValue(geoMap["district"]),
							As:        safeStringValue(geoMap["as"]),
							Asn:       safeNumericValue(geoMap["asn"]), // 安全处理数字类型
							AsName:    safeStringValue(geoMap["as_name"]),
							Org:       safeStringValue(geoMap["org"]),
							Isp:       safeStringValue(geoMap["isp"]),
							Zip:       safeStringValue(geoMap["zip"]),
							Lon:       safeNumericValue(geoMap["lon"]), // 安全处理数字类型
							Lat:       safeNumericValue(geoMap["lat"]), // 安全处理数字类型
						}
					}
				}

				// 处理ICP信息
				if icpData, ok := scanAsset["icp"]; ok && icpData != nil {
					if icpMap, ok := icpData.(map[string]interface{}); ok {
						newAsset.Icp.No = safeStringValue(icpMap["no"])
						newAsset.Icp.Type = safeStringValue(icpMap["type"])
						newAsset.Icp.CompanyName = safeStringValue(icpMap["company_name"])
						newAsset.Icp.Date = icpMap["date"]
					}
				}

				// 使用ES的Create方法插入
				log.WithContextInfof(ctx, "[LOGO_DEBUG] 准备插入ES - ID: %s, Logo.Hash: %v, Logo.Content: %s", newAsset.ID, newAsset.Logo.Hash, newAsset.Logo.Content)

				// 检查ES中是否已存在该ID的文档
				existingAssets2, err2 := foradarAssetModel.FindByIpPort(ctx, int(s.Task.UserId), []elastic.IpPort{{
					Ip:      newAsset.Ip,
					Port:    cast.ToString(newAsset.Port),
					PortInt: cast.ToInt(newAsset.Port),
				}})
				if err2 == nil && len(existingAssets2) > 0 {
					log.WithContextInfof(ctx, "[LOGO_DEBUG] 发现相同IP+端口的文档数量: %d", len(existingAssets2))
					for _, existing := range existingAssets2 {
						log.WithContextInfof(ctx, "[LOGO_DEBUG] 现有文档 - ID: %s, Logo.Hash: %v, Logo.Content: %s",
							existing.ID, existing.Logo.Hash, existing.Logo.Content)
						if existing.ID == newAsset.ID {
							log.WithContextInfof(ctx, "[LOGO_DEBUG] 警告：ES中已存在相同ID的文档！这可能导致覆盖")
						}
					}
				}

				_, _, err := foradarAssetModel.Create(ctx, []*foradar_assets.ForadarAsset{newAsset})
				if err != nil {
					log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]插入ForadarAsset到ES失败: %v", err)
				} else {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]插入ForadarAsset到ES成功: id=%s, ip=%s, port=%d, protocol=%s",
						id, s.completeIPV6(subAsset.IP), subAsset.Port, subAsset.Protocol)
				}
			} else {
				// 更新资产数据
				asset := existingAssets[0]

				// 对应PHP: $scanAsset = array_merge($scanAsset, Arr::only($asset->toArray(), ['status','type','created_at']));
				if asset.Status != 0 {
					scanAsset["status"] = asset.Status
				}
				if asset.Type != 0 {
					scanAsset["type"] = asset.Type
				}
				if asset.CreatedAt != "" {
					scanAsset["created_at"] = asset.CreatedAt
				}

				// 对应PHP: if ($asset->status == ForadarAssets::STATUS_UPLOAD) { $scanAsset['status'] = ForadarAssets::STATUS_CLAIMED; }
				if asset.Status == fofaee_assets.STATUS_UPLOAD {
					scanAsset["status"] = fofaee_assets.STATUS_CLAIMED
				}
				// 对应PHP: if ($this->task->asset_type == \App\Models\MySql\Task::TASK_ASSET_MANUAL) { $scanAsset['status'] = ForadarAssets::STATUS_CLAIMED; }
				if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
					scanAsset["status"] = fofaee_assets.STATUS_CLAIMED
				}

				// 设置任务ID和检测任务ID - 对应PHP: $scanAsset['task_id'] = $this->getTaskId($asset);
				// 对应PHP中getTaskId的逻辑：获取现有任务ID并添加当前任务ID
				existingTaskIds := asset.TaskID
				currentTaskId := int(s.Task.ID)
				found := false
				for _, tid := range existingTaskIds {
					if tid == currentTaskId {
						found = true
						break
					}
				}
				if !found {
					existingTaskIds = append(existingTaskIds, currentTaskId)
				}
				scanAsset["task_id"] = existingTaskIds

				// 对应PHP: $scanAsset['detect_assets_tasks_id'] = $this->getDetectAssetsTasksId($asset);
				scanAsset["detect_assets_tasks_id"] = asset.DetectAssetsTasksID

				// 更新标签和规则标签 - 对应PHP: $scanAsset['tags'] = array_values(array_unique(array_merge(($scanAsset['tags'] ?? []), ($asset->tags ?? []))));
				if currentTags, ok := scanAsset["tags"].([]interface{}); ok {
					// 这里需要从asset.Tags中获取现有标签并合并，但asset结构中可能没有Tags字段
					// 按PHP逻辑，如果有现有标签，进行合并去重
					tagMap := make(map[string]bool)
					uniqueTags := []interface{}{}
					for _, tag := range currentTags {
						if tagStr, ok := tag.(string); ok {
							if !tagMap[tagStr] {
								tagMap[tagStr] = true
								uniqueTags = append(uniqueTags, tag)
							}
						}
					}
					scanAsset["tags"] = uniqueTags
				}

				// 对应PHP: $scanAsset['rule_tags'] = $allAssets->where('ip', $subAsset->ip)->where('port', $subAsset->port)->where('protocol', $subAsset->protocol)->pluck('rule_tags')->collapse()->unique()->filter()->values()->toArray() ?? [];
				var allRuleTags []interface{}
				log.WithContextInfof(ctx, "[DEBUG] 开始合并 rule_tags，allAssets 数量: %d，目标 IP: %s, Port: %d, Protocol: %s", len(allAssets), subAsset.IP, subAsset.Port, subAsset.Protocol)
				for _, a := range allAssets {
					if a.IP == subAsset.IP && a.Port == subAsset.Port && a.Protocol == subAsset.Protocol {
						log.WithContextInfof(ctx, "[DEBUG] 找到匹配的资产，RuleTags 数量: %d", len(a.RuleTags))
						for _, rule := range a.RuleTags {
							allRuleTags = append(allRuleTags, map[string]interface{}{
								"rule_id":            rule.RuleId, // 注意：fofaee_subdomain.Rule 使用 RuleId 字段
								"cn_product":         rule.CnProduct,
								"product":            rule.Product,
								"category":           rule.Category,
								"level":              rule.Level,
								"softhard":           rule.SoftHard, // 注意：fofaee_subdomain.Rule 使用 SoftHard 字段
								"cn_category":        rule.CnCategory,
								"company":            rule.Company,
								"cn_company":         rule.CnCompany,
								"parent_category":    rule.ParentCategory,
								"cn_parent_category": rule.CnParentCategory,
							})
						}
					}
				}
				if len(allRuleTags) > 0 {
					scanAsset["rule_tags"] = allRuleTags
					log.WithContextInfof(ctx, "[DEBUG] 更新模式：设置 rule_tags，数量: %d", len(allRuleTags))
				} else {
					// 对应PHP: if (empty($scanAsset['rule_tags'])) { unset($scanAsset['rule_tags']); }
					delete(scanAsset, "rule_tags")
					log.WithContextInfof(ctx, "[DEBUG] 更新模式：删除 rule_tags（为空）")
				}

				// CDN检测 - 对应PHP的完整CDN检测逻辑
				// 对应PHP: if(!($scanAsset['is_ipv6'] ?? false)){ $scanAsset['is_cdn'] = isCdn($scanAsset['ip']); }
				if isIPv6, ok := scanAsset["is_ipv6"].(bool); !ok || !isIPv6 {
					ip := safeStringValue(scanAsset["ip"])
					log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] 开始CDN检测 - IP: %s", ip)
					isCdnResult := network.IsCdn(scanAsset["ip"], 0)
					scanAsset["is_cdn"] = isCdnResult
					log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] CDN检测完成 - IP: %s, 检测结果: %v", ip, isCdnResult)
					// 如果检测为CDN，立即设置缓存
					if isCdnResult {
						s.setCdnCache(ctx, scanAsset)
					}
				}
				// 对应PHP: if(empty($scanAsset['is_cdn'])){ $scanAsset['is_cdn'] = isCdn([($scanAsset['cname'] ?? '')],1); }
				if !getBoolValue(scanAsset["is_cdn"]) {
					if cname, ok := scanAsset["cname"]; ok {
						if cnameSlice, ok := cname.([]string); ok {
							isCdnResult := network.IsCdn(cnameSlice, 1)
							scanAsset["is_cdn"] = isCdnResult
							// 如果检测为CDN，立即设置缓存
							if isCdnResult {
								s.setCdnCache(ctx, scanAsset)
							}
						}
					}
				}
				// 对应PHP: if(empty($scanAsset['is_cdn'])){ if(($scanAsset['rule_tags'] ?? [])){ $scanAsset['is_cdn'] = ruleIsCdn($scanAsset['rule_tags']); } }
				if !getBoolValue(scanAsset["is_cdn"]) {
					if ruleTags, ok := scanAsset["rule_tags"]; ok {
						if ruleTagsSlice, ok := ruleTags.([]interface{}); ok {
							isCdnResult := network.RuleIsCdn(ruleTagsSlice)
							scanAsset["is_cdn"] = isCdnResult
							// 如果检测为CDN，立即设置缓存
							if isCdnResult {
								s.setCdnCache(ctx, scanAsset)
							}
						}
					}
				}
				// 对应PHP: if(empty($scanAsset['is_cdn']) && ($scanAsset['cert']['subject_key'] ?? '')){ $cdnNum = count(explode('cdn',($scanAsset['cert']['subject_key'] ?? ''))); if($cdnNum > 1){ $scanAsset['is_cdn'] = true; } }
				if !getBoolValue(scanAsset["is_cdn"]) {
					if certData, ok := scanAsset["cert"]; ok && certData != nil {
						if certMap, ok := certData.(map[string]interface{}); ok {
							if subjectKey, exists := certMap["subject_key"]; exists {
								subjectKeyStr := safeStringValue(subjectKey)
								if subjectKeyStr != "" {
									// 修正：使用PHP相同的逻辑：count(explode('cdn', $str)) > 1
									cdnParts := strings.Split(strings.ToLower(subjectKeyStr), "cdn")
									if len(cdnParts) > 1 {
										scanAsset["is_cdn"] = true
										// 如果检测为CDN，立即设置缓存
										s.setCdnCache(ctx, scanAsset)
									}
								}
							}
						}
					}
				}

				// 处理geo信息
				if geoData, ok := scanAsset["geo"]; ok {
					if geoMap, ok := geoData.(map[string]interface{}); ok {
						scanAsset["geo"] = geoMap
					}
				}

				// 证书处理 - 对应PHP: if($scanAsset['status'] == ForadarAssets::STATUS_CLAIMED){ CertAssetService::dealCertAsset($scanAsset,$this->task->detect_assets_tasks_id ?? null); }
				status := utils.GetIntValue(scanAsset["status"])
				if status == fofaee_assets.STATUS_CLAIMED {
					// 构建证书处理需要的ForadarAsset结构体，对应PHP的弱类型转换
					certAsset := &cert.ForadarAsset{
						ID:        safeStringValue(scanAsset["id"]),
						IP:        safeStringValue(scanAsset["ip"]),
						UserID:    s.Task.UserId,
						CompanyID: uint64(s.Task.CompanyId),
						Status:    status,
						Type:      utils.GetIntValue(scanAsset["type"]),
					}

					// 类型转换处理
					if port, ok := scanAsset["port"]; ok {
						certAsset.Port = utils.GetIntValue(port)
					}
					if protocol, ok := scanAsset["protocol"]; ok {
						certAsset.Protocol = safeStringValue(protocol)
					}
					if subdomain, ok := scanAsset["subdomain"]; ok {
						certAsset.Subdomain = safeStringValue(subdomain)
					}
					if domain, ok := scanAsset["domain"]; ok {
						certAsset.Domain = safeStringValue(domain)
					}

					// 处理证书信息
					if certData, ok := scanAsset["cert"]; ok && certData != nil {
						if certMap, ok := certData.(map[string]interface{}); ok {
							certAsset.Cert = &cert.CertInfo{
								SN:        safeStringValue(certMap["sn"]),
								IssuerCN:  safeStringValue(certMap["issuer_cn"]),
								SubjectCN: safeStringValue(certMap["subject_cn"]),
								Raw:       safeStringValue(certMap["raw"]),
								IsValid:   getBoolValue(certMap["is_valid"]),
								Version:   safeStringValue(certMap["v"]),
								CertDate:  safeStringValue(certMap["cert_date"]),
								NotBefore: safeStringValue(certMap["not_before"]),
								NotAfter:  safeStringValue(certMap["not_after"]),
							}

							// 处理数组字段
							if issuerCns, ok := certMap["issuer_cns"]; ok {
								if cnsSlice, ok := issuerCns.([]string); ok {
									strs := make([]string, 0, len(cnsSlice))
									for _, cn := range cnsSlice {
										strs = append(strs, safeStringValue(cn))
									}
									certAsset.Cert.IssuerCNs = strs
								}
							}
							if issuerOrg, ok := certMap["issuer_org"]; ok {
								if orgSlice, ok := issuerOrg.([]string); ok {
									strs := make([]string, 0, len(orgSlice))
									for _, org := range orgSlice {
										strs = append(strs, safeStringValue(org))
									}
									certAsset.Cert.IssuerOrg = strs
								}
							}
							if subjectOrg, ok := certMap["subject_org"]; ok {
								if orgSlice, ok := subjectOrg.([]string); ok {
									strs := make([]string, 0, len(orgSlice))
									for _, org := range orgSlice {
										strs = append(strs, safeStringValue(org))
									}
									certAsset.Cert.SubjectOrg = strs
								}
							}

							// 其他字段
							if certNum, ok := certMap["cert_num"]; ok {
								certAsset.Cert.CertNum = utils.GetIntValue(certNum)
							}
							if sigAlth, ok := certMap["sig_alth"]; ok {
								certAsset.Cert.SigAlth = safeStringValue(sigAlth)
							}
							if subjectKey, ok := certMap["subject_key"]; ok {
								certAsset.Cert.SubjectKey = safeStringValue(subjectKey)
							}
							if validType, ok := certMap["valid_type"]; ok {
								certAsset.Cert.ValidType = safeStringValue(validType)
							}
						}
					}

					// 调用证书处理服务
					certService := cert.NewCertAssetService()
					var detectTaskID *uint64
					if s.Task.DetectAssetsTasksId > 0 {
						detectTaskIDValue := uint64(s.Task.DetectAssetsTasksId)
						detectTaskID = &detectTaskIDValue
					}
					err := certService.DealCertAsset(ctx, certAsset, detectTaskID)
					if err != nil {
						log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]处理证书资产失败: %v", err)
					}
				}

				// 合并推荐理由 - 对应PHP中的推荐理由合并逻辑
				// 对应PHP: $hasHeBingReason = 0; if($scanAsset['reason']){ $originReason = $asset->reason ?? []; if($originReason){ $reasonIdArr = collect($scanAsset['reason'])->pluck('id')->toArray(); foreach ($originReason as $or){ if(!in_array($or['id'],$reasonIdArr)){ $scanAsset['reason'][] = $or; $hasHeBingReason = 1; } } } }else{ $originReason = $asset->reason ?? []; if($originReason){ $scanAsset['reason'] = $originReason; $hasHeBingReason = 2; } }
				hasHeBingReason := 0
				if reasonData, ok := scanAsset["reason"]; ok && reasonData != nil {
					// 当前有推荐理由
					if reasonSlice, ok := reasonData.([]interface{}); ok {
						// 获取原有推荐理由
						if len(asset.Reason) > 0 {
							// 提取当前推荐理由的ID
							reasonIdMap := make(map[int]bool)
							for _, reason := range reasonSlice {
								if reasonMap, ok := reason.(map[string]interface{}); ok {
									if id, exists := reasonMap["id"]; exists {
										if idInt := utils.GetIntValue(id); idInt > 0 {
											reasonIdMap[idInt] = true
										}
									}
								}
							}
							// 合并不重复的原有推荐理由
							for _, originReason := range asset.Reason {
								originReasonMap := map[string]interface{}{
									"id":                originReason.ID,
									"group_id":          originReason.GroupID,
									"content":           originReason.Content,
									"type":              originReason.Type,
									"clue_company_name": safeClueCompanyName(originReason.ClueCompanyName),
									"source":            originReason.Source,
								}
								if !reasonIdMap[originReason.ID] {
									reasonSlice = append(reasonSlice, originReasonMap)
									hasHeBingReason = 1
								}
							}
							scanAsset["reason"] = reasonSlice
						}
					}
				} else {
					// 当前没有推荐理由，使用原有的
					if len(asset.Reason) > 0 {
						var reasonSlice []interface{}
						for _, originReason := range asset.Reason {
							reasonSlice = append(reasonSlice, map[string]interface{}{
								"id":                originReason.ID,
								"group_id":          originReason.GroupID,
								"content":           originReason.Content,
								"type":              originReason.Type,
								"clue_company_name": safeClueCompanyName(originReason.ClueCompanyName),
								"source":            originReason.Source,
							})
						}
						scanAsset["reason"] = reasonSlice
						hasHeBingReason = 2
					}
				}
				if hasHeBingReason > 0 {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]writeIpAndPortAsset ip端口资产维度合并了推荐理由 taskId:%d, id:%s, hasHeBingReason:%d", s.Task.ID, asset.ID, hasHeBingReason)
				}

				// 推荐扫描疑似资产状态处理 - 对应PHP: if ($this->task->asset_type == \App\Models\MySql\Task::TASK_ASSET_CLOUD) { if($asset['is_user_sign_unsure'] == 1 && ($asset['status'] == ForadarAssets::STATUS_DEFAULT) && ($scanAsset['status'] != ForadarAssets::STATUS_DEFAULT)){ LogService::info('writeIpAndPortAsset-ip端口维度数据','更新该资产状态为疑似状态，因为这个资产之前人工标记为疑似',['assets_id'=>$id]); $scanAsset['status'] = ForadarAssets::STATUS_DEFAULT; } }
				if s.Task.AssetType == scan_task.TASK_ASSET_CLOUD {
					// 修正：直接使用FofaeeAssets结构体中的IsUserSignUnsure字段，对应PHP逻辑
					oldAssetModel := fofaee_assets.NewFofaeeAssetsModel()
					oldAssetData := oldAssetModel.FindFullById(ctx, id)
					if oldAssetData != nil {
						oldStatus := utils.GetIntValue(oldAssetData.Status)
						scanStatus := utils.GetIntValue(scanAsset["status"])

						if oldAssetData.IsUserSignUnsure == 1 && oldStatus == fofaee_assets.STATUS_DEFAULT && scanStatus != fofaee_assets.STATUS_DEFAULT {
							log.WithContextInfof(ctx, "[ScanForadarAssetHandler]writeIpAndPortAsset-ip端口维度数据 更新该资产状态为疑似状态，因为这个资产之前人工标记为疑似: assets_id=%s", id)
							scanAsset["status"] = fofaee_assets.STATUS_DEFAULT
						}
					}
				}

				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]writeIpAndPortAsset 更新IP+Port数据,taskId:%d, id:%s, status=%d",
					s.Task.ID, asset.ID, utils.GetIntValue(scanAsset["status"]))

				// 更新数据库 - 对应PHP: ForadarAssets::query()->where('_id', $id)->update($scanAsset);
				// 使用ES的更新方法，根据mapping修正字段名
				updateData := map[string]any{
					"id":                     id,
					"task_id":                scanAsset["task_id"],
					"detect_assets_tasks_id": scanAsset["detect_assets_tasks_id"],
					"tags":                   scanAsset["tags"],
					"rule_tags":              scanAsset["rule_tags"],
					"is_cdn":                 scanAsset["is_cdn"],
					"status":                 scanAsset["status"],
					"reason":                 scanAsset["reason"],            // 添加推荐理由字段
					"clue_company_name":      scanAsset["clue_company_name"], // 添加企业名称字段
					"cname":                  scanAsset["cname"],             // 添加CNAME字段
					"geo":                    scanAsset["geo"],               // 添加地理位置字段
					"icp":                    scanAsset["icp"],               // 添加ICP字段
					"online_state":           scanAsset["online_state"],
					"updated_at":             time.Now().Format("2006-01-02 15:04:05"),
				}

				updateData["logo"] = scanAsset["logo"] // 添加Logo字段

				err := foradarAssetModel.UpdateWithMap(ctx, []map[string]any{updateData})
				if err != nil {
					log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新ForadarAsset到ES失败: %v", err)
				} else {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]更新ForadarAsset到ES成功: id=%s, ip=%s, port=%d, protocol=%s",
						id, s.completeIPV6(subAsset.IP), subAsset.Port, subAsset.Protocol)
				}
			}

			// 爬虫频次限制和协议检查 - 对应PHP: if (!$this->isAllPort) { $crawlerIpNum = Cache::get('crawler_num:' . $this->task->id . ":" . $scanAsset['ip']); if ($crawlerIpNum > config('app.crawler_num')) { LogService::info('ScanCrawlerMqJob', '退出第4步,当前ip的端口已经爬虫超过200次，跳过', ['taskId' => $this->task->id, 'ip' => $scanAsset['ip']]); } else { if (($scanAsset['protocol'] == 'http' || $scanAsset['protocol'] == 'https' || $scanAsset['protocol'] == 'tls') && ($scanAsset['url'] ?? '')) { ScanCrawlerMqJob::publish($id, $this->task->id, $this->task->web_logo_switch, $crawlerProgress); } } } else { LogService::info('ScanForadarAssetJob', 'ScanForadarAssetJob-全端口扫描，不进行爬虫任务下发', ['task_id' => $this->task->id, 'isAllPort' => $this->isAllPort]); }
			if !isAllPort {
				// 因为有的ip开了上千个端口，这种都去爬虫不现实，就一个任务的单个ip最多爬取200次，再多了不进行爬取
				crawlerCacheKey := fmt.Sprintf("crawler_num:%d:%s", s.Task.ID, scanAsset["ip"])
				var crawlerIpNum int
				redis.GetCache(crawlerCacheKey, &crawlerIpNum)
				crawlerLimit := cfg.LoadAPP().CrawlerNum // 对应config('app.crawler_num')

				if crawlerIpNum > crawlerLimit {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ScanCrawlerMqJob 退出第4步,当前ip的端口已经爬虫超过%d次，跳过: taskId=%d, ip=%s", crawlerLimit, s.Task.ID, scanAsset["ip"])
				} else {
					// 只需要http和https协议进行爬虫
					protocol := safeStringValue(scanAsset["protocol"])
					url := safeStringValue(scanAsset["url"])
					if (protocol == "http" || protocol == "https" || protocol == "tls") && url != "" {
						// 增加爬虫计数器
						redis.SetCache(crawlerCacheKey, 24*time.Hour, crawlerIpNum+1)

						// 对应PHP: ScanCrawlerMqJob::publish($id, $this->task->id, $this->task->web_logo_switch, $crawlerProgress);
						s.publish(ctx, id, uint64(s.Task.ID), s.Task.WebLogoSwitch, crawlerProgress)
						log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ScanForadarAssetJob ScanForadarAssetJobCrawler-下发爬虫了: task_id=%d, isAllPort=%t, id=%s", s.Task.ID, isAllPort, id)
					} else {
						log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ScanForadarAssetJob ScanForadarAssetJobCrawler-不进行爬虫下发，因为协议不符合，或者url为空: task_id=%d, isAllPort=%t, id=%s, protocol=%s, url=%s", s.Task.ID, isAllPort, id, protocol, url)
					}
				}
			} else {
				// 如果是全端口扫描，不进行爬虫了
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ScanForadarAssetJob ScanForadarAssetJob-全端口扫描，不进行爬虫任务下发: task_id=%d, isAllPort=%t", s.Task.ID, isAllPort)
			}
		}()
	}
}
func (s *ScanForadarAsset) publish(ctx context.Context, id string, taskId uint64, webLogoSwitch int, crawlerProgress float64) {
	// 使用连接池获取通道
	chWrapper, err := mq_pool.GetChannel()
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取MQ通道失败: %v", err)
		return
	}
	defer chWrapper.Close() // 自动将连接放回池中

	// declare exchange (topic)
	if err := chWrapper.ExchangeDeclare(
		"topic.scan.exchange", "topic", true, false, false, false, nil,
	); err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]exchange.declare: %+v", err)
		return
	}

	// declare queue
	args := amqp.Table{
		"x-max-priority": int32(10), // 与现有队列一致
		//"x-expires":      60000,
	}
	q, err := chWrapper.QueueDeclare("queue.scan.crawler", true, false, false, false, args)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]queue.declare: %+v", err)
		return
	}

	// bind
	if err := chWrapper.QueueBind(q.Name, "scan.crawler", "topic.scan.exchange", false, nil); err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]queue.bind: %+v", err)
		return
	}
	payload := []interface{}{
		id,
		taskId,
		webLogoSwitch,
		crawlerProgress,
	}
	body, err := json.Marshal(payload)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]json marshal: %+v", err)
		return
	}
	if err := chWrapper.PublishWithContext(
		ctx,
		"topic.scan.exchange", "scan.crawler", false, false,
		amqp.Publishing{
			Body:         body,
			DeliveryMode: amqp.Transient,
			Priority:     10,
		},
	); err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]publish: %w", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]published id=%s taskId=%d webLogoSwitch=%t crawlerProgress=%+v", id, taskId, webLogoSwitch, crawlerProgress)
}

// 生成资产ID
func (s *ScanForadarAsset) generateAssetId(ip string, port int, protocol, subdomain string) string {
	// 根据PHP代码: md5(completeIPV6($subAsset->ip).$subAsset->port.$subAsset->protocol.$getLiveSubdoamnin.$this->task->user_id)
	data := fmt.Sprintf("%s%d%s%s%d", ip, port, protocol, subdomain, s.Task.UserId)
	id := utils.Md5Hash(data)

	// [LOGO_DEBUG] 打印MD5计算详情
	log.Infof("[LOGO_DEBUG] generateAssetId - IP: %s, Port: %d, Protocol: %s, Subdomain: %s, UserId: %d", ip, port, protocol, subdomain, s.Task.UserId)
	log.Infof("[LOGO_DEBUG] generateAssetId - 原始字符串: '%s', MD5结果: %s", data, id)

	return id
}

var ipdbInstance *datx.City
var once sync.Once

func initDatxInstance() {
	once.Do(func() {
		filePath := cfg.LoadCommon().RootStorage + "/mydata4vipday3.datx"
		_, err := os.Stat(filePath)
		if err != nil {
			fmt.Printf("IpGetProvince -> 加载IP数据库失败,filePath:%s,error:%v", filePath, err)
			return
		}
		ipdbInstance, err = datx.NewCity(filePath)
		if err != nil {
			fmt.Printf("IpGetProvince -> 加载IP数据库失败,filePath:%s,error:%v", filePath, err)
			return
		}
	})
}

// IpGetProvince 获取IP地理位置信息，兼容PHP ipGetProvince函数
// 该方法依赖cgf配置，在utils中会有循环引用问题，所以这里单独实现
// 该方法依赖ipip.net的IP数据库，所以需要先初始化ipdbInstance，在init中实现
func IpGetProvince(ip string) *datx.Location {
	if ip == "" {
		return nil
	}

	isIP, isPrivateIP := utils.IsPrivateIP(ip)
	if !isIP {
		return nil
	}

	if isPrivateIP {
		result := &datx.Location{}
		result.Province = "内网"
		result.City = "内网"
		result.Country = "内网"
		return result
	} else {
		// 获取IP数据库实例
		if ipdbInstance == nil {
			// 延迟初始化
			initDatxInstance()
			if ipdbInstance == nil {
				return nil
			}
		}
		// 查询IP地理位置
		info, err := ipdbInstance.FindLocation(ip)
		if err != nil {
			fmt.Printf("IpGetProvince -> 查询IP地理位置失败,ip:%s,error:%v", ip, err)
			return nil
		}
		province := utils.RegionToProvince(info.Province)
		if province != "" {
			info.Province = province
		}
		return &info
	}
}

// 组装IP端口维度数据、
func (s *ScanForadarAsset) completeAssetData(ctx context.Context, id string, asset *UnifiedAsset, service *fofaee_service.FofaeeService, isOffOnline bool, offLinePorts []int) map[string]interface{} {
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeAssetData subassets的ip: %s", asset.IP)

	// 对应PHP: $recommendInfo = $this->getRecommendResultInfo($subAsset, $this->getFullUrl($subAsset->url ?? $subAsset->_id, $subAsset));
	url := s.getFullUrl(asset.Host, asset)
	recommendInfo := s.getRecommendResultInfo(map[string]interface{}{
		"ip":                   s.completeIPV6(asset.IP),
		"port":                 asset.Port,
		"protocol":             asset.Protocol,
		"url":                  asset.Host,
		"is_recommend":         asset.IsRecommendResult,
		"subdomain":            asset.Subdomain,
		"domain":               asset.Domain,
		"reason":               asset.RecommendReason, // 传入推荐理由字段
		"assets_source":        asset.AssetsSource,
		"assets_source_domain": asset.AssetsSourceDomain,
		"oneforall_source":     asset.OneforallSource,
		"is_cdn":               asset.IsCdn,
		"source_updated_at":    asset.SourceUpdatedAt,
	}, url)

	fullUrl := s.getFullUrl(url, asset)
	fullUrl = strings.Replace(fullUrl, fmt.Sprintf("%d_", s.Task.UserId), "", 1)

	// 对应PHP: $subdomain = getSubdomain($url);
	subdomain := utils.GetSubdomain(fullUrl)

	// 对应PHP: $domain = getTopDomain($url);
	domain := utils.GetTopDomain(fullUrl)

	// 构建geo信息
	geo := map[string]interface{}{
		"continent": "",
		"country":   "",
		"city":      "",
		"province":  "",
		"district":  "",
		"as":        "",
		"asn":       nil, // 数字字段应该是nil而不是空字符串
		"as_name":   "",
		"org":       "",
		"isp":       "",
		"zip":       "",
		"lon":       "",
		"lat":       "",
	}
	// 查询IP地理位置
	ipAddress := IpGetProvince(asset.IP)
	if ipAddress != nil {
		geo["country"] = ipAddress.Country
		geo["city"] = ipAddress.City
		geo["province"] = ipAddress.Province
		geo["isp"] = ipAddress.ISP
		geo["lon"] = ipAddress.Longitude
		geo["lat"] = ipAddress.Latitude
	}

	// 获取推荐理由 - 对应PHP: $recommendReason = object_get($recommendInfo, 'reason', []); $assetsSource = $recommendInfo->assets_source ?? null; $oneforallSource = $recommendInfo->oneforall_source ?? null; $assetsSourceDomain = ''; $sourceUpdateAt = $recommendInfo->source_updated_at ?? null;
	recommendReason := []interface{}{}
	var assetsSource int
	var oneforallSource interface{}
	assetsSourceDomain := ""
	sourceUpdateAt := ""

	if recommendInfo != nil {
		if recommendInfo.Reason != nil {
			// 转换RecommendReason类型为interface{}
			for _, reason := range recommendInfo.Reason {
				recommendReason = append(recommendReason, map[string]interface{}{
					"id":                reason.Id,
					"content":           reason.Content,
					"type":              reason.Type,
					"clue_company_name": safeClueCompanyName(reason.ClueCompanyName),
					"group_id":          reason.GroupId,
					"source":            reason.Source,
				})
			}

			// 将推荐理由设置到 UnifiedAsset 中
			asset.RecommendReason = make([]RecommendReason, 0, len(recommendInfo.Reason))
			for _, reason := range recommendInfo.Reason {
				asset.RecommendReason = append(asset.RecommendReason, RecommendReason{
					Id:              reason.Id,
					Type:            reason.Type,
					Content:         reason.Content,
					GroupId:         reason.GroupId,
					ClueCompanyName: reason.ClueCompanyName,
					Source:          reason.Source,
				})
			}
		}
		assetsSource = recommendInfo.AssetsSource
		oneforallSource = recommendInfo.OneforallSource // 对应PHP: $oneforallSource = $recommendInfo->oneforall_source ?? null;
		sourceUpdateAt = recommendInfo.SourceUpdatedAt
	}

	// 对应PHP: if(empty($recommendReason) && $this->task->flag){ ... }
	if len(recommendReason) == 0 && s.Task.Flag != "" {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]该资产推荐理由为空，判断是否是域名实时解析出来的ip: ip=%s, port=%d, flag=%s, task_id=%d", asset.IP, asset.Port, s.Task.Flag, s.Task.ID)

		// 对应PHP: if((bool)filter_var($subAsset->ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV6)){ $cacheIpToClue = Cache::get('ip_to_clue_domain:'.$this->task->id.":".md5($subAsset->ip)); }else{ $cacheIpToClue = Cache::get('ip_to_clue_domain:'.$this->task->id.":".$subAsset->ip); }
		var cacheKey string
		if utils.IsIPv6(asset.IP) {
			cacheKey = cache.GetCacheKey("ip_to_clue_domain", fmt.Sprintf("%d:%s", s.Task.ID, utils.Md5Hash(asset.IP)))
		} else {
			cacheKey = cache.GetCacheKey("ip_to_clue_domain", fmt.Sprintf("%d:%s", s.Task.ID, asset.IP))
		}

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]查询IP域名缓存: ip=%s, port=%d, task_id=%d, cache_key=%s, is_ipv6=%t", asset.IP, asset.Port, s.Task.ID, cacheKey, utils.IsIPv6(asset.IP))

		var cacheIpToClue string
		// 直接使用Redis原生Get方法获取字符串值，因为缓存中存储的是纯字符串而不是JSON
		cacheIpToClue, _ = redis.GetClient().Get(context.Background(), cacheKey).Result()

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]IP域名缓存查询结果: ip=%s, port=%d, task_id=%d, cache_key=%s, cache_value=%s", asset.IP, asset.Port, s.Task.ID, cacheKey, cacheIpToClue)

		if cacheIpToClue != "" {
			// 对应PHP: if (($this->detectTask->id ?? null) || ($this->organDetectTask->id ?? null)) { $clueGroupId = $this->detectTask->group_id ?? null; if (empty($clueGroupId)) { $clueGroupId = $this->organDetectTask->group_id ?? null; } if($clueGroupId){ ... } }
			if s.DetectTask != nil || s.OrganDetectTask != nil {
				var clueGroupId uint64
				if s.DetectTask != nil {
					clueGroupId = s.DetectTask.GroupId
				}
				if clueGroupId == 0 && s.OrganDetectTask != nil {
					clueGroupId = s.OrganDetectTask.GroupId
				}
				if clueGroupId != 0 {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]找推荐理由，推荐理由为空，找线索库里面匹配的: cacheIpToClue=%s, ip=%s, port=%d, task_id=%d, clueGroupId=%d", cacheIpToClue, asset.IP, asset.Port, s.Task.ID, clueGroupId)

					// 对应PHP: $recommendReason = Clue::query()->where('user_id',$this->task->user_id)->where('group_id',$clueGroupId)->where('content',$cacheIpToClue)->where('status','!=',Clue::CLUE_REFUSE_STATUS)->get(['id','group_id','source','type','content','clue_company_name'])->toArray();
					clueList, err := mysql.NewDSL[clues.Clue]().QueryByParams([][]interface{}{
						{"user_id", "=", s.Task.UserId},
						{"group_id", "=", clueGroupId},
						{"content", "=", cacheIpToClue},
						{"status", "!=", 2}, // clues.CLUE_REFUSE_STATUS
					})
					if err == nil && len(clueList) > 0 {
						for _, clue := range clueList {
							recommendReason = append(recommendReason, map[string]interface{}{
								"id":                clue.Id,
								"group_id":          clue.GroupId,
								"source":            clue.Source,
								"type":              clue.Type,
								"content":           clue.Content,
								"clue_company_name": safeClueCompanyName(clue.ClueCompanyName),
							})

							// 同时添加到 UnifiedAsset 的 RecommendReason 字段
							asset.RecommendReason = append(asset.RecommendReason, RecommendReason{
								Id:              int(clue.Id),
								Type:            clue.Type,
								Content:         clue.Content,
								GroupId:         int(clue.GroupId),
								ClueCompanyName: clue.ClueCompanyName,
								Source:          clue.Source,
							})
						}
						assetsSourceDomain = cacheIpToClue
						if assetsSource == 0 {
							assetsSource = recommend_result.ASSETS_SOURCE_DOMAIN_CLUE // RecommendResult::ASSETS_SOURCE_DOMAIN_CLUE
							assetsSourceDomain = cacheIpToClue
						}
						log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]推荐资产没找到reason，来线索匹配,线索匹配上了: ip=%s, port=%d, assetsSource=%d, task_id=%d, assetsSourceDomain=%s", asset.IP, asset.Port, assetsSource, s.Task.ID, assetsSourceDomain)
					} else {
						// 对应PHP: if(empty($recommendReason)){ $cacheIpToClue = getTopDomain($cacheIpToClue); $recommendReason = Clue::query()->where('user_id',$this->task->user_id)->where('group_id',$clueGroupId)->where('content',$cacheIpToClue)->where('status','!=',Clue::CLUE_REFUSE_STATUS)->get(['id','group_id','source','type','content','clue_company_name'])->toArray(); }
						topDomain := utils.GetTopDomain(cacheIpToClue)
						log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]直接域名匹配失败，尝试顶级域名匹配: ip=%s, port=%d, task_id=%d, original_domain=%s, top_domain=%s, clue_group_id=%d", asset.IP, asset.Port, s.Task.ID, cacheIpToClue, topDomain, clueGroupId)

						clueList2, err := mysql.NewDSL[clues.Clue]().QueryByParams([][]interface{}{
							{"user_id", "=", s.Task.UserId},
							{"group_id", "=", clueGroupId},
							{"content", "=", topDomain},
							{"status", "!=", 2}, // clues.CLUE_REFUSE_STATUS
						})
						if err == nil && len(clueList2) > 0 {
							for _, clue := range clueList2 {
								recommendReason = append(recommendReason, map[string]interface{}{
									"id":                clue.Id,
									"group_id":          clue.GroupId,
									"source":            clue.Source,
									"type":              clue.Type,
									"content":           clue.Content,
									"clue_company_name": safeClueCompanyName(clue.ClueCompanyName),
								})

								// 同时添加到 UnifiedAsset 的 RecommendReason 字段
								asset.RecommendReason = append(asset.RecommendReason, RecommendReason{
									Id:              int(clue.Id),
									Type:            clue.Type,
									Content:         clue.Content,
									GroupId:         int(clue.GroupId),
									ClueCompanyName: clue.ClueCompanyName,
									Source:          clue.Source,
								})
							}
							if assetsSource == 0 {
								assetsSource = recommend_result.ASSETS_SOURCE_DOMAIN_CLUE // RecommendResult::ASSETS_SOURCE_DOMAIN_CLUE
							}
							log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]推荐资产没找到reason，来线索匹配,线索匹配上了: ip=%s, port=%d, assetsSource=%d, assetsSourceDomain=%s , task_id=%d", asset.IP, asset.Port, assetsSource, assetsSourceDomain, s.Task.ID)
						} else {
							// 对应PHP: 优先取当时解析出来这个ip的子域名，子域名找不到就取主域名
							var cacheSubdomainKey string
							if utils.IsIPv6(asset.IP) {
								cacheSubdomainKey = cache.GetCacheKey("ip_to_clue_subdomain", fmt.Sprintf("%d:%s", s.Task.ID, utils.Md5Hash(asset.IP)))
							} else {
								cacheSubdomainKey = cache.GetCacheKey("ip_to_clue_subdomain", fmt.Sprintf("%d:%s", s.Task.ID, asset.IP))
							}

							log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]顶级域名也匹配失败，查询子域名缓存: ip=%s, port=%d, task_id=%d, subdomain_cache_key=%s", asset.IP, asset.Port, s.Task.ID, cacheSubdomainKey)

							var cacheIpToTableDomain string
							// 直接使用Redis原生Get方法获取字符串值，因为缓存中存储的是纯字符串而不是JSON
							cacheIpToTableDomain, _ = redis.GetClient().Get(context.Background(), cacheSubdomainKey).Result()

							log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]子域名缓存查询结果: ip=%s, port=%d, task_id=%d, subdomain_cache_key=%s, subdomain_cache_value=%s", asset.IP, asset.Port, s.Task.ID, cacheSubdomainKey, cacheIpToTableDomain)

							if cacheIpToTableDomain != "" {
								assetsSourceDomain = cacheIpToTableDomain
								log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]使用子域名缓存作为资产来源域名: ip=%s, port=%d, task_id=%d, assets_source_domain=%s", asset.IP, asset.Port, s.Task.ID, assetsSourceDomain)
							} else {
								assetsSourceDomain = cacheIpToClue
								log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]子域名缓存为空，使用原始域名作为资产来源域名: ip=%s, port=%d, task_id=%d, assets_source_domain=%s", asset.IP, asset.Port, s.Task.ID, assetsSourceDomain)
							}
							assetsSource = recommend_result.ASSETS_SOURCE_SCAN_ASSETS_DOMAIN // RecommendResult::ASSETS_SOURCE_SCAN_ASSETS_DOMAIN
							log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]推荐资产没找到reason，来线索匹配,线索也没匹配上啊: ip=%s, port=%d, assetsSource=%d , task_id=%d, assetsSourceDomain=%s", asset.IP, asset.Port, assetsSource, s.Task.ID, assetsSourceDomain)
						}
					}

					// 对应PHP: if(($this->detectTask->id ?? null) && empty($assetsSource)){ if($this->detectTask->scan_type == DetectAssetsTask::FULL_PORT){ $assetsSource = RecommendResult::ASSETS_DETECT_SCAN_FULL_PORT; } if($this->detectTask->scan_type == DetectAssetsTask::NORMAL_PORT){ $assetsSource = RecommendResult::ASSETS_DETECT_SCAN_NORMAL_PORT; } }
					if s.DetectTask != nil && assetsSource == 0 {
						if s.DetectTask.ScanType == 2 { // FULL_PORT
							assetsSource = recommend_result.ASSETS_DETECT_SCAN_FULL_PORT // RecommendResult::ASSETS_DETECT_SCAN_FULL_PORT
							log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]推荐资产没找到reason，认为是单位测绘的全端口扫描-1: ip=%s, port=%d, assetsSource=%d , task_id=%d", asset.IP, asset.Port, assetsSource, s.Task.ID)
						}
						if s.DetectTask.ScanType == 1 { // NORMAL_PORT
							assetsSource = recommend_result.ASSETS_DETECT_SCAN_NORMAL_PORT // RecommendResult::ASSETS_DETECT_SCAN_NORMAL_PORT
							log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]推荐资产没找到reason，认为是单位测绘的常用口扫描-1: ip=%s, port=%d, assetsSource=%d , task_id=%d", asset.IP, asset.Port, assetsSource, s.Task.ID)
						}
					}
				}
			}
		} else {
			// 对应PHP: 在这里面判断是否是单位测绘任务下发的常用端口扫描或者全部端口扫描任务
			if s.DetectTask != nil {
				if s.DetectTask.ScanType == 2 { // FULL_PORT
					assetsSource = recommend_result.ASSETS_DETECT_SCAN_FULL_PORT // RecommendResult::ASSETS_DETECT_SCAN_FULL_PORT
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]推荐资产没找到reason，认为是单位测绘的全端口扫描-2: ip=%s, port=%d, assetsSource=%d, task_id=%d", asset.IP, asset.Port, assetsSource, s.Task.ID)
				}
				if s.DetectTask.ScanType == 1 { // NORMAL_PORT
					assetsSource = recommend_result.ASSETS_DETECT_SCAN_NORMAL_PORT // RecommendResult::ASSETS_DETECT_SCAN_NORMAL_PORT
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]推荐资产没找到reason，认为是单位测绘的常用口扫描-2: ip=%s, port=%d, assetsSource=%d, task_id=%d", asset.IP, asset.Port, assetsSource, s.Task.ID)
				}
			}
			if assetsSource == 0 {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler][DEBUG-FIND-DOMAIN]找推荐理由，推荐理由为空，也找不到线索库里面的: cache_ip_to_clue=%s, ip=%s, port=%d, flag=%s, task_id=%d, cache_key=%s", cacheIpToClue, asset.IP, asset.Port, s.Task.Flag, s.Task.ID, cacheKey)
			}
		}
	}

	// 对应PHP: 'online_state' => ($isOffline || ($offLinePorts && in_array($subAsset->port, $offLinePorts))) ? 0 : 1, //在线情况
	onlineState := 1 // 默认在线
	if isOffOnline || (len(offLinePorts) > 0 && contains(offLinePorts, asset.Port)) {
		onlineState = 0
	}

	// 对应PHP: 'level' => $recommendInfo->level ?? null,
	var level interface{}
	var cloudName interface{}
	var isFakeAssets interface{}
	if recommendInfo != nil {
		level = recommendInfo.Level
		cloudName = recommendInfo.CloudName
		isFakeAssets = recommendInfo.IsFakeAssets
	}

	// 对应PHP: 处理网站状态码为整型 $statusCodeNew = $subAsset->status_code ?? null; if ($statusCodeNew) { $statusCodeNew = intval($statusCodeNew); }
	var httpStatusCode interface{}
	if asset.Title != "" { // 如果有标题说明可能有状态码
		// 这里应该从 asset 中获取原始状态码，但由于 UnifiedAsset 结构体中没有定义 StatusCode 字段
		// 暂时设置为 nil，后续会根据协议类型获取
		httpStatusCode = nil
	}

	// [DEBUG] 打印ID生成相关参数和URL信息
	log.WithContextInfof(ctx, "[DEBUG] ID生成参数 - IP: %s, Port: %d, Protocol: %s, Subdomain: %s, 生成的ID: %s",
		s.completeIPV6(asset.IP), asset.Port, asset.Protocol, subdomain, id)
	log.WithContextInfof(ctx, "[DEBUG] URL信息 - fullUrl: %s, recommendInfo.Url: %s, asset.Host: %s",
		fullUrl, getRecommendInfoUrl(recommendInfo), asset.Host)

	// 构建资产数据
	log.WithContextInfof(ctx, "[DEBUG] 构建 assetData，asset.RuleTags 类型: %T, 长度: %d", asset.RuleTags, len(asset.RuleTags))
	assetData := map[string]interface{}{
		"id":                     id,
		"protocol":               asset.Protocol,
		"port":                   asset.Port,
		"fid":                    "",
		"ip":                     s.completeIPV6(asset.IP),
		"task_id":                []uint64{uint64(s.Task.ID)},
		"detect_assets_tasks_id": []uint64{},
		"is_ipv6":                utils.IsIPv6(asset.IP),
		"user_id":                s.Task.UserId,
		"type":                   s.Task.AssetType,
		"online_state":           onlineState,
		"status":                 s.getIpAndPortStatus(ctx, subdomain, recommendInfo, isOffOnline, id, asset.IP),
		"http_status_code":       httpStatusCode, // 对应PHP: $statusCodeNew
		"company_id":             s.Task.CompanyId,
		"url":                    fullUrl,
		"title":                  asset.Title,
		"domain":                 domain,
		"open_parse":             domain_utils.IsWildcardDomain(domain),
		"tags":                   s.getIpTags(),
		"level":                  level,
		"assets_source":          assetsSource,
		"oneforall_source":       oneforallSource, // 对应PHP: 'oneforall_source' => $oneforallSource,
		"assets_source_domain":   assetsSourceDomain,
		"subdomain":              subdomain,
		"icp": map[string]interface{}{ // 对应PHP: 'icp' => ['no' => getIcp($subAsset->body ?? ''), 'type' => null, 'company_name' => null, 'date' => null],
			"no":           utils.GetIcpNumber(asset.Body),
			"type":         "",
			"company_name": "",
			"date":         nil,
		},
		"logo": map[string]interface{}{
			"hash":    nil,
			"content": "",
		},
		"is_login":          0,
		"login_screenshot":  "",
		"screenshot":        "",
		"reliability_score": 0,
		"cloud_name":        cloudName,
		"is_fake_assets":    isFakeAssets,
		"cname":             s.getCname(&fullUrl),
		"reason":            recommendReason,
		"geo":               geo,
		"created_at":        time.Now().Format("2006-01-02 15:04:05"),
		"updated_at":        time.Now().Format("2006-01-02 15:04:05"),
		"rule_tags":         asset.RuleTags, // 类型: []fofaee_subdomain.Rule
		"cert": map[string]interface{}{
			"raw":         "", // 对应PHP: $subAsset->cert ?? ($service->cert ?? null) - 稍后初始化
			"subject_key": "", // 对应PHP: getCert($subAsset->cert ?? ($service->cert ?? '')) - 稍后初始化
		},
		"header": asset.Header,
		"banner": asset.Banner,
		"body":   asset.Body, // 对应PHP: $subAsset->body ?? null
	}

	// 设置detect_assets_tasks_id
	if s.Task.DetectAssetsTasksId != 0 {
		assetData["detect_assets_tasks_id"] = []uint64{uint64(s.Task.DetectAssetsTasksId)}
	}

	// 初始化证书信息 - 对应PHP: 'cert' => ['raw' => $subAsset->cert ?? ($service->cert ?? null), 'subject_key' => getCert($subAsset->cert ?? ($service->cert ?? ''))]
	var certRaw string
	if service != nil && service.Cert != "" {
		certRaw = service.Cert
		// 更新之前初始化的cert字段
		if certMap, ok := assetData["cert"].(map[string]interface{}); ok {
			certMap["raw"] = certRaw
			certMap["subject_key"] = strings.Join(utils.GetCert(certRaw, true), " ")
		}
	} else {
		assetData["cert"] = nil
	}

	// 尝试获取企业名称
	func() {
		defer func() {
			if r := recover(); r != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]completeAssetData 获取企业名称报错了: %v, assets_id=%s", r, id)
				assetData["clue_company_name"] = []string{}
			}
		}()

		var companyName string

		// 尝试多种方式解析 reason 数据
		if reasonData, exists := assetData["reason"]; exists && reasonData != nil {
			// 方式1: 尝试解析为 []interface{}
			if reasons, ok := reasonData.([]interface{}); ok {
				for _, reason := range reasons {
					if reasonMap, ok := reason.(map[string]interface{}); ok {
						reasonType := utils.GetIntValue(reasonMap["type"])
						if reasonType != 4 && reasonType != 5 { // 对应clues.TYPE_KEYWORD和clues.TYPE_LOGO
							// 尝试多种方式获取 clue_company_name
							if clueCompanyName := safeGetClueCompanyName(reasonMap); clueCompanyName != "" {
								companyName = clueCompanyName
								break
							}
						}
					}
				}
			} else if reasons, ok := reasonData.([]foradar_assets.AssetReason); ok {
				// 方式2: 尝试解析为 []foradar_assets.AssetReason
				for _, reason := range reasons {
					if reason.Type != 4 && reason.Type != 5 { // 对应clues.TYPE_KEYWORD和clues.TYPE_LOGO
						if clueCompanyName := safeClueCompanyName(reason.ClueCompanyName); clueCompanyName != "" {
							companyName = clueCompanyName
							break
						}
					}
				}
			} else {
				// 方式3: 记录未知类型用于调试
				log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]completeAssetData 未知的reason类型: %T, assets_id=%s", reasonData, id)
			}
		}

		if companyName != "" {
			assetData["clue_company_name"] = []string{companyName}
		} else {
			assetData["clue_company_name"] = []string{}
		}
	}()

	// 对应PHP: 处理网站状态码，协议类型为http或者https，并且状态码为空的，自己实现获取状态码
	// 对应PHP: LogService::info('getstatus_time_cost', 'start_get_status_code', ['ip端口的assets_id' => $asset['id'], 'url' => $asset['url'], 'user_id' => $this->task->user_id]);
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getstatus_time_cost start_get_status_code: assets_id=%s, url=%s, user_id=%d", assetData["id"], assetData["url"], s.Task.UserId)
	if protocol, ok := assetData["protocol"].(string); ok && (protocol == "http" || protocol == "https" || protocol == "tls" || protocol == "unknown") {
		if url, urlOk := assetData["url"].(string); urlOk && url != "" {
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]completeAssetData-调用function的获取网站状态码报错了: assets_id=%s, url=%s, user_id=%d", assetData["id"], url, s.Task.UserId)
					}
				}()

				statusCode := network.GetUrlStatusCode(url, assetData["id"].(string))
				if statusCode > 0 {
					assetData["http_status_code"] = statusCode
				}
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]获取ip端口维度数据的网站状态码: assets_id=%s, url=%s, http_status_code=%d, user_id=%d", assetData["id"], url, statusCode, s.Task.UserId)
			}()
		}
	}
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getstatus_time_cost end_get_status_code: assets_id=%s, url=%s, online_state=%d, user_id=%d", assetData["id"], assetData["url"], assetData["online_state"], s.Task.UserId)

	// 标记认领状态
	status := utils.GetIntValue(assetData["status"])
	if status == 1 { // 对应foradar_assets.StatusClaimed
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeAssetData-status-company-name 我是认领状态: TaskId=%d, ip=%s, port=%d, company_name=%v",
			s.Task.ID, asset.IP, asset.Port, assetData["clue_company_name"])
	} else {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeAssetData-status-company-name 我不是认领状态: TaskId=%d, ip=%s, port=%d, company_name=%v",
			s.Task.ID, asset.IP, asset.Port, assetData["clue_company_name"])
	}

	// 对应PHP: if ($asset['protocol'] == 'unknown') { $asset['protocol'] = 'tcp'; }
	if protocol, ok := assetData["protocol"].(string); ok && protocol == "unknown" {
		assetData["protocol"] = "tcp"
	}

	// 对应PHP: $asset['assets_type'] = 1; // 本地扫描的端口，都是tcp端口
	if isOffOnline {
		assetData["assets_type"] = 1 // 本地扫描的端口，都是tcp端口
	}

	// 注意：在completeAssetData函数中，PHP原始代码并没有调用getAssetLevel
	// getAssetLevel只在completeIpAssetData(IP维度数据)中被调用
	// 这里的level来自于recommendInfo，而不是通过getAssetLevel计算
	// 对应PHP: 'level' => $recommendInfo->level ?? null,

	// 对应PHP: 推荐结果的证书处理和CDN检测
	if recommendInfo != nil && recommendInfo.CertRaw != "" {
		// 对应PHP: if(!empty($subAsset->cert_raw)){ $cert = json_decode($subAsset->cert_raw, true); if($cert){ $asset['cert'] = $cert; } }
		var c map[string]interface{}
		if err := json.Unmarshal([]byte(recommendInfo.CertRaw), &c); err == nil {
			assetData["cert"] = c
		}
	}

	// 对应PHP: 如果是推荐的资产，那么取fofa的cloud_name字段，判断是否为cdn if ($recommendInfo) { if ($recommendInfo->cloud_name ?? '') { $asset['is_cdn'] = true; } }
	if recommendInfo != nil && recommendInfo.CloudName != "" {
		assetData["is_cdn"] = true
		// 如果检测为CDN，立即设置缓存
		s.setCdnCache(ctx, assetData)
	}

	// 对应PHP: 处理如果是列表端口扫描，然后又没有推荐理由的时候，推荐资产里面非第三方导入的还找不到这条数据的，type设置为0，是微内核基础扫描，扫描到的
	if len(recommendReason) == 0 && s.Task.Flag != "" {
		resultList, err := elastic.AllByParams[recommend_result.RecommendResult](1000, [][]interface{}{
			{"flag", "=", s.Task.Flag},
			{"user_id", "=", s.Task.UserId},
			{"ip", "=", assetData["ip"]},
			{"port", "=", assetData["port"]},
		}, []es2.Sorter{es2.NewFieldSort("id").Desc(), es2.NewFieldSort("ip").Desc()})
		if err == nil && len(resultList) > 0 {
			allResultProtocol := make([]string, 0)
			for _, result := range resultList {
				allResultProtocol = append(allResultProtocol, result.Protocol)
			}
			protocolFound := false
			for _, protocol := range allResultProtocol {
				if protocol == assetData["protocol"].(string) {
					protocolFound = true
					break
				}
			}
			if len(allResultProtocol) > 0 && !protocolFound {
				assetData["type"] = 0
				if assetData["assets_source"] == 0 {
					assetData["assets_source"] = recommend_result.ASSETS_SOURCE_NEW_PROROCOL // RecommendResult::ASSETS_SOURCE_NEW_PROROCOL
				}
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeAssetData-标记为本地扫描了把这个端口: id=%s, ip=%s, flag=%s", assetData["id"], assetData["ip"], s.Task.Flag)
			}
		}
	}

	// 对应PHP: if($sourceUpdateAt){ $asset['source_updated_at'] = $sourceUpdateAt; }
	if sourceUpdateAt != "" {
		assetData["source_updated_at"] = sourceUpdateAt
	}

	// 对应PHP: 判断仿冒资产类型 if($asset['is_fake_assets']){ $asset['threaten_type'] = $recommendInfo->threaten_type ?? 0; $asset['threaten_type_name'] = $recommendInfo->threaten_type_name ?? ''; }
	if isFakeAssets, ok := assetData["is_fake_assets"].(bool); ok && isFakeAssets && recommendInfo != nil {
		assetData["threaten_type"] = recommendInfo.ThreatenType
		assetData["threaten_type_name"] = recommendInfo.ThreatenTypeName
	}

	// 对应PHP: 处理有威胁类型，但是没威胁类型名称的资产 if($asset['status'] == IpAssets::STATUS_THREATEN && empty($asset['threaten_type_name']) && $asset['threaten_type']){ $asset['threaten_type_name'] = BlackKeywordType::query()->where('id',$asset['threaten_type'])->value('name'); }
	status = utils.GetIntValue(assetData["status"])
	if status == 2 { // IpAssets::STATUS_THREATEN
		if threatenTypeName, exists := assetData["threaten_type_name"]; !exists || threatenTypeName == "" {
			if threatenType, typeExists := assetData["threaten_type"]; typeExists && threatenType != nil && threatenType != 0 {
				blackKeywordTypeModel := black_keyword_type.NewTypeModel()
				keywordType, err := blackKeywordTypeModel.First(func(db *gorm.DB) {
					db.Where("id = ?", threatenType)
				})
				if err == nil {
					assetData["threaten_type_name"] = keywordType.Name
				}
			}
		}
	}

	// 对应PHP: if (($subAsset->isRecommendResult ?? false)) { $asset['cert']['raw'] = $subAsset->cert_raw ?? null; $asset['cert']['subject_key'] = $subAsset->cert ?? null; $asset['cert']['is_valid'] = $subAsset->certs_valid ?? null; }
	if recommendInfo != nil && recommendInfo.CertRaw != "" {
		if certMap, ok := assetData["cert"].(map[string]interface{}); ok {
			certMap["raw"] = recommendInfo.CertRaw
			certMap["subject_key"] = recommendInfo.Cert
			certMap["is_valid"] = recommendInfo.CertsValid
		}
	}

	// 这里的CDN判断逻辑已经在前面的cloud_name处理部分完成了，无需重复

	// 对应PHP: 在这匹配一下疑似资产的标题是否命中了威胁词库数据 if($asset['status'] == ForadarAssets::STATUS_DEFAULT){ ... }
	status = utils.GetIntValue(assetData["status"])
	if status == 0 { // ForadarAssets::STATUS_DEFAULT
		// 对应PHP: 线索链是域名或者ip或者子域名，不进行这个判断了(这几种数据来源的资产不会是黄赌毒资产，除非网站被黑了)
		var reasonType int = 99
		if reasons, ok := assetData["reason"].([]interface{}); ok && len(reasons) > 0 {
			if firstReason, ok := reasons[0].(map[string]interface{}); ok {
				reasonType = utils.GetIntValue(firstReason["type"])
			}
		}

		// 对应PHP: if (!in_array($reasonType, [Clue::TYPE_DOMAIN, Clue::TYPE_SUBDOMAIN, Clue::TYPE_IP])) {
		if reasonType != clues.TYPE_DOMAIN && reasonType != clues.TYPE_SUBDOMAIN && reasonType != clues.TYPE_IP {
			// 硬编码的黄赌毒关键词列表
			blackTitle := []string{"金沙娱", "金年会", "智慧投注", "mg娱乐", "MG摆脱", "mile", "春秋彩票", "彩票", "亚美", "彩金", "皇冠体育", "皇冠app", "金莎", "大富豪"}

			// 创建硬编码关键词映射，用于快速判断是否为硬编码关键词
			hardcodedKeywords := make(map[string]bool)
			for _, keyword := range blackTitle {
				hardcodedKeywords[keyword] = true
			}

			var cacheBlackTitle []string
			cacheKey := "black_title_keyword"
			if !redis.GetCache(cacheKey, &cacheBlackTitle) || len(cacheBlackTitle) < 200 {
				systemBlackTitles, err := mysql.NewDSL[black_keyword_system.Keyword]().QueryByParams([][]interface{}{
					{"status", "=", 1},
				})
				if err == nil {
					for _, title := range systemBlackTitles {
						cacheBlackTitle = append(cacheBlackTitle, title.Keyword)
					}
					redis.SetCache(cacheKey, 24*time.Hour, cacheBlackTitle)
				}
			}

			blackTitle = append(blackTitle, cacheBlackTitle...)
			blackTitle = utils.UniqueStringSlice(blackTitle)

			if title, ok := assetData["title"].(string); ok && title != "" {
				// 对应PHP: try { $title = html_entity_decode($asset['title'] ?? ''); } catch (\Throwable $eeee) { $title = ($asset['title'] ?? ''); }
				title = html.UnescapeString(title)

				// 获取威胁类型映射
				blackKeywordTypes, err := mysql.NewDSL[black_keyword_type.KeywordType]().QueryByParams([][]interface{}{})
				typeToName := make(map[int]string)
				if err == nil {
					for _, t := range blackKeywordTypes {
						if t.Id > 0 { // 确保ID有效
							typeToName[int(t.Id)] = t.Name
						}
					}
				} else {
					log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取威胁类型映射失败: %v", err)
				}

				for _, keyword := range blackTitle {
					// 对应PHP: if (count(explode($value, $title)) > 1 || ($title == $value)) {
					if strings.Contains(title, keyword) || title == keyword {
						assetData["is_fake_assets"] = true

						// 获取威胁类型ID
						var fakeTypeId int
						var fakeTypeName string

						// 检查是否为硬编码关键词
						isHardcoded, exists := hardcodedKeywords[keyword]
						if exists && isHardcoded {
							// 硬编码关键词，设置默认威胁类型
							// 根据内置类型，黄赌毒网站对应ID为3（参考 webService/handler/manage/black_keyword/black_keyword.go 中的 builtinType）
							fakeTypeId = 3 // 黄赌毒网站
							if name, exists := typeToName[fakeTypeId]; exists && name != "" {
								fakeTypeName = name
							} else {
								fakeTypeName = "黄赌毒网站" // 默认名称
							}
							log.WithContextInfof(ctx, "[ScanForadarAssetHandler]匹配到硬编码关键词，使用默认威胁类型: keyword=%s, fakeTypeId=%d, fakeTypeName=%s", keyword, fakeTypeId, fakeTypeName)
						} else {
							// 从数据库查询关键词对应的威胁类型
							systemBlackTitle, err := mysql.NewDSL[black_keyword_system.Keyword]().FindByParams([][]interface{}{
								{"keyword", "=", keyword},
								{"status", "=", 1},
							})
							if err == nil && systemBlackTitle.TypeId > 0 {
								// 确保TypeId在合理范围内（避免溢出）
								if systemBlackTitle.TypeId <= 2147483647 { // int32最大值
									fakeTypeId = int(systemBlackTitle.TypeId)
									if name, exists := typeToName[fakeTypeId]; exists && name != "" {
										fakeTypeName = name
									} else {
										fakeTypeName = "其他"
									}
									log.WithContextInfof(ctx, "[ScanForadarAssetHandler]从数据库查询到关键词威胁类型: keyword=%s, fakeTypeId=%d, fakeTypeName=%s", keyword, fakeTypeId, fakeTypeName)
								} else {
									// TypeId过大，使用默认值
									fakeTypeId = 1
									fakeTypeName = "其他"
									log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]威胁类型ID过大，使用默认值: keyword=%s, original_type_id=%d, fakeTypeId=%d", keyword, systemBlackTitle.TypeId, fakeTypeId)
								}
							} else {
								// 数据库中不存在该关键词或TypeId无效，设置默认威胁类型
								fakeTypeId = 1 // 其他类型
								fakeTypeName = "其他"
								if err != nil {
									log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]数据库中未找到关键词，使用默认威胁类型: keyword=%s, fakeTypeId=%d, fakeTypeName=%s, error=%v", keyword, fakeTypeId, fakeTypeName, err)
								} else {
									log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]关键词威胁类型ID无效，使用默认威胁类型: keyword=%s, type_id=%d, fakeTypeId=%d, fakeTypeName=%s", keyword, systemBlackTitle.TypeId, fakeTypeId, fakeTypeName)
								}
							}
						}

						// 最终安全检查：确保威胁类型ID和名称都有效
						if fakeTypeId > 0 && fakeTypeName != "" {
							assetData["threaten_type"] = fakeTypeId
							assetData["status"] = 2 // IpAssets::STATUS_THREATEN
							assetData["threaten_type_name"] = fakeTypeName

							log.WithContextInfof(ctx, "[ScanForadarAssetHandler]扫描的时候重新匹配疑似资产是否命中威胁词库数据，仿冒资产识别到了-把这个资产的类型标记为威胁资产: assets_id=%s, threaten_type=%d, threaten_type_name=%s, task_id=%d, title=%s, keyword=%s", assetData["id"], fakeTypeId, fakeTypeName, s.Task.ID, title, keyword)
						} else {
							// 威胁类型无效，记录错误但不设置威胁状态
							log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]威胁类型ID或名称无效，跳过设置威胁状态: keyword=%s, fakeTypeId=%d, fakeTypeName=%s, assets_id=%s", keyword, fakeTypeId, fakeTypeName, assetData["id"])
						}
						break
					}
				}
			}
		}
	}

	// 对应PHP: if ($service) { $asset['banner'] = $service->banner ?? null; $asset['cert'] = array_merge($service->certs ?? [], $asset['cert']); }
	if service != nil {
		if service.Banner != "" {
			assetData["banner"] = service.Banner
		}
		// 合并cert信息 - 对应PHP: $asset['cert'] = array_merge($service->certs ?? [], $asset['cert']);
		if certMap, ok := assetData["cert"].(map[string]interface{}); ok && service.Cert != "" {
			// service.Certs是结构体，需要转换为map进行合并
			serviceCertMap := map[string]interface{}{
				"cert_date":  service.Certs.CertDate,
				"is_valid":   service.Certs.IsValid,
				"valid_type": service.Certs.ValidType,
				"issuer_cn":  service.Certs.IssuerCN,
				"issuer_cns": service.Certs.IssuerCNs,
				"issuer_org": service.Certs.IssuerOrg,
				"not_after":  service.Certs.NotAfter,
				"not_before": service.Certs.NotBefore,
				"sig_alth":   service.Certs.SigAlth,
				"sn":         service.Certs.Sn,
				"subject_cn": service.Certs.SubjectCN,
				"v":          service.Certs.V,
			}
			// PHP的array_merge是后面的覆盖前面的，所以service的证书信息优先级更高
			for k, v := range serviceCertMap {
				if v != nil && v != "" {
					certMap[k] = v
				}
			}
		}
	}

	// 对应PHP: LOGO处理 - 处理favicon和logo
	// 对应PHP: if ($subAsset->favicon['hash'] ?? null) { ... }
	if asset.Favicon != nil && asset.Favicon.Hash != "" {
		// 对应PHP: 单个扫描任务最多爬取200个
		cacheKey := fmt.Sprintf("crawer_logo_num:%d", s.Task.ID)
		var hasCrawlerNum int
		if !redis.GetCache(cacheKey, &hasCrawlerNum) {
			hasCrawlerNum = 0
		}

		// 对应PHP: if ($hasCrawlerNum <= config('app.crawer_logo_num')) {
		if hasCrawlerNum <= 200 { // 默认最多200个，对应config('app.crawer_logo_num')
			logoMap := assetData["logo"].(map[string]interface{})
			logoMap["hash"] = asset.Favicon.Hash
			// 对应PHP: $asset['logo']['content'] = ltrim(str_replace(storage_path(), '', CrawlerService::saveBase64Ico($subAsset->favicon['base64'], $this->task->user_id)), '/');
			content := utils.SaveBase64Ico(asset.Favicon.Base64, s.Task.UserId)
			if content != "" {
				logoMap["content"] = content
			}
			// 更新计数器
			redis.SetCache(cacheKey, 24*time.Hour, hasCrawlerNum+1)
		}
	}
	// 对应PHP: if ($subAsset->logo['hash'] ?? null) { $asset['logo']['hash'] = $subAsset->logo['hash']; $asset['logo']['content'] = $subAsset->logo['content']; }
	if asset.Logo != nil && asset.Logo.Hash != "" {
		logoMap := assetData["logo"].(map[string]interface{})
		logoMap["hash"] = asset.Logo.Hash
		logoMap["content"] = asset.Logo.Content
	}

	// 企业名称获取逻辑已在前面处理过，这里无需重复

	// 对应PHP: 状态认领日志输出
	status = utils.GetIntValue(assetData["status"])
	if status != 0 {
		ip := assetData["ip"]
		port := assetData["port"]
		companyName := assetData["clue_company_name"]

		if status == 1 { // 对应PHP: ForadarAssets::STATUS_CLAIMED
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeAssetData-status-company-name TaskId:%d, 我当前ip=%v, port=%v, company_name=%v, status=我是认领状态*********",
				s.Task.ID, ip, port, companyName)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeAssetData-status-company-name TaskId:%d, 我当前ip=%v, port=%v, company_name=%v, status=我不是认领状态2222222",
				s.Task.ID, ip, port, companyName)
		}
	}

	// 删除重复的状态码处理逻辑，状态码获取已在前面处理
	log.Debugf("[ScanForadarAssetHandler]completeAssetData-full TaskId:%d, subdomain=%v, service=%v, company_name=%v, asset=%v",
		s.Task.ID, asset, service, assetData["clue_company_name"], assetData)

	return assetData
}

// genIpAssets 生成IP维度数据 - 对应PHP: genIpAssets
func (s *ScanForadarAsset) genIpAssets(ctx context.Context, taskId uint64) {
	// 分批处理TaskAsset - 对应PHP: TaskAsset::query()->where('task_id', $taskId)->orderBy('_id')->chunk(500, function ($taskAssets) use ($taskId) {
	batchSize := 500
	page := 1
	var lastSortValues []interface{} = nil
	for {
		// 获取任务资产列表
		_, taskAssets, ls, err := elastic.ListSearchAfterByParams[fofaee_task_assets.FofaeeTaskAssets](
			batchSize,
			[][]interface{}{
				{"task_id", "=", int(taskId)},
			},
			[]es2.Sorter{
				es2.NewFieldSort("_id").Asc(),
			},
			lastSortValues,
		)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取任务资产列表失败: %v", err)
			return
		}
		lastSortValues = ls

		if len(taskAssets) == 0 {
			break
		}

		// 检查任务是否存在 - 对应PHP: if (!Task::query()->where('id', $this->task->id)->exists()) { $this->clearFailedAssets(); return false; }
		if !s.taskExists(ctx) {
			s.clearFailedAssets(ctx)
			return
		}

		// 处理每个资产 - 对应PHP: $taskAssets->map(function ($tAsset) use ($taskId) {
		for _, tAsset := range taskAssets {
			// 再次检查任务是否存在
			if !s.taskExists(ctx) {
				s.clearFailedAssets(ctx)
				return
			}

			// 获取ForadarAssets数据 - 对应PHP: $fAsset = ForadarAssets::query()->where('ip', $tAsset->ip)->where("user_id",$this->task->user_id)->get();
			fAssets, err := s.getForadarAssetsByIp(ctx, tAsset.Ip, s.Task.UserId)
			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取ForadarAssets失败: %v", err)
				continue
			}

			// 对应PHP: if ($fAsset->isEmpty()) { LogService::info('genIpAssets', 'ip和端口索引表的数据为空啊，查询条件：', ['task_id'=>$taskId,'ip'=>$tAsset->ip]); $fAsset = []; }
			if len(fAssets) == 0 {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ip和端口索引表的数据为空啊，查询条件: task_id=%d, ip=%s", taskId, tAsset.Ip)
				fAssets = []*foradar_assets.ForadarAsset{}
			}

			// 构建IP维度ID - 对应PHP: $id = $this->task->user_id.'_'.$tAsset->ip;
			id := fmt.Sprintf("%d_%s", s.Task.UserId, tAsset.Ip)

			// 获取现有的IpAssets数据 - 对应PHP: $asset = IpAssets::query()->where('_id', $id)->first();
			oldAsset, err := s.getIpAssetById(ctx, id)
			if err != nil {
				log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]获取IpAssets失败: %v", err)
			}

			// 补全IP的数据 - 对应PHP: $scanAsset = $this->completeIpAssetData($id, $fAsset, $tAsset, $asset);
			scanAsset := s.completeIpAssetData(ctx, id, fAssets, tAsset, oldAsset)
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]scanAsset------1111111: status=%v, ip=%s", scanAsset["status"], scanAsset["ip"])

			// 处理IP维度和IP端口维度数据status状态不一致问题 - 对应PHP状态处理逻辑
			s.handleStatusConsistency(ctx, taskId, scanAsset, fAssets)

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]scanAsset------222222: status=%v", scanAsset["status"])
			// 设置影子资产标记 - 对应PHP: $scanAsset['is_shadow'] = IpAssets::NOT_SHADOW;
			scanAsset["is_shadow"] = fofaee_assets.NOT_SHADOW
			// CDN检测 - 对应PHP CDN检测逻辑
			s.detectCDN(ctx, scanAsset)

			// [DEBUG] 打印CDN检测结果
			log.WithContextInfof(ctx, "[DEBUG] CDN检测完成 - IP: %s, is_cdn: %v", scanAsset["ip"], scanAsset["is_cdn"])

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]scanAsset------333333: status=%v", scanAsset["status"])

			// 聚合域名和标题 - 对应PHP聚合逻辑
			s.aggregateDomainsAndTitles(ctx, scanAsset)

			// 插入或更新IpAssets - 对应PHP插入更新逻辑
			err = s.insertOrUpdateIpAsset(ctx, id, scanAsset, oldAsset, tAsset)
			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]插入或更新IpAssets失败: %v", err)
				continue
			}

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]scanAsset------4444: status=%v", scanAsset["status"])

			// 刷新索引 - 对应PHP: IpAssets::query()->refresh();
			// 注意：ElasticSearch的刷新操作在Go版本中可能不需要显式调用
			// err = elastic.RefreshIndex("foradar_ip_assets")
			// if err != nil {
			//     log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]刷新IpAssets索引失败: %v", err)
			// }

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]写入IP维度数据: %s", id)

			// 分发记录IP历史任务 - 对应PHP: RecordIpHistory::dispatch(...)->onQueue('record_ip_history');
			log.WithContextInfof(ctx, "[RECORD_IP_HISTORY_DEBUG] 调用参数 - UserId: %d, CompanyId: %d, IP: %s",
				s.Task.UserId, s.Task.CompanyId, tAsset.Ip)
			s.dispatchRecordIpHistoryJob(ctx, s.Task.UserId, uint64(s.Task.CompanyId), tAsset.Ip, scanAsset)

			// 更新ForadarAssets状态 - 对应PHP手动扫描状态更新逻辑
			if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
				s.updateForadarAssetsStatus(ctx, tAsset)
			}

			// 统计台账数量 - 对应PHP公司使用数量统计
			if s.Task.CompanyId > 0 {
				s.updateCompanyUsedIpAsset(ctx)
			}
		}

		page++
	}
}

func (s *ScanForadarAsset) dispatchExtractAssetCluesJob(ctx context.Context, userId uint64, search map[string]interface{}, deteId, groupId uint64) {
	// 对应PHP任务分发逻辑：ExtractAssetCluesJob::dispatch(...)->onQueue('extract_asset_clues')
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 直接调用PHP的自动提取线索任务
	log.WithContextInfof(ctx, "资产测绘关联异步任务 - 自动提取线索任务下发: user_id=%d, detect_id=%d, group_id=%d", userId, deteId, groupId)
	err := asyncq.ExtractAssetCluesJob.Dispatch(userId, search, deteId, groupId)
	if err != nil {
		log.WithContextErrorf(ctx, "资产测绘关联异步任务 - 自动提取线索任务入队失败: %v", err)
	} else {
		log.WithContextInfof(ctx, "资产测绘关联异步任务 - 自动提取线索任务下发成功: user_id=%d, detect_id=%d, group_id=%d", userId, deteId, groupId)
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发提取资产线索任务: userId=%d, deteId=%d, groupId=%d", userId, deteId, groupId)
}

func (s *ScanForadarAsset) dispatchCountClueAssetTotalJob(ctx context.Context, userId uint64) {
	// 对应PHP任务分发逻辑：CountClueAssetTotal::dispatch(...)->onQueue('count_clue_asset_total')
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 这个任务使用Laravel的JobHelper分发
	err := DispatchLaravelCountClueAssetTotalJob()
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发送Laravel任务失败: %v", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发统计线索已认领资产数量任务: userId=%d", userId)
}

func (s *ScanForadarAsset) dispatchUpdateIpAndPortAssetsCompanyNameJob(ctx context.Context, userId, taskId, companyId uint64) {
	// 对应PHP任务分发逻辑
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 构造UpdateIpAndPortAssetCompanyNameJobPayload
	payload := asyncq.UpdateIpAndPortAssetCompanyNameJobPayload{
		UserId:    userId,
		TaskId:    taskId,
		CompanyId: companyId,
	}

	// 使用asyncq.Enqueue发送任务到队列
	err := asyncq.Enqueue(ctx, asyncq.UpdateIpAndPortAssetCompanyNameJob, payload)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发送任务到队列失败: %v", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发更新IP端口资产企业名称任务: userId=%d, taskId=%d, companyId=%d", userId, taskId, companyId)
}

func (s *ScanForadarAsset) dispatchUpdateRiskTypeAssetsJob(ctx context.Context, userId, taskId, companyId, detectTaskId uint64) {
	// 对应PHP任务分发逻辑
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	if cfg.ExecGolangJob() {
		// // 调用UpdateRiskTypeAssets
		// err := asyncq.Enqueue(ctx, asyncq.UpdateRiskTypeAssetJob, asyncq.UpdateRiskTypeAssetJobPayload{
		// 	UserId:       uint64(userId),
		// 	TaskId:       taskId,
		// 	CompanyId:    uint64(companyId),
		// 	DetectTaskId: &detectTaskId,
		// })
		// if err != nil {
		// 	log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]调用UpdateRiskTypeAssetJob任务失败: %v", err)
		// 	return
		// }
		// 调用PHP Laravel任务：UpdateRiskTypeAssets
		err := asyncq.UpdateRiskTypeAssets.Dispatch(userId, taskId, companyId, detectTaskId)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]调用PHP UpdateRiskTypeAssets任务失败: %v", err)
			return
		}
	} else {
		// 调用PHP Laravel任务：UpdateRiskTypeAssets
		err := asyncq.UpdateRiskTypeAssets.Dispatch(userId, taskId, companyId, detectTaskId)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]调用PHP UpdateRiskTypeAssets任务失败: %v", err)
			return
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发更新风险类型资产任务: userId=%d, taskId=%d, companyId=%d, detectTaskId=%d", userId, taskId, companyId, detectTaskId)
}

func (s *ScanForadarAsset) dispatchStatisticsLoginAssetsJob(ctx context.Context, userId, taskId uint64) {
	// 对应PHP任务分发逻辑
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	if cfg.ExecGolangJob() {
		// 调用PHP的统计登录资产任务
		err := asyncq.StatisticsLoginAssetsPhpJob.Dispatch(userId, taskId)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler] 调用PHP统计登录资产任务失败: %v", err)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler] 成功调用PHP统计登录资产任务: userId=%d, taskId=%d", userId, taskId)
		}
	} else {
		// 调用PHP的统计登录资产任务
		err := asyncq.StatisticsLoginAssetsPhpJob.Dispatch(userId, taskId)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler] 调用PHP统计登录资产任务失败: %v", err)
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler] 成功调用PHP统计登录资产任务: userId=%d, taskId=%d", userId, taskId)
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发统计登录入口数据任务: userId=%d, taskId=%d", userId, taskId)
}

func (s *ScanForadarAsset) dispatchTableAssetsDomainsSyncJob(ctx context.Context, userId, taskId, detectAssetsTasksId uint64) {
	// 对应PHP任务分发逻辑：TableAssetsDoaminsSync::dispatch(...)->onQueue('table_assets_domains_sync')
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 这个任务使用Laravel的JobHelper分发，参考现有的DispatchLaravelTableAssetsDomainsSyncJob实现
	// 使用Laravel队列分发台账域名数据同步任务
	// 注意：这里需要传递相应的domains和count参数，但在当前上下文中我们没有这些数据
	// 所以这里使用占位符值，实际使用时需要根据具体需求调整
	domains := []interface{}{} // 空域名列表
	count := uint64(0)         // 0个域名
	taskType := "asset_scan"   // 任务类型

	err := DispatchLaravelTableAssetsDomainsSyncJob(userId, taskId, taskType, domains, count)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发送Laravel任务失败: %v", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发台账域名数据同步任务: userId=%d, taskId=%d, detectAssetsTasksId=%d", userId, taskId, detectAssetsTasksId)
}

func (s *ScanForadarAsset) dispatchTagAssetsJob(ctx context.Context, userId, taskId uint64) {
	// 对应PHP任务分发逻辑
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 构造TagAssetsJobPayload
	payload := asyncq.TagAssetsJobPayload{
		UserId: userId,
		TaskId: taskId,
	}

	// 使用asyncq.Enqueue发送任务到队列
	err := asyncq.Enqueue(ctx, asyncq.TagAssetsJob, payload)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发送任务到队列失败: %v", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发标记资产tags任务: userId=%d, taskId=%d", userId, taskId)
}

func (s *ScanForadarAsset) dispatchUpdateIpAndPortOnlineStateJob(ctx context.Context, userId, taskId uint64) {
	// 对应PHP任务分发逻辑
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 构造UpdateIpAndPortOnlineStateJobPayload
	payload := asyncq.UpdateIpAndPortOnlineStateJobPayload{
		UserId: int(userId),
		TaskId: int(taskId),
	}

	// 使用asyncq.Enqueue发送任务到队列
	err := asyncq.Enqueue(ctx, asyncq.UpdateIpAndPortOnlineStateJob, payload)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发送任务到队列失败: %v", err)
		return
	}
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发更新IP端口在线状态任务: userId=%d, taskId=%d", userId, taskId)
}

func (s *ScanForadarAsset) dispatchCacheTableIpsConditionJob(ctx context.Context, userId uint64) {
	// 对应PHP任务分发逻辑：CacheTableIpsCondition::dispatch(...)->onQueue('cache_table_ips_condition')
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 这个任务使用Laravel的JobHelper分发
	err := DispatchLaravelCacheTableIpsConditionJob(userId)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发送Laravel任务失败: %v", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发缓存台账IP条件任务: userId=%d", userId)
}

func (s *ScanForadarAsset) dispatchShadowAssetsTagJob(ctx context.Context, userId, taskId, detectTaskId uint64) {
	// 对应PHP任务分发逻辑
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 构造ShadowAssetsTagJobPayload
	payload := asyncq.ShadowAssetsTagJobPayload{
		UserId:       int(userId),
		TaskId:       int(taskId),
		DetectTaskId: int(detectTaskId),
	}

	// 使用asyncq.Enqueue发送任务到队列
	err := asyncq.Enqueue(ctx, asyncq.ShadowAssetsTagJob, payload)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发送任务到队列失败: %v", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发影子资产标签任务: userId=%d, taskId=%d, detectTaskId=%d", userId, taskId, detectTaskId)
}

// getIpAndPortStatus 获取IP+Port入库状态 - 对应PHP: getIpAndPortStatus($subdomain, $recommendInfo, $isOffline,$id,$ip)
// 参数:
// - subdomain: 子域名
// - recommendInfo: 推荐信息
// - isOffline: true 资产在线 false 资产离线
// - id: IP端口ID
// - ip: IP地址
// 返回: 资产状态
func (s *ScanForadarAsset) getIpAndPortStatus(ctx context.Context, subdomain string, recommendInfo *recommend_result.RecommendResult, isOffline bool, id, ip string) int {
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getIpAndPortStatus: %+v", map[string]interface{}{
		"message":       "资产status的评级获取",
		"subdomain":     subdomain,
		"ip":            ip,
		"recommendInfo": recommendInfo,
		"isoffline":     isOffline,
		"ip_port_id":    id,
	})

	// 对应PHP: $status = ($this->task->asset_type == \App\Models\MySql\Task::TASK_ASSET_MANUAL) ?  ForadarAssets::STATUS_CLAIMED : ForadarAssets::STATUS_DEFAULT;
	status := fofaee_assets.STATUS_DEFAULT
	if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
		status = fofaee_assets.STATUS_CLAIMED
	}

	// 对应PHP: if ($this->task->asset_type != \App\Models\MySql\Task::TASK_ASSET_MANUAL) {
	if s.Task.AssetType != scan_task.TASK_ASSET_MANUAL {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getIpAndPortStatus", map[string]interface{}{
			"message":   "我进来判断啦",
			"isoffline": isOffline,
			"ip":        ip,
		})

		// 对应PHP: if (($recommendInfo->is_fake_assets ?? '')) { $status = ForadarAssets::STATUS_THREATEN; }
		if recommendInfo != nil && recommendInfo.IsFakeAssets {
			status = fofaee_assets.STATUS_THREATEN
		} else {
			// 改造，缓存 对应PHP: $domains = Clue::query()->where('user_id', $this->task->user_id)->where('group_id', object_get($this->detectTask, 'group_id'))->where('status', Clue::CLUE_PASS_STATUS)->where('is_deleted', Clue::NOT_DELETE)->where('type', Clue::TYPE_DOMAIN)->pluck('content')->toArray();
			var groupId uint64
			if s.DetectTask != nil {
				groupId = s.DetectTask.GroupId
			}
			domains, err := clues.NewCluer().ListAll(
				mysql.WithColumnValue("user_id", s.Task.UserId),
				mysql.WithColumnValue("group_id", groupId),
				mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
				mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
				mysql.WithColumnValue("type", clues.TYPE_DOMAIN),
			)
			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]getIpAndPortStatus查询域名线索失败: %v", err)
				domains = []*clues.Clue{} // 设为空数组继续执行
			}

			// 转换为content数组
			domainContents := make([]string, 0)
			for _, domain := range domains {
				domainContents = append(domainContents, domain.Content)
			}

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getIpAndPortStatus", map[string]interface{}{
				"message":     "资产status的评级获取",
				"线索的-domains": domainContents,
				"ip":          ip,
				"isoffline":   isOffline,
				"ip_port_id":  id,
			})

			// 对应PHP: $level = $recommendInfo->level ?? null; if ($level && in_array($level, [RecommendResult::ASSETS_LEVEL_A,RecommendResult::ASSETS_LEVEL_B])) {
			var level *int
			if recommendInfo != nil {
				level = &recommendInfo.Level
			}

			if level != nil && (*level == recommend_result.AssetsLevelA || *level == recommend_result.AssetsLevelB) {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getIpAndPortStatus", map[string]interface{}{
					"message": "我是A或者B级资产",
					"level":   *level,
					"ip":      ip,
				})

				// 对应PHP: $status = !$isOffline ? ForadarAssets::STATUS_CLAIMED : ForadarAssets::STATUS_DEFAULT;
				if !isOffline {
					status = fofaee_assets.STATUS_CLAIMED
				} else {
					status = fofaee_assets.STATUS_DEFAULT
				}

				// 对应PHP: if(($isOffline) && ($this->detectTask->id ?? null) && ($this->detectTask->off_assets_to ?? null) && (($this->detectTask->expand_source ?? null) == DetectAssetsTask::EXPAND_FROM_DETECT)){
				if isOffline && s.DetectTask != nil && s.DetectTask.ID > 0 && s.DetectTask.OffAssetsTo > 0 && s.DetectTask.ExpandSource == detect_assets_tasks.ExpandSourceDetect {
					// 对应PHP: if($this->detectTask->off_assets_to == DetectAssetsTask::TO_DEFAULT){ $status = ForadarAssets::STATUS_DEFAULT; }
					if s.DetectTask.OffAssetsTo == detect_assets_tasks.ToDefault {
						status = fofaee_assets.STATUS_DEFAULT
					}
					// 对应PHP: if($this->detectTask->off_assets_to == DetectAssetsTask::TO_TABLE){ $status = ForadarAssets::STATUS_CLAIMED; }
					if s.DetectTask.OffAssetsTo == detect_assets_tasks.ToTable {
						status = fofaee_assets.STATUS_CLAIMED
					}
				}
			}

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getIpAndPortStatus:%+v", map[string]interface{}{
				"message":    "进一步判断是不是C级资产",
				"subdomain":  subdomain,
				"ip":         ip,
				"domains":    domainContents,
				"status":     status,
				"ip_port_id": id,
			})

			// 对应PHP: try { if($domains){ $newDomains = []; foreach ($domains as $ddddd){ $newDomains[] = '.'.$ddddd; } $domains = $newDomains; } }catch (\Throwable $e){ }
			newDomains := make([]string, 0)
			if len(domainContents) > 0 {
				for _, domain := range domainContents {
					newDomains = append(newDomains, "."+domain)
				}
				domainContents = newDomains
			}

			// 对应PHP: if ($level && ($level == RecommendResult::ASSETS_LEVEL_C && Str::endsWith($subdomain, $domains))) {
			if level != nil && *level == recommend_result.AssetsLevelC {
				for _, domain := range domainContents {
					if strings.HasSuffix(subdomain, domain) {
						log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getIpAndPortStatus", map[string]interface{}{
							"message":    "我是C级资产2222222222222",
							"level":      *level,
							"ip":         ip,
							"ip_port_id": id,
						})
						status = fofaee_assets.STATUS_CLAIMED
						break
					}
				}
			}
		}
	}

	var recommendIp string
	if recommendInfo != nil {
		recommendIp = recommendInfo.Ip
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getIpAndPortStatus", map[string]interface{}{
		"message":          "根据推荐理由，最终出来的资产状态",
		"recommendInfo-ip": recommendIp,
		"ip":               ip,
		"status":           status,
		"ip_port_id":       id,
	})

	return status
}

// 辅助函数
func contains(slice []int, item int) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// 获取指定IP的子域名和服务结果，返回统一资产列表
func (s *ScanForadarAsset) getSubdomainAndServiceResult(subdomains []*fofaee_subdomain.FofeeSubdomain, services []*fofaee_service.FofaeeService, taskPorts []int, ip string, isAllPort bool) ([]*UnifiedAsset, map[int]*fofaee_service.FofaeeService) {
	allAssets := make([]*UnifiedAsset, 0)
	serviceByPort := make(map[int]*fofaee_service.FofaeeService)

	// 处理子域名
	if isAllPort {
		log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getSubdomainAndServiceResult-isAllPort: %v", subdomains)
		for _, subdomain := range subdomains {
			allAssets = append(allAssets, s.createUnifiedAssetFromSubdomain(subdomain))
		}
	} else {
		for _, subdomain := range subdomains {
			port := 0
			if subdomain.Port != nil {
				if portFloat, ok := subdomain.Port.(float64); ok {
					log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getSubdomainAndServiceResult-portFloat: %v", portFloat)
					port = int(portFloat)
				}
			}
			for _, taskPort := range taskPorts {
				if port == taskPort {
					log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getSubdomainAndServiceResult-port: %v", port)
					allAssets = append(allAssets, s.createUnifiedAssetFromSubdomain(subdomain))
					break
				}
			}
		}
	}

	// 收集子域名端口
	subdomainPorts := make(map[int]bool)
	for _, asset := range allAssets {
		if asset.IsSubdomain {
			log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getSubdomainAndServiceResult-subdomainPorts: %v", asset.Port)
			subdomainPorts[asset.Port] = true
		}
	}

	// 处理服务
	for _, service := range services {
		serviceByPort[service.Port] = service

		if isAllPort || contains(taskPorts, service.Port) {
			// 如果是HTTP/HTTPS协议且端口已在子域名中，跳过
			if (service.Protocol == "http" || service.Protocol == "https") && subdomainPorts[service.Port] {
				continue
			}
			log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getSubdomainAndServiceResult-service: %v", service)
			allAssets = append(allAssets, s.createUnifiedAssetFromService(service))
		}
	}

	return allAssets, serviceByPort
}

// getFullUrl 根据主机和资产项生成完整的URL
// 参数：
// - host: 主机名（可为空）
// - item: 资产项（包含IP、端口、协议等信息）
// 返回：完整的URL字符串
func (s *ScanForadarAsset) getFullUrl(host string, item interface{}) string {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]getFullUrl发生panic: %v", r)
		}
	}()

	// 处理空主机
	if host == "" {
		host = ""
	}

	// 获取资产项的字段值
	var ip, protocol string
	var port int
	var isRecommendResult bool

	// 根据不同的资产类型提取字段
	switch v := item.(type) {
	case *UnifiedAsset:
		ip = v.IP
		port = v.Port
		protocol = v.Protocol
		if host == "" && v.IsSubdomain {
			host = v.Host
		}
	case *fofaee_subdomain.FofeeSubdomain:
		ip = v.Ip
		if v.Port != nil {
			if portFloat, ok := v.Port.(float64); ok {
				port = int(portFloat)
			} else if portInt, ok := v.Port.(int); ok {
				port = portInt
			}
		}
		protocol = v.Protocol
		if host == "" {
			host = v.Host
		}
	case *fofaee_service.FofaeeService:
		ip = v.IP
		port = v.Port
		protocol = v.Protocol
	case map[string]interface{}:
		// 处理通用map类型
		if ipVal, ok := v["ip"]; ok {
			if ipStr, ok := ipVal.(string); ok {
				ip = ipStr
			}
		}
		if portVal, ok := v["port"]; ok {
			if portInt, ok := portVal.(int); ok {
				port = portInt
			} else if portFloat, ok := portVal.(float64); ok {
				port = int(portFloat)
			}
		}
		if protocolVal, ok := v["protocol"]; ok {
			if protocolStr, ok := protocolVal.(string); ok {
				protocol = protocolStr
			}
		}
		if recommendVal, ok := v["isRecommendResult"]; ok {
			if recommendBool, ok := recommendVal.(bool); ok {
				isRecommendResult = recommendBool
			}
		}
		if host == "" {
			if hostVal, ok := v["host"]; ok {
				if hostStr, ok := hostVal.(string); ok {
					host = hostStr
				}
			}
		}
	default:
		log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]getFullUrl不支持的资产类型: %T", item)
		return ""
	}

	// 如果是推荐结果，处理IDN域名
	if isRecommendResult {
		if urlVal, ok := item.(map[string]interface{})["url"]; ok {
			if urlStr, ok := urlVal.(string); ok {
				host = utils.GetIdnDomain(urlStr, true)
			}
		}
		if host == "" {
			host = utils.GetIdnDomain(strings.TrimSpace(host), true)
		}
	}

	// 获取IP地址
	if ip == "" {
		ip = host
	}
	ip = utils.CompleteIPV6(ip)

	// 如果主机为空，使用IP
	if host == "" {
		host = ip
	}

	// 处理IPv6地址
	if utils.IsIPv6(ip) && utils.GetSubdomain(host) == "" {
		if protocol == "https" || protocol == "http" {
			if port == 80 || port == 443 {
				return fmt.Sprintf("%s://[%s]", protocol, ip)
			} else {
				return fmt.Sprintf("%s://[%s]:%d", protocol, ip, port)
			}
		} else {
			return fmt.Sprintf("[%s]:%d", ip, port)
		}
	}

	// 处理HTTP/HTTPS协议
	if (protocol == "https" || protocol == "http") && !strings.HasPrefix(host, "https://") && !strings.HasPrefix(host, "http://") {
		host = fmt.Sprintf("%s://%s", protocol, host)
	}

	// 处理非标准端口
	if port != 80 && port != 443 && !strings.HasSuffix(strings.TrimRight(host, "/"), fmt.Sprintf(":%d", port)) {
		host = fmt.Sprintf("%s:%d", host, port)
	}

	// 处理443端口的特殊情况
	if port == 443 && protocol != "https" && !strings.HasSuffix(host, ":443") {
		host = fmt.Sprintf("%s:443", host)
	}

	// 如果是443端口且已经有:443后缀，移除它（HTTPS默认端口）
	if port == 443 && strings.HasSuffix(host, ":443") {
		host = strings.Replace(host, ":443", "", 1)
	}

	// 最终处理IDN域名
	fullUrl := utils.GetIdnDomain(strings.TrimSpace(host), true)

	return fullUrl
}

// appendRecommendResultToAssets 将推荐结果数据补充到资产列表中
// appendRecommendResultToAssets 补充IP+Port数据 - 对应PHP: appendRecommendResultToAssets
// 参数：
// - allAssets: 所有资产列表
// - ip: 目标IP
// - ports: 端口列表
// - taskPorts: 任务端口列表
// - isAllPort: 是否扫描所有端口
// 返回：[补充后的资产列表, 是否离线, 离线端口列表]
func (s *ScanForadarAsset) appendRecommendResultToAssets(ctx context.Context, allAssets []*UnifiedAsset, ip string, ports []interface{}, taskPorts []int, isAllPort bool) ([]*UnifiedAsset, bool, []int, error) {
	// 添加方法开始日志
	log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] appendRecommendResultToAssets 开始 - TaskID: %d, Flag: %s, IP: %s", s.Task.ID, s.Task.Flag, ip)

	// 对应PHP: if (empty($ports)) { LogService::info(...); $ports =[]; }
	if len(ports) == 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]端口为null,IP:%s", ip)
		ports = make([]interface{}, 0)
	}

	// 对应PHP: $isOffOnline = $allAssets->isEmpty();
	isOffOnline := len(allAssets) == 0

	// 转换allAssets为可序列化的格式用于日志
	allAssetsInfo := make([]map[string]interface{}, len(allAssets))
	for i, asset := range allAssets {
		allAssetsInfo[i] = map[string]interface{}{
			"ip":       asset.IP,
			"port":     asset.Port,
			"protocol": asset.Protocol,
			"title":    asset.Title,
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]appendRecommendResultToAssets-2: isOffOnline=%v, ip=%s, isAllPort=%v, ports=%v, allAssets长度=%d, allAssets=%v",
		isOffOnline, ip, isAllPort, ports, len(allAssets), allAssetsInfo)

	offLinePorts := make([]int, 0)

	// 对应PHP: $flag = $this->task->flag ?? null; if ($this->detectTask && empty($flag)) { $flag = object_get($this->detectTask, 'expend_flags'); } if ($this->organDetectTask && empty($flag)) { $flag = object_get($this->organDetectTask, 'flag'); }
	flag := s.Task.Flag
	if s.DetectTask != nil && flag == "" {
		flag = s.DetectTask.ExpendFlags
	}
	if s.OrganDetectTask != nil && flag == "" {
		flag = s.OrganDetectTask.Flag
	}

	// 对应PHP: $ipGroup = $allAssets->groupBy("ip");
	ipGroup := make(map[string]map[int]*UnifiedAsset)
	ipGroup[ip] = make(map[int]*UnifiedAsset)
	for _, asset := range allAssets {
		if asset.IP == ip {
			ipGroup[ip][asset.Port] = asset
		}
	}

	if flag != "" {
		// 获取现有端口映射 - 对应PHP: $allAssets->keyBy('port')->keys()->toArray()
		existingPorts := make(map[int]bool)
		for _, asset := range allAssets {
			existingPorts[asset.Port] = true
		}

		// 计算离线端口 - 对应PHP离线端口计算逻辑
		if isAllPort {
			// 全端口扫描：计算1-65535中不存在的端口 - 对应PHP: array_values(array_diff(range(1,65535), ...))
			for port := 1; port <= 65535; port++ {
				if !existingPorts[port] {
					offLinePorts = append(offLinePorts, port)
				}
			}
		} else {
			// 指定端口扫描：计算任务端口中不存在的端口 - 对应PHP: array_values(array_diff($taskPorts, ...))
			for _, port := range taskPorts {
				if !existingPorts[port] {
					offLinePorts = append(offLinePorts, port)
				}
			}
		}

		// 获取补充数据-总是会用推荐的数据去补充ip端口维度索引的数据-并非有端口差集的时候才补充
		// 对应PHP: RecommendResult::query()->where('flag', $flag)->where('ip', $ip)->when(!empty($taskPorts),...)->orderBy("source_updated_at")->get()
		appendRecords, err := s.getRecommendResults(ctx, flag, ip, taskPorts)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取推荐结果失败: %v", err)
			return allAssets, isOffOnline, offLinePorts, err
		}

		// 处理推荐结果 - 对应PHP: ->map(function ($info) use ($offLinePorts,$ipGroup) { ... })
		for _, info := range appendRecords {
			// 补充扫描信息 - 对应PHP: $scanInfo = $ipGroup->get($info->ip,collect())->keyBy("port")->get($info->port);
			portInt := utils.SafeInt(info.Port)
			if scanInfo, exists := ipGroup[info.Ip][portInt]; exists {
				// 对应PHP: $info->header = ($scanInfo->header ?? ($scanInfo->banner ?? ""));
				info.Header = getStringValue(scanInfo.Header, scanInfo.Banner)
				// 对应PHP: $info->banner = ($scanInfo->header ?? ($scanInfo->banner ?? ""));
				info.Banner = getStringValue(scanInfo.Header, scanInfo.Banner)
				// 对应PHP: $info->body = $scanInfo->body ?? "";
				info.Body = scanInfo.Banner
				// 对应PHP: $info->rule_tags = $scanInfo->rule_tags ?? [];
				info.RuleTags = scanInfo.RuleTags
			}

			// 对应PHP: $info->host = $info->url;
			info.Host = info.Url
			// 对应PHP: $info->title = $info->title ?? '';
			if info.Title == "" {
				info.Title = ""
			}

			// 修改logo里面的content为下载地址的数据 - 对应PHP: if (($logo['content'] ?? '')) { $info->logo['content'] = parseDownloadUrl(...); }
			// 注意：PHP代码中有bug，使用了未定义的$logo变量，应该是$info->logo['content']
			if info.Logo.Content != "" {
				// 注意：这里需要实现parseDownloadUrl函数，暂时保持原样
				// info.Logo.Content = utils.ParseDownloadUrl(info.Logo.Content, false)
			}

			// 对应PHP: $info->isRecommendResult = true;
			info.IsRecommendResult = true

			// 对应PHP: if (in_array($info->port,$offLinePorts)) { $info->status = 0; }
			if contains(offLinePorts, portInt) {
				info.Status = 0
			}

			// 转换为UnifiedAsset并添加到结果中
			unifiedAsset := s.convertRecommendResultToUnifiedAsset(info)
			allAssets = append(allAssets, unifiedAsset)
		}
	}

	// 添加方法结束日志
	log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] appendRecommendResultToAssets 结束 - TaskID: %d, 最终资产数量: %d", s.Task.ID, len(allAssets))

	return allAssets, isOffOnline, offLinePorts, nil
}

// getRecommendResults 获取推荐结果数据 - 对应PHP: RecommendResult::query()->where(...)->orderBy("source_updated_at")->get()
func (s *ScanForadarAsset) getRecommendResults(ctx context.Context, flag, ip string, taskPorts []int) ([]*RecommendResultInfo, error) {
	// 添加方法开始日志
	log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] getRecommendResults 开始 - Flag: %s, IP: %s, TaskPorts: %v", flag, ip, taskPorts)

	// 构建查询条件 - 对应PHP: ->where('flag', $flag)->where('ip', $ip)
	conditions := [][]interface{}{
		{"flag", "=", flag},
		{"ip", "=", ip},
	}

	// 如果有检测任务，添加task_id条件 - 对应PHP: ->when($this->detectTask, function ($q) { return $q->where('task_id', $this->task->id); })
	if s.DetectTask != nil {
		conditions = append(conditions, []interface{}{"task_id", "=", s.Task.ID})
	}
	// 如果有组织架构测绘任务，添加task_id条件 - 对应PHP: ->when($this->organDetectTask, function ($q) { return $q->where('task_id', $this->task->id); })
	if s.OrganDetectTask != nil {
		conditions = append(conditions, []interface{}{"task_id", "=", s.Task.ID})
	}

	// 如果有指定端口，添加端口过滤条件 - 对应PHP: ->when(!empty($taskPorts),function ($q) use($taskPorts){ return $q->whereIn('port',$taskPorts); })
	if len(taskPorts) > 0 {
		portInterfaces := make([]interface{}, len(taskPorts))
		for i, port := range taskPorts {
			portInterfaces[i] = port
		}
		conditions = append(conditions, []interface{}{"port", "in", portInterfaces})
	}

	// 查询推荐结果 - 对应PHP: ->orderBy("source_updated_at")->get()
	results, err := elastic.AllByParams[recommend_result.RecommendResult](
		1000,
		conditions,
		[]es2.Sorter{
			es2.NewFieldSort("source_updated_at").Asc(),
			es2.NewFieldSort("id").Asc(),
		},
	)
	if err != nil {
		return nil, fmt.Errorf("查询推荐结果失败: %v", err)
	}

	// 转换为RecommendResultInfo格式
	var resultInfos []*RecommendResultInfo
	for _, result := range results {
		// 转换推荐理由
		var recommendReasons []RecommendReason
		for _, reason := range result.Reason {
			recommendReasons = append(recommendReasons, RecommendReason{
				Id:              reason.Id,
				Type:            reason.Type,
				Content:         reason.Content,
				GroupId:         reason.GroupId,
				ClueCompanyName: reason.ClueCompanyName,
				Source:          reason.Source,
			})
		}

		resultInfo := &RecommendResultInfo{
			Id:                result.Id,
			Ip:                result.Ip,
			Port:              safeStringValue(result.Port),
			Url:               result.Url,
			Protocol:          result.Protocol,
			Title:             result.Title,
			Domain:            result.Domain,
			Subdomain:         result.Subdomain,
			Header:            result.Banner, // RecommendResult中没有Header字段，使用Banner
			Banner:            result.Banner,
			Body:              result.Banner, // RecommendResult中没有Body字段，使用Banner
			Host:              result.Url,    // RecommendResult中没有Host字段，使用Url
			Status:            0,             // 默认状态
			Logo:              RecommendResultLogo{Hash: result.Logo.Hash, Content: result.Logo.Content},
			IsRecommendResult: false,                            // 将在后续设置
			RuleTags:          make([]fofaee_subdomain.Rule, 0), // 需要处理规则标签
			RecommendReason:   recommendReasons,                 // 设置推荐理由
			AssetsSource:      result.AssetsSource,
			OneforallSource:   result.OneforallSource,
			SourceUpdatedAt:   result.SourceUpdatedAt,
			IsCdn:             utils.SafeInt(result.IsCDN) != 0,
			CloudName:         result.CloudName,
		}
		resultInfos = append(resultInfos, resultInfo)
	}

	// 添加方法结束日志
	log.WithContextInfof(ctx, "[RECOMMEND_DEBUG] getRecommendResults 结束 - Flag: %s, IP: %s, 返回数量: %d", flag, ip, len(resultInfos))

	return resultInfos, nil
}

// convertRecommendResultToUnifiedAsset 将推荐结果转换为统一资产
func (s *ScanForadarAsset) convertRecommendResultToUnifiedAsset(info *RecommendResultInfo) *UnifiedAsset {
	port := utils.SafeInt(info.Port)

	return &UnifiedAsset{
		IP:                info.Ip,
		Port:              port,
		Protocol:          info.Protocol,
		Banner:            info.Banner,
		Header:            info.Header,
		Title:             info.Title,
		Host:              info.Host,
		Domain:            info.Domain,
		Subdomain:         info.Subdomain,
		Body:              info.Body,                        // 对应PHP: $subAsset->body
		RuleTags:          make([]fofaee_subdomain.Rule, 0), // 推荐结果暂时没有规则标签
		IsSubdomain:       false,                            // 推荐结果不是子域名类型
		IsRecommendResult: info.IsRecommendResult,
		RecommendReason:   info.RecommendReason, // 传递推荐理由
		AssetsSource:      func() *int64 { v := int64(info.AssetsSource); return &v }(),
		OneforallSource:   info.OneforallSource,
		SourceUpdatedAt:   info.SourceUpdatedAt,
		IsCdn:             info.IsCdn,
		IsIpv6:            utils.IsIPv6(info.Ip),
		CloudName:         info.CloudName,
	}
}

// RecommendReason 推荐理由结构体
type RecommendReason struct {
	Id              int    `json:"id"`
	Type            int    `json:"type"`
	Content         string `json:"content"`
	GroupId         int    `json:"group_id"`
	ClueCompanyName string `json:"clue_company_name"`
	Source          int    `json:"source"`
}

// RecommendResultInfo 推荐结果信息结构体
type RecommendResultInfo struct {
	Id                string                  `json:"id"`
	Ip                string                  `json:"ip"`
	Port              string                  `json:"port"`
	Url               string                  `json:"url"`
	Protocol          string                  `json:"protocol"`
	Title             string                  `json:"title"`
	Domain            string                  `json:"domain"`
	Subdomain         string                  `json:"subdomain"`
	Header            string                  `json:"header"`
	Banner            string                  `json:"banner"`
	Body              string                  `json:"body"`
	Host              string                  `json:"host"`
	Status            int                     `json:"status"`
	Logo              RecommendResultLogo     `json:"logo"`
	IsRecommendResult bool                    `json:"isRecommendResult"`
	RuleTags          []fofaee_subdomain.Rule `json:"rule_tags"`
	RecommendReason   []RecommendReason       `json:"recommend_reason"`
	AssetsSource      int                     `json:"assets_source"`
	OneforallSource   string                  `json:"oneforall_source"`
	SourceUpdatedAt   string                  `json:"source_updated_at"`
	IsCdn             bool                    `json:"is_cdn"`
	CloudName         string                  `json:"cloud_name"`
}

// RecommendResultLogo 推荐结果Logo结构
type RecommendResultLogo struct {
	Hash    interface{} `json:"hash"`
	Content string      `json:"content"`
}

// getStringValue 获取字符串值，优先返回第一个非空值
func getStringValue(values ...string) string {
	for _, value := range values {
		if value != "" {
			return value
		}
	}
	return ""
}

// getBoolValue 获取布尔值
func getBoolValue(value interface{}) bool {
	if value == nil {
		return false
	}
	switch v := value.(type) {
	case bool:
		return v
	case int:
		return v != 0
	case string:
		return v == "true" || v == "1"
	case float64:
		return v != 0
	}
	return false
}

// getIpTags 获取IP维度标签
// 根据任务类型和来源返回相应的标签
// 返回：标签数组
func (s *ScanForadarAsset) getIpTags() []int {
	if s.Task.AssetType == scan_task.TASK_ASSET_CLOUD {
		// 0 客户-扫描 //1 安服-扫描 //2 客户-推荐 //3 安服-推荐
		if s.Task.TaskFrom == 1 {
			log.Infof("getIpTags safe_rec")
			return []int{fofaee_assets.SAFE_REC}
		} else {
			return []int{fofaee_assets.CLIENT_REC}
		}
	} else {
		if s.Task.TaskFrom == 1 {
			// 安服扫描
			return []int{fofaee_assets.SAFE_SCAN}
		} else {
			return []int{fofaee_assets.CLIENT_SCAN}
		}
	}
}

// filterDomainAsset 过滤域名资产（原PHP函数名filterDamianAsset有误）
// 参数：
// - fAssets: ForadarAssets资产列表
// - tAsset: TaskAsset任务资产
// 返回：[hostUniqueHash, hostList, portLists]
func (s *ScanForadarAsset) filterDomainAsset(ctx context.Context, fAssets []*foradar_assets.ForadarAsset, tAsset *fofaee_task_assets.FofaeeTaskAssets) ([]string, []map[string]interface{}, []map[string]interface{}) {
	if len(fAssets) == 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset: fassets为空，补充ip维度数据")
		return []string{}, []map[string]interface{}{}, []map[string]interface{}{}
	}

	hostUniqueHash := make([]string, 0)
	hostList := make([]map[string]interface{}, 0)

	log.WithContextInfof(ctx, "[DEBUG_HOST_LIST] 开始构建hostList，fAssets数量: %d", len(fAssets))

	// 对fAssets进行map操作，生成hostList，同时填充hostUniqueHash
	for _, f := range fAssets {
		// 计算hash
		port := utils.SafeInt(f.Port)
		hash := utils.Md5Hash(fmt.Sprintf("%d%s%s", port, f.Protocol, f.Subdomain))
		hostUniqueHash = append(hostUniqueHash, hash)

		// 设置is_open字段fix
		isOpen := utils.SafeInt(f.OnlineState)

		// 创建hostList项目（修正：包含所有PHP版本中的字段）
		hostItem := map[string]interface{}{
			// 基础字段
			"port":           port,
			"url":            f.Url,
			"protocol":       f.Protocol,
			"fid":            f.Fid,
			"ip":             f.Ip,
			"type":           f.Type,
			"online_state":   f.OnlineState,
			"is_open":        isOpen,
			"open_parse":     f.OpenParse,
			"is_login":       f.IsLogin,
			"is_login_page":  f.IsLoginPage,
			"is_copyright":   f.IsCopyright,
			"is_fake_assets": f.IsFakeAssets,
			"is_cdn":         f.IsCdn,
			// 内容字段
			"title":         f.Title,
			"banner":        f.Banner,
			"header":        f.Header,
			"domain":        f.Domain,
			"subdomain":     f.Subdomain,
			"threaten_type": f.ThreatenType,
			"reason_string": f.ReasonString,
			// 时间字段
			"created_at":        f.CreatedAt,
			"updated_at":        f.UpdatedAt,
			"source_updated_at": f.SourceUpdatedAt,
			"cname":             f.Cname,
			// 复杂字段
			"http_status_code":  f.HTTPStatusCode,
			"cert":              f.Cert,
			"icp":               f.Icp,
			"logo":              f.Logo,
			"reason":            f.Reason,
			"rule_tags":         f.RuleTags,
			"clue_company_name": safeStringArray(f.ClueCompanyName),
			// 来源字段
			"assets_source":        f.AssetsSource,
			"oneforall_source":     f.OneforallSource,
			"assets_source_domain": f.AssetsSourceDomain,
		}
		hostList = append(hostList, hostItem)
	}

	// 对应PHP: 处理这扫描了某些端口，但是没扫出来数据，把这些端口更新为离线状态
	isAllPort := s.isAllPort
	// 定义一个离线的端口数据集
	offPortArr := make([]int, 0)
	// 定义一个在线的端口数据集
	onlinePortArr := make([]int, 0)

	// log.WithContextInfof(ctx, "[ScanForadarAssetJob]filterDomainAsset-0-task_assets-打印taskAssets: tAsset=%+v, task_id=%d", tAsset, s.Task.ID)

	if isAllPort {
		// 全端口扫描
		// 查询当前task_assets扫出来的端口
		thisTaskAssetsOnlineState := tAsset.State
		var thisIpGetPortArr []interface{}
		if thisTaskAssetsOnlineState == 0 {
			// 离线的话，这个ip就设置扫到的端口为空就可以了
			thisIpGetPortArr = []interface{}{}
		} else {
			thisIpGetPortArr = tAsset.Ports
		}

		if len(thisIpGetPortArr) == 0 {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-5-全端口扫描: thisIpGetPortArr=%v, ip=%s, task_id=%d", thisIpGetPortArr, tAsset.Ip, s.Task.ID)
			for i := range hostList {
				port := utils.SafeInt(hostList[i]["port"])
				offPortArr = append(offPortArr, port)
				hostList[i]["is_open"] = 0
				hostList[i]["http_status_code"] = 0
				hostList[i]["online_state"] = 0
			}
		} else {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-6-全端口扫描: thisIpGetPortArr=%v, ip=%s, task_id=%d", thisIpGetPortArr, tAsset.Ip, s.Task.ID)
			// 转换为int切片以便查找
			portSet := make(map[int]bool)
			for _, p := range thisIpGetPortArr {
				if port := utils.SafeInt(p); port > 0 {
					portSet[port] = true
				}
			}

			for i := range hostList {
				port := utils.SafeInt(hostList[i]["port"])
				if !portSet[port] {
					offPortArr = append(offPortArr, port)
					hostList[i]["is_open"] = 0
					hostList[i]["http_status_code"] = 0
					hostList[i]["online_state"] = 0
				} else {
					onlinePortArr = append(onlinePortArr, port)
					hostList[i]["is_open"] = 1
					hostList[i]["online_state"] = 1
				}
			}
		}
	} else {
		// 当前ip在扫描任务结果里面task_assets索引的在线端口数据
		thisTaskAssetsOnlineState := tAsset.State
		var thisIpGetPortArr []interface{}
		if thisTaskAssetsOnlineState == 0 {
			// 离线的话，这个ip就设置扫到的端口为空就可以了
			thisIpGetPortArr = []interface{}{}
		} else {
			thisIpGetPortArr = tAsset.Ports
		}

		// 非全端口扫描 - 判断是否为列表端口扫描类型
		isListPortScan := s.Task.PortRange > 0
		if isListPortScan {
			// 列表端口扫描 - 对应PHP: $taskListPort = $taskPortArr[2] ?? [];
			var taskListPort map[string][]task.TaskProbeInfo
			if len(s.portInfoArr) > 2 {
				if probePortsMap, ok := s.portInfoArr[2].(map[string][]task.TaskProbeInfo); ok {
					taskListPort = probePortsMap
				} else {
					taskListPort = make(map[string][]task.TaskProbeInfo)
				}
			} else {
				taskListPort = make(map[string][]task.TaskProbeInfo)
			}

			// 对应PHP: $thisIpTaskInfo = $taskListPort[$tAssets->ip] ?? [];
			var thisIpTaskInfo []task.TaskProbeInfo
			if ipTaskInfo, exists := taskListPort[tAsset.Ip]; exists {
				thisIpTaskInfo = ipTaskInfo
			}

			// 对应PHP: $thisIpAllPortArr = collect($thisIpTaskInfo)->pluck('port')->unique()->filter()->values()->toArray();
			thisIpAllPortArr := make([]int, 0)
			portSet := make(map[int]bool)
			for _, item := range thisIpTaskInfo {
				if item.Port > 0 && !portSet[item.Port] {
					thisIpAllPortArr = append(thisIpAllPortArr, item.Port)
					portSet[item.Port] = true
				}
			}

			// 转换为int切片以便查找
			getPortSet := make(map[int]bool)
			for _, p := range thisIpGetPortArr {
				if port := utils.SafeInt(p); port > 0 {
					getPortSet[port] = true
				}
			}

			allPortSet := make(map[int]bool)
			for _, port := range thisIpAllPortArr {
				allPortSet[port] = true
			}

			if len(thisIpGetPortArr) == 0 {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-1-列表端口扫描: thisIpAllPortArr=%v, thisIpGetPortArr=%v, ip=%s, task_id=%d", thisIpAllPortArr, thisIpGetPortArr, tAsset.Ip, s.Task.ID)
				for i := range hostList {
					port := utils.SafeInt(hostList[i]["port"])
					if allPortSet[port] {
						offPortArr = append(offPortArr, port)
						hostList[i]["is_open"] = 0
						hostList[i]["http_status_code"] = 0
						hostList[i]["online_state"] = 0
					}
				}
			} else {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-2-列表端口扫描: thisIpAllPortArr=%v, thisIpGetPortArr=%v, ip=%s, task_id=%d", thisIpAllPortArr, thisIpGetPortArr, tAsset.Ip, s.Task.ID)
				// 当前ip有扫出来端口数据
				for i := range hostList {
					port := utils.SafeInt(hostList[i]["port"])
					// 当前ip的某个端口在扫描目标，但是没在扫描结果的话，设置为离线
					if allPortSet[port] && !getPortSet[port] {
						offPortArr = append(offPortArr, port)
						hostList[i]["is_open"] = 0
						hostList[i]["http_status_code"] = 0
						hostList[i]["online_state"] = 0
					} else if allPortSet[port] && getPortSet[port] {
						onlinePortArr = append(onlinePortArr, port)
						hostList[i]["is_open"] = 1
						hostList[i]["online_state"] = 1
					}
				}
			}
		} else {
			// 常用端口或者指定端口或者端口分组扫描 - 对应PHP: $thisIpAllPortArr = $taskPortArr[0] ?? [];
			var thisIpAllPortArr []int
			if len(s.portInfoArr) > 0 {
				if taskPortsSlice, ok := s.portInfoArr[0].([]int); ok {
					thisIpAllPortArr = taskPortsSlice
				}
			}

			// 转换为int切片以便查找
			getPortSet := make(map[int]bool)
			for _, p := range thisIpGetPortArr {
				if port := utils.SafeInt(p); port > 0 {
					getPortSet[port] = true
				}
			}

			allPortSet := make(map[int]bool)
			for _, port := range thisIpAllPortArr {
				allPortSet[port] = true
			}

			if len(thisIpGetPortArr) == 0 {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-3-常用端口或者指定端口或者端口分组扫描: thisIpAllPortArr=%v, thisIpGetPortArr=%v, ip=%s, task_id=%d", thisIpAllPortArr, thisIpGetPortArr, tAsset.Ip, s.Task.ID)
				for i := range hostList {
					port := utils.SafeInt(hostList[i]["port"])
					if allPortSet[port] {
						offPortArr = append(offPortArr, port)
						hostList[i]["is_open"] = 0
						hostList[i]["http_status_code"] = 0
						hostList[i]["online_state"] = 0
					}
				}
			} else {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-4-常用端口或者指定端口或者端口分组扫描: thisIpAllPortArr=%v, thisIpGetPortArr=%v, ip=%s, task_id=%d", thisIpAllPortArr, thisIpGetPortArr, tAsset.Ip, s.Task.ID)
				for i := range hostList {
					port := utils.SafeInt(hostList[i]["port"])
					if allPortSet[port] && !getPortSet[port] {
						offPortArr = append(offPortArr, port)
						hostList[i]["is_open"] = 0
						hostList[i]["http_status_code"] = 0
						hostList[i]["online_state"] = 0
					} else if allPortSet[port] && getPortSet[port] {
						onlinePortArr = append(onlinePortArr, port)
						hostList[i]["is_open"] = 1
						hostList[i]["online_state"] = 1
					}
				}
			}
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-离线的端口数据集: offPortArr=%v, ip=%s, task_id=%d", offPortArr, tAsset.Ip, s.Task.ID)

	// 将int切片转换为interface{}切片的辅助函数

	// 更新离线端口状态 - 对应PHP: ForadarAssets::query()->where('user_id', $this->task->user_id)->where('ip', $tAssets->ip)->whereIn('port', $offPortArr)->update(['online_state' => 0, 'http_status_code' => 0]);
	if len(offPortArr) > 0 {
		offPortArr = utils.ListDistinct(offPortArr)

		// 使用ES的UpdateByQuery方法
		foradarModel := foradar_assets.NewForadarAssetModel()
		query := es2.NewBoolQuery().
			Must(es2.NewTermQuery("user_id", s.Task.UserId)).
			Must(es2.NewTermQuery("ip.keyword", tAsset.Ip))

		// 添加端口条件
		portTerms := make([]interface{}, len(offPortArr))
		for i, port := range offPortArr {
			portTerms[i] = port
		}
		query.Must(es2.NewTermsQuery("port", portTerms...))

		updateData := map[string]interface{}{
			"online_state":     0,
			"http_status_code": 0,
		}

		err := foradarModel.UpdateByQuery(ctx, query, updateData)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新离线端口失败: %v", err)
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-更新这些端口为离线端口: offPortArr=%v, ip=%s, task_id=%d", offPortArr, tAsset.Ip, s.Task.ID)
	}

	// 更新在线端口状态 - 对应PHP: ForadarAssets::query()->where('user_id', $this->task->user_id)->where('ip', $tAssets->ip)->whereIn('port', $onlinePortArr)->update(['online_state' => 1]);
	if len(onlinePortArr) > 0 {
		onlinePortArr = utils.ListDistinct(onlinePortArr)

		// 使用ES的UpdateByQuery方法
		foradarModel := foradar_assets.NewForadarAssetModel()
		query := es2.NewBoolQuery().
			Must(es2.NewTermQuery("user_id", s.Task.UserId)).
			Must(es2.NewTermQuery("ip.keyword", tAsset.Ip))

		// 添加端口条件
		portTerms := make([]interface{}, len(onlinePortArr))
		for i, port := range onlinePortArr {
			portTerms[i] = port
		}
		query.Must(es2.NewTermsQuery("port", portTerms...))

		updateData := map[string]interface{}{
			"online_state": 1,
		}

		err := foradarModel.UpdateByQuery(ctx, query, updateData)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新在线端口失败: %v", err)
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]filterDomainAsset-更新这些端口为在线端口: onlinePortArr=%v, ip=%s, task_id=%d", onlinePortArr, tAsset.Ip, s.Task.ID)
	}

	// 按端口分组处理，选择最佳的域名资产记录
	filterIpPortDomain := make(map[int]map[string]interface{})
	portGroups := make(map[int][]map[string]interface{})
	for _, host := range hostList {
		port := utils.SafeInt(host["port"])
		portGroups[port] = append(portGroups[port], host)
	}

	for port, rows := range portGroups {
		// 对应PHP: 取更新时间最近的一条,有域名的数据
		var updatedNewRow map[string]interface{}

		// 先筛选有子域名的记录并按source_updated_at降序排序
		subdomainRowsForTime := make([]map[string]interface{}, 0)
		for _, row := range rows {
			subdomain := utils.SafeString(row["subdomain"])
			if subdomain != "" {
				subdomainRowsForTime = append(subdomainRowsForTime, row)
			}
		}

		// 按source_updated_at降序排序，选择最新的有子域名的记录
		if len(subdomainRowsForTime) > 0 {
			sort.Slice(subdomainRowsForTime, func(i, j int) bool {
				timeI := utils.SafeString(subdomainRowsForTime[i]["source_updated_at"])
				timeJ := utils.SafeString(subdomainRowsForTime[j]["source_updated_at"])
				return timeI > timeJ // 降序排列
			})
			updatedNewRow = subdomainRowsForTime[0]
		}

		if updatedNewRow != nil {
			filterIpPortDomain[port] = updatedNewRow
		} else {
			// 对应PHP: 没有找到时间最新的有subdomain记录，进入复杂选择逻辑
			// 按subdomain长度排序（对应PHP: sortBy(function ($item) { return strlen($item['subdomain'] ?? ''); }, SORT_ASC)）
			filterIpPortDomain[port] = rows[0]
			// 死代码，因为只有有一条记录有subdomain,就会进前面if的逻辑，不会进这样，如果所有subdomain都为空，进这里，后面又筛选有subdomain的记录，这逻辑是矛盾的
			// 此处根据subdomain长度排序也不成立
			// sortRows := make([]map[string]interface{}, len(rows))
			// copy(sortRows, rows)
			// sort.Slice(sortRows, func(i, j int) bool {
			// 	subdomainI := utils.SafeString(sortRows[i]["subdomain"])
			// 	subdomainJ := utils.SafeString(sortRows[j]["subdomain"])
			// 	return len(subdomainI) < len(subdomainJ)
			// })

			// // 筛选有subdomain的记录（这次是从排序后的结果中筛选）
			// subdomainRows := make([]map[string]interface{}, 0)
			// for _, row := range sortRows {
			// 	subdomain := utils.SafeString(row["subdomain"])
			// 	if subdomain != "" {
			// 		subdomainRows = append(subdomainRows, row)
			// 	}
			// }

			// if len(subdomainRows) > 0 {
			// 	// 筛选状态码为200或subdomain等于domain的记录
			// 	var codeInfo map[string]interface{}
			// 	for _, row := range subdomainRows {
			// 		httpStatusCode := utils.SafeInt(row["http_status_code"])
			// 		subdomain := utils.SafeString(row["subdomain"])
			// 		domain := utils.SafeString(row["domain"])
			// 		if httpStatusCode == 200 || subdomain == domain {
			// 			codeInfo = row
			// 			break
			// 		}
			// 	}

			// 	if codeInfo != nil {
			// 		// 状态200或subdomain==domain的记录
			// 		filterIpPortDomain[port] = codeInfo
			// 	} else {
			// 		// subdomain取一条，进入更复杂的选择逻辑
			// 		portInfo := subdomainRows[0]
			// 		domain := utils.SafeString(portInfo["domain"])

			// 		// 查找www.domain或subdomain==domain的记录
			// 		var wwwRow map[string]interface{}
			// 		for _, row := range subdomainRows {
			// 			url := utils.SafeString(row["url"])
			// 			subdomain := utils.SafeString(row["subdomain"])
			// 			rowDomain := utils.SafeString(row["domain"])
			// 			if strings.Contains(strings.ToLower(url), "www."+domain) || subdomain == rowDomain {
			// 				wwwRow = row
			// 				break
			// 			}
			// 		}

			// 		if wwwRow != nil {
			// 			filterIpPortDomain[port] = wwwRow
			// 		} else {
			// 			// 统计domain出现次数，选择最多的domain对应的记录
			// 			domainCount := make(map[string]int)
			// 			for _, row := range subdomainRows {
			// 				domain := utils.SafeString(row["domain"])
			// 				if domain != "" {
			// 					domainCount[domain]++
			// 				}
			// 			}

			// 			// 找出现次数最多的domain
			// 			var maxDomain string
			// 			var maxCount int
			// 			for domain, count := range domainCount {
			// 				if count > maxCount {
			// 					maxCount = count
			// 					maxDomain = domain
			// 				}
			// 			}

			// 			// 找到对应maxDomain的记录
			// 			if maxDomain != "" {
			// 				for _, row := range subdomainRows {
			// 					if utils.SafeString(row["domain"]) == maxDomain {
			// 						portInfo = row
			// 						break
			// 					}
			// 				}
			// 			}

			// 			filterIpPortDomain[port] = portInfo
			// 		}
			// 	}
			// } else {
			// 	// 默认取第一条（按subdomain长度排序后的第一条）
			// 	filterIpPortDomain[port] = sortRows[0]
			// }
		}
	}

	// 将filterIpPortDomain转换为portLists，并清理不应该在port_list中的字段
	portLists := make([]map[string]interface{}, 0)
	for _, item := range filterIpPortDomain {
		// 创建一个清理后的port_list项目，移除可能导致ES映射冲突的字段
		cleanedItem := make(map[string]interface{})

		// 定义port_list中应该保留的字段
		allowedFields := map[string]bool{
			"port":                 true,
			"url":                  true,
			"protocol":             true,
			"fid":                  true,
			"ip":                   true,
			"type":                 true,
			"online_state":         true,
			"is_open":              true,
			"open_parse":           true,
			"is_login":             true,
			"is_login_page":        true,
			"is_copyright":         true,
			"is_fake_assets":       true,
			"is_cdn":               true,
			"title":                true,
			"domain":               true,
			"subdomain":            true,
			"threaten_type":        true,
			"reason_string":        true,
			"cname":                true,
			"http_status_code":     true,
			"assets_source":        true,
			"oneforall_source":     true,
			"assets_source_domain": true,
			// 注意：移除了updated_at, created_at, source_updated_at等时间字段
			// 注意：移除了banner, header, cert, icp, logo, reason, rule_tags等复杂字段
		}

		// 只复制允许的字段
		for key, value := range item {
			if allowedFields[key] {
				cleanedItem[key] = value
			}
		}

		portLists = append(portLists, cleanedItem)
	}

	// 按端口排序
	sort.Slice(portLists, func(i, j int) bool {
		portI := utils.SafeInt(portLists[i]["port"])
		portJ := utils.SafeInt(portLists[j]["port"])
		return portI < portJ
	})

	return hostUniqueHash, hostList, portLists
}

// getAssetCompanyName 获取资产企业名称
func (s *ScanForadarAsset) getAssetCompanyName(ctx context.Context, portLists []map[string]interface{}, clueCompanyName []string, id string) []string {
	oldName := clueCompanyName
	if len(portLists) == 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetCompanyName portLists为空，企业名称返回空数组")
		return []string{}
	}

	var finalClueCompanyName []string

	// 如果是单位测绘类型的扫描任务的话，判断推荐理由里面是否存在子域名类型的线索，存在的话，取子域名类型线索的企业名称
	// 对应PHP: if (($this->task->detect_assets_tasks_id ?? null)) {
	if s.Task.DetectAssetsTasksId > 0 {
		// 对应PHP: $reasonAll = collect($portLists)->pluck('reason')->collapse()->toArray();
		var reasonAll []map[string]interface{}
		log.WithContextInfof(ctx, "[DEBUG] Starting to process portLists, length: %d", len(portLists))
		for i, port := range portLists {
			log.WithContextInfof(ctx, "[DEBUG] Processing port %d, keys: %v", i, getMapKeys(port))
			if reasonInterface, exists := port["reason"]; exists {
				log.WithContextInfof(ctx, "[DEBUG] port reason exists, type: %T, value: %+v", reasonInterface, reasonInterface)

				// 处理 []foradar_assets.AssetReason 类型
				if reasonSlice, ok := reasonInterface.([]foradar_assets.AssetReason); ok {
					log.WithContextInfof(ctx, "[DEBUG] Successfully cast to []foradar_assets.AssetReason, length: %d", len(reasonSlice))
					for i, reasonItem := range reasonSlice {
						log.WithContextInfof(ctx, "[DEBUG] Processing reason item %d: %+v", i, reasonItem)
						reasonMap := map[string]interface{}{
							"id":                reasonItem.ID,
							"group_id":          reasonItem.GroupID,
							"source":            reasonItem.Source,
							"type":              reasonItem.Type,
							"content":           reasonItem.Content,
							"clue_company_name": safeClueCompanyName(reasonItem.ClueCompanyName),
						}
						log.WithContextInfof(ctx, "[DEBUG] Created reasonMap: %+v", reasonMap)
						reasonAll = append(reasonAll, reasonMap)
					}
				} else if reasonSlice, ok := reasonInterface.([]interface{}); ok {
					log.WithContextInfof(ctx, "[DEBUG] Cast to []interface{}, length: %d", len(reasonSlice))
					// 兼容处理 []interface{} 类型
					for i, reasonItem := range reasonSlice {
						log.WithContextInfof(ctx, "[DEBUG] Processing interface{} item %d, type: %T, value: %+v", i, reasonItem, reasonItem)
						if reasonMap, ok := reasonItem.(map[string]interface{}); ok {
							log.WithContextInfof(ctx, "[DEBUG] Successfully cast to map[string]interface{}: %+v", reasonMap)
							reasonAll = append(reasonAll, reasonMap)
						} else {
							log.WithContextInfof(ctx, "[DEBUG] Failed to cast item %d to map[string]interface{}", i)
						}
					}
				} else {
					log.WithContextInfof(ctx, "[DEBUG] Failed to cast reason to any known type, actual type: %T", reasonInterface)
				}
			} else {
				log.WithContextInfof(ctx, "[DEBUG] port does not have 'reason' key")
			}
		}
		log.WithContextInfof(ctx, "[DEBUG] Finished processing portLists, reasonAll length: %d, content: %+v", len(reasonAll), reasonAll)

		// 对应PHP: $existSubdomainReason = collect($reasonAll)->where('type', Clue::TYPE_SUBDOMAIN)->first();
		var existSubdomainReason map[string]interface{}
		for _, reason := range reasonAll {
			if reasonType, exists := reason["type"]; exists {
				if utils.SafeInt(reasonType) == clues.TYPE_SUBDOMAIN {
					existSubdomainReason = reason
					break
				}
			}
		}

		if existSubdomainReason != nil {
			// 对应PHP: $subdomainCompanyName = ($existSubdomainReason['clue_company_name'] ?? '');
			subdomainCompanyName := utils.SafeString(existSubdomainReason["clue_company_name"])
			if subdomainCompanyName != "" {
				// 对应PHP: $clueCompanyName = [$subdomainCompanyName];
				finalClueCompanyName = []string{subdomainCompanyName}
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetCompanyName-取子域名线索的企业名称啊: assets_id=%s, subdomainCompanyName=%s, clueCompanyName=%v, oldName=%v",
					id, subdomainCompanyName, finalClueCompanyName, oldName)
			} else {
				// 对应PHP: } else { $clueCompanyName = array_values(array_unique(array_filter(array_merge(...)))); }
				finalClueCompanyName = s.mergeAllCompanyNames(portLists, clueCompanyName)
			}
		} else {
			// 对应PHP: $existDomainReason = collect($reasonAll)->where('type', Clue::TYPE_DOMAIN)->first();
			var existDomainReason map[string]interface{}
			for _, reason := range reasonAll {
				if reasonType, exists := reason["type"]; exists {
					if utils.SafeInt(reasonType) == clues.TYPE_DOMAIN {
						existDomainReason = reason
						break
					}
				}
			}

			if existDomainReason != nil {
				// 对应PHP: $domainCompanyName = ($existDomainReason['clue_company_name'] ?? '');
				domainCompanyName := utils.SafeString(existDomainReason["clue_company_name"])
				if domainCompanyName != "" {
					// 对应PHP: $clueCompanyName = [$domainCompanyName];
					finalClueCompanyName = []string{domainCompanyName}
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetCompanyName-取主域名线索的企业名称啊: assets_id=%s, domainCompanyName=%s, clueCompanyName=%v, oldName=%v",
						id, domainCompanyName, finalClueCompanyName, oldName)
				} else {
					// 对应PHP: } else { $clueCompanyName = array_values(array_unique(array_filter(array_merge(...)))); }
					finalClueCompanyName = s.mergeAllCompanyNames(portLists, clueCompanyName)
				}
			} else {
				// 对应PHP: $clueCompanyName = array_values(array_unique(array_filter(array_merge(...))))
				finalClueCompanyName = s.mergeAllCompanyNames(portLists, clueCompanyName)
			}
		}
	} else {
		// 对应PHP: } else { $clueCompanyName = array_values(array_unique(array_filter(array_merge(...)))); }
		finalClueCompanyName = s.mergeAllCompanyNames(portLists, clueCompanyName)
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetCompanyName-0: assets_id=%s, clueCompanyName=%v, oldName=%v",
		id, finalClueCompanyName, oldName)

	// 取到的企业名称为空或者只有一个时
	if len(finalClueCompanyName) == 0 || len(finalClueCompanyName) == 1 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetCompanyName-1: assets_id=%s, clueCompanyName=%v, oldName=%v",
			id, finalClueCompanyName, oldName)
		return finalClueCompanyName
	}

	// 用户的企业名称为空时 - 对应PHP: if (!$this->companyName) {
	if s.CompanyName == "" {
		result := []string{}
		if len(finalClueCompanyName) > 0 {
			result = append(result, finalClueCompanyName[0])
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetCompanyName-2: assets_id=%s, clueCompanyName=%v, oldName=%v",
			id, result, oldName)
		return result
	}

	// 用户的企业名称存在时 - 对应PHP: if (collect($clueCompanyName)->contains($this->companyName)) {
	for _, name := range finalClueCompanyName {
		if name == s.CompanyName {
			result := []string{s.CompanyName}
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetCompanyName-3: assets_id=%s, clueCompanyName=%s, oldName=%v",
				id, s.CompanyName, oldName)
			return result
		}
	}

	// 默认返回第一个 - 对应PHP: return [current($clueCompanyName)];
	result := []string{}
	if len(finalClueCompanyName) > 0 {
		result = append(result, finalClueCompanyName[0])
	}
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetCompanyName-4: assets_id=%s, clueCompanyName=%v, oldName=%v",
		id, result, oldName)
	return result
}

// mergeAllCompanyNames 合并所有企业名称 - 对应PHP的复杂array_merge逻辑
func (s *ScanForadarAsset) mergeAllCompanyNames(portLists []map[string]interface{}, clueCompanyName []string) []string {
	var allCompanyNames []string

	// 1. 对应PHP: collect($portLists)->pluck('clue_company_name')->collapse()->filter()->unique()->values()->toArray()
	// 提取每个port的clue_company_name字段，这个字段本身可能是数组，需要collapse压平
	for _, port := range portLists {
		if ccnInterface, exists := port["clue_company_name"]; exists {
			// clue_company_name可能是数组，需要处理
			if ccnSlice, ok := ccnInterface.([]interface{}); ok {
				// 如果是数组，遍历每个元素
				for _, item := range ccnSlice {
					if name, ok := item.(string); ok && strings.TrimSpace(name) != "" {
						allCompanyNames = append(allCompanyNames, strings.TrimSpace(name))
					}
				}
			} else if ccnString, ok := ccnInterface.(string); ok && strings.TrimSpace(ccnString) != "" {
				// 如果是字符串，直接添加
				allCompanyNames = append(allCompanyNames, strings.TrimSpace(ccnString))
			}
		}
	}

	// 2. 对应PHP: collect($portLists)->pluck('reason')->collapse()->pluck('clue_company_name')->filter()->unique()->values()->toArray()
	// 提取每个port的reason字段（数组），然后从reason项中提取clue_company_name
	for _, port := range portLists {
		if reasonInterface, exists := port["reason"]; exists {
			if reasonSlice, ok := reasonInterface.([]interface{}); ok {
				// reason是数组，遍历每个reason项
				for _, reasonItem := range reasonSlice {
					if reasonMap, ok := reasonItem.(map[string]interface{}); ok {
						// 从reason项中提取clue_company_name字段
						if ccnInterface, exists := reasonMap["clue_company_name"]; exists {
							if name, ok := ccnInterface.(string); ok && strings.TrimSpace(name) != "" {
								allCompanyNames = append(allCompanyNames, strings.TrimSpace(name))
							}
						}
					}
				}
			}
		}
	}

	// 3. 对应PHP: collect($portLists)->pluck('icp.company_name')->filter()->unique()->values()->toArray()
	// 提取每个port的icp对象中的company_name字段（点号表示嵌套访问）
	for _, port := range portLists {
		if icpInterface, exists := port["icp"]; exists {
			if icpMap, ok := icpInterface.(map[string]interface{}); ok {
				if companyNameInterface, exists := icpMap["company_name"]; exists {
					if name, ok := companyNameInterface.(string); ok && strings.TrimSpace(name) != "" {
						allCompanyNames = append(allCompanyNames, strings.TrimSpace(name))
					}
				}
			}
		}
	}

	// 4. 添加传入的clue_company_name - 对应PHP: $clue_company_name
	for _, name := range clueCompanyName {
		if strings.TrimSpace(name) != "" {
			allCompanyNames = append(allCompanyNames, strings.TrimSpace(name))
		}
	}

	// 5. 去重并过滤空值 - 对应PHP: array_values(array_unique(array_filter(...)))
	return utils.ListDistinct(allCompanyNames)
}

// AssetLevelResult 资产等级结果
type AssetLevelResult struct {
	Status       int  `json:"status"`
	Level        *int `json:"level"`
	ThreatenType *int `json:"threaten_type,omitempty"`
}

// getAssetLevel 获取资产等级
func (s *ScanForadarAsset) getAssetLevel(ctx context.Context, fAssets []*foradar_assets.ForadarAsset, oldAsset *fofaee_assets.FofaeeAssets) AssetLevelResult {
	var threatType *int

	// 初始化状态
	status := fofaee_assets.STATUS_DEFAULT
	if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
		status = fofaee_assets.STATUS_CLAIMED
	}

	if len(fAssets) == 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetLevel fAssets为空11111:%+v", map[string]interface{}{
			"status": status,
			"level":  nil,
		})
		return AssetLevelResult{Status: status, Level: nil, ThreatenType: threatType}
	}

	// 计算level（最小值）
	var level *int
	for _, asset := range fAssets {
		if assetLevel := utils.SafeInt(asset.Level); assetLevel > 0 {
			if level == nil || assetLevel < *level {
				level = &assetLevel
			}
		}
	}

	// 检查是否是仿冒资产 - 对应PHP: try { $is_fake_assets = $fAssets?->where('is_fake_assets',true)->pluck('is_fake_assets')->unique()->first(); }catch (\Throwable $eee){ $is_fake_assets = false; }
	var isFakeAssets bool
	func() {
		defer func() {
			if r := recover(); r != nil {
				isFakeAssets = false
			}
		}()
		for _, asset := range fAssets {
			if asset.IsFakeAssets {
				isFakeAssets = true
				break
			}
		}
	}()

	if oldAsset != nil {

		from := 0
		if oldAsset.Status != fofaee_assets.STATUS_DEFAULT {
			// 取ip端口维度的status为准
			statusArr := make([]int, 0)
			for _, asset := range fAssets {
				statusArr = append(statusArr, utils.SafeInt(asset.Status))
			}
			statusArr = utils.ListDistinct(statusArr)
			if len(statusArr) > 1 {
				// 存在2种不同类型的状态
				if utils.ListContains(statusArr, fofaee_assets.STATUS_CLAIMED) {
					status = fofaee_assets.STATUS_CLAIMED
					from = 0
				} else if utils.ListContains(statusArr, fofaee_assets.STATUS_UPLOAD) {
					status = fofaee_assets.STATUS_CLAIMED
					from = 1
				} else if utils.ListContains(statusArr, fofaee_assets.STATUS_IGNORE) {
					status = fofaee_assets.STATUS_IGNORE
					from = 2
				} else if utils.ListContains(statusArr, fofaee_assets.STATUS_THREATEN) {
					status = fofaee_assets.STATUS_THREATEN
					from = 3
				} else {
					status = fofaee_assets.STATUS_DEFAULT
					from = 4
				}
			} else if len(statusArr) == 1 {
				status = statusArr[0]
				from = 5
			} else {
				status = utils.SafeInt(oldAsset.Status)
				from = 6
			}
		} else {
			if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
				status = fofaee_assets.STATUS_CLAIMED
				from = 7
			} else {
				status = fofaee_assets.STATUS_DEFAULT
				from = 8
			}
		}

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetLevel 取的原来的ip维度的值啊: %+v", map[string]interface{}{
			"status":      status,
			"level":       level,
			"oldAsset_id": oldAsset.Id,
			"from":        from,
		})
		return AssetLevelResult{Status: status, Level: level, ThreatenType: threatType}
	}

	var domains []string
	if s.Task.AssetType != scan_task.TASK_ASSET_MANUAL {
		if isFakeAssets {
			// 仿冒资产
			status = foradar_assets.StatusThreatAsset
			for _, asset := range fAssets {
				if asset.IsFakeAssets {
					if tt := utils.SafeInt(asset.ThreatenType); tt > 0 {
						threatType = &tt
					}
					break
				}
			}

			ips := make([]string, 0)
			for _, asset := range fAssets {
				ips = append(ips, asset.Ip)
			}
			ips = utils.ListDistinct(ips)

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetLevel 当前资产被识别为威胁资产啊", map[string]interface{}{
				"status":     status,
				"threatType": threatType,
				"level":      level,
				"ip":         ips,
			})
		} else {
			if s.DetectTask != nil && s.DetectTask.ExpandSource == detect_assets_tasks.ExpandSourceDetect {
				// 单位测绘
				clueModel := clues.NewCluer()
				clueList, err := clueModel.ListAll(
					mysql.WithColumnValue("user_id", s.Task.UserId),
					mysql.WithColumnValue("group_id", s.DetectTask.GroupId),
					mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
					mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
					mysql.WithColumnValue("type", clues.TYPE_DOMAIN),
				)
				if err == nil {
					for _, clue := range clueList {
						domains = append(domains, clue.Content)
					}
				}

				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetLevel 取的单位测绘的线索源1111", map[string]interface{}{
					"scan_task_id": s.Task.ID,
					"domains":      domains,
				})
			} else {
				// 云端推荐
				if s.Task.Flag != "" {
					// 单位测绘没取到线索，那么肯定是云端推荐来的任务。取这个云端推荐任务下发的线索源
					// 对应PHP: $clueIdsArr = RecommendRecord::query()->where('user_id', $this->task->user_id)->where('id', $this->task->flag)->pluck('clue_id')->collapse()->filter()->values()->toArray();
					recommendRecordModel := recommend_record.NewRecommendRecordModel()
					recommendRecord, err := recommendRecordModel.FindByID(s.Task.Flag)
					if err == nil && recommendRecord != nil && len(recommendRecord.ClueId) > 0 {
						// 对应PHP: if($clueIdsArr){ $domains = Clue::query()->where('user_id', $this->task->user_id)->where('status', Clue::CLUE_PASS_STATUS)->where('is_deleted', Clue::NOT_DELETE)->where('type', Clue::TYPE_DOMAIN)->whereIn('id',$clueIdsArr)->pluck('content')->toArray();
						clueModel := clues.NewCluer()
						clueIds := make([]interface{}, 0, len(recommendRecord.ClueId))
						for _, id := range recommendRecord.ClueId {
							clueIds = append(clueIds, id)
						}
						clueList, err := clueModel.ListAll(
							mysql.WithColumnValue("user_id", s.Task.UserId),
							mysql.WithColumnValue("status", clues.CLUE_PASS_STATUS),
							mysql.WithColumnValue("is_deleted", clues.NOT_DELETE),
							mysql.WithColumnValue("type", clues.TYPE_DOMAIN),
							mysql.WithValuesIn("id", clueIds),
						)
						if err == nil {
							for _, clue := range clueList {
								domains = append(domains, clue.Content)
							}
						}
					}
				}

				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetLevel 取的推荐任务的线索源2222:%+v", map[string]interface{}{
					"scan_task_id": s.Task.ID,
					"domains":      domains,
				})

			}

			// 修正：提前计算 isOnline - 对应PHP: $isOnline = $fAssets?->where('online_state', ForadarAssets::ONLINE_STATUS_YES)->isNotEmpty();
			isOnline := false
			for _, asset := range fAssets {
				onlineState := utils.SafeInt(asset.OnlineState)
				if onlineState == foradar_assets.OnlineStatusYES {
					isOnline = true
					break
				}
			}

			// 处理domains前面加点 - 对应PHP: try { if($domains){ $newDomains = []; foreach ($domains as $ddddd){ $newDomains[] = '.'.$ddddd; } $domains = $newDomains; } }catch (\Throwable $e){ }
			func() {
				defer func() {
					if r := recover(); r != nil {
						// 对应PHP的catch，静默处理错误
					}
				}()
				if len(domains) > 0 {
					newDomains := make([]string, 0)
					for _, domain := range domains {
						newDomains = append(newDomains, "."+domain)
					}
					domains = newDomains
				}
			}()

			// 检查域名匹配
			isDomainOk := false
			for _, asset := range fAssets {
				for _, domain := range domains {
					if strings.HasSuffix(asset.Url, domain) {
						isDomainOk = true
						break
					}
				}
				if isDomainOk {
					break
				}
			}

			// A类,B类 资产&不在线 => 未知资产
			if level != nil && (*level == recommend_result.AssetsLevelA || *level == recommend_result.AssetsLevelB) {
				if isOnline {
					status = foradar_assets.StatusConfirmAsset
				} else {
					status = foradar_assets.StatusSuspectedAsset
				}

				// 判断是否是单位测绘任务
				if !isOnline && s.DetectTask != nil && s.DetectTask.OffAssetsTo > 0 && s.DetectTask.ExpandSource == detect_assets_tasks.ExpandSourceDetect {
					if s.DetectTask.OffAssetsTo == detect_assets_tasks.ToDefault {
						status = foradar_assets.StatusSuspectedAsset
					}
					if s.DetectTask.OffAssetsTo == detect_assets_tasks.ToTable {
						status = foradar_assets.StatusConfirmAsset
					}
				}

				// 判断是否是组织架构测绘任务 - 对应PHP: if ((!$isOnline) && ($this->organDetectTask->id ?? null)) { $status = ForadarAssets::STATUS_DEFAULT; }
				if !isOnline && s.OrganDetectTask != nil && s.OrganDetectTask.ID > 0 {
					status = foradar_assets.StatusSuspectedAsset
				}

				fAssetsIds := make([]string, 0)
				for _, asset := range fAssets {
					fAssetsIds = append(fAssetsIds, asset.ID)
				}
				fAssetsIds = utils.ListDistinct(fAssetsIds)

				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetLevel AB类资产定级", map[string]interface{}{
					"isOnline":   isOnline,
					"fAssetsIds": fAssetsIds,
					"isDomainOk": isDomainOk,
				})
			}

			// C类资产
			if level != nil && *level == recommend_result.AssetsLevelC && isDomainOk && isOnline {
				status = foradar_assets.StatusConfirmAsset

				fAssetsIds := make([]string, 0)
				for _, asset := range fAssets {
					fAssetsIds = append(fAssetsIds, asset.ID)
				}
				fAssetsIds = utils.ListDistinct(fAssetsIds)

				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetLevel C类资产", map[string]interface{}{
					"isOnline":   isOnline,
					"fAssetsIds": fAssetsIds,
					"isDomainOk": isDomainOk,
				})
			}
		}
	}

	ips := make([]string, 0)
	for _, asset := range fAssets {
		ips = append(ips, asset.Ip)
	}
	ips = utils.ListDistinct(ips)

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getAssetLevel 取level啊33333", map[string]interface{}{
		"status": status,
		"level":  level,
		"ip":     ips,
	})

	return AssetLevelResult{
		Status:       status,
		Level:        level,
		ThreatenType: threatType,
	}
}

// getDetectAssetsTasksId 获取资产任务ID
func (s *ScanForadarAsset) getDetectAssetsTasksId(asset interface{}) []uint64 {
	// 如果没有detectTask，直接返回资产的detect_assets_tasks_id
	if s.DetectTask == nil {
		// 尝试从asset中提取detect_assets_tasks_id字段
		if assetMap, ok := asset.(map[string]interface{}); ok {
			if detectTasksId, exists := assetMap["detect_assets_tasks_id"]; exists {
				return s.parseDetectAssetsTasksId(detectTasksId)
			}
		}
		return []uint64{}
	}

	// 如果有detectTask，需要合并ID
	var id []uint64

	// 尝试从asset中提取detect_assets_tasks_id字段
	if assetMap, ok := asset.(map[string]interface{}); ok {
		if detectTasksId, exists := assetMap["detect_assets_tasks_id"]; exists {
			id = s.parseDetectAssetsTasksId(detectTasksId)
		}
	}

	// 合并当前detectTask的ID
	if s.DetectTask != nil && s.DetectTask.ID > 0 {
		id = append(id, uint64(s.DetectTask.ID))
	}

	// 去重并返回
	return utils.ListDistinct(id)
}

// parseDetectAssetsTasksId 解析detect_assets_tasks_id字段
func (s *ScanForadarAsset) parseDetectAssetsTasksId(detectTasksId interface{}) []uint64 {
	if detectTasksId == nil {
		return []uint64{}
	}

	var result []uint64

	switch v := detectTasksId.(type) {
	case []interface{}:
		// 如果是数组
		for _, item := range v {
			if id := utils.SafeInt(item); id > 0 {
				result = append(result, uint64(id))
			}
		}
	case []uint64:
		// 如果已经是uint64数组
		result = v
	case []int:
		// 如果是int数组
		for _, id := range v {
			if id > 0 {
				result = append(result, uint64(id))
			}
		}
	case string:
		// 如果是字符串
		if id := utils.SafeInt(v); id > 0 {
			result = append(result, uint64(id))
		}
	case int:
		// 如果是整数
		if v > 0 {
			result = append(result, uint64(v))
		}
	case int64:
		// 如果是int64
		if v > 0 {
			result = append(result, uint64(v))
		}
	case uint64:
		// 如果是uint64
		if v > 0 {
			result = append(result, v)
		}
	default:
		// 其他类型，尝试转换为int
		if id := utils.SafeInt(v); id > 0 {
			result = append(result, uint64(id))
		}
	}

	return result
}

// getOrgDetectAssetsTasksId 获取组织架构测绘任务ID - 对应PHP: getOrgDetectAssetsTasksId
func (s *ScanForadarAsset) getOrgDetectAssetsTasksId(asset interface{}) []uint64 {
	// 对应PHP: if (!($this->organDetectTask ?? null)) { return $asset->org_detect_assets_tasks_id ?? []; }
	if s.OrganDetectTask == nil {
		return s.extractOrgDetectAssetsTasksId(asset)
	}

	// 对应PHP: $id = []; if (($asset->org_detect_assets_tasks_id ?? null)) { ... }
	var id []uint64
	if orgDetectTasksId := s.extractOrgDetectAssetsTasksId(asset); len(orgDetectTasksId) > 0 {
		id = orgDetectTasksId
	}

	// 对应PHP: array_values(array_unique(array_merge($id ?? [], [intval($this->organDetectTask->id)])));
	if s.OrganDetectTask != nil && s.OrganDetectTask.ID > 0 {
		id = append(id, uint64(s.OrganDetectTask.ID))
	}

	// 去重并返回
	return utils.ListDistinct(id)
}

// extractOrgDetectAssetsTasksId 从asset中提取org_detect_assets_tasks_id字段
func (s *ScanForadarAsset) extractOrgDetectAssetsTasksId(asset interface{}) []uint64 {
	// 处理map[string]interface{}类型
	if assetMap, ok := asset.(map[string]interface{}); ok {
		if orgDetectTasksId, exists := assetMap["org_detect_assets_tasks_id"]; exists {
			return s.parseOrgDetectAssetsTasksId(orgDetectTasksId)
		}
		return []uint64{}
	}

	// 处理结构体类型，直接返回空数组（因为在实际使用中，asset通常是map[string]interface{}）
	return []uint64{}
}

// getOrgId 获取组织架构的ID - 对应PHP: getOrgId
func (s *ScanForadarAsset) getOrgId(asset interface{}) []uint64 {
	// 对应PHP: if (!($this->organDetectTask ?? null)) { return $asset->organization_id ?? []; }
	if s.OrganDetectTask == nil {
		return s.extractOrganizationId(asset)
	}

	// 对应PHP: $id = []; if (($asset->organization_id ?? null)) { ... }
	var id []uint64
	if organizationId := s.extractOrganizationId(asset); len(organizationId) > 0 {
		id = organizationId
	}

	// 对应PHP: array_values(array_unique(array_merge($id ?? [], [intval($this->organDetectTask->organization_id)])));
	// 注意：在Go代码中，我们使用organDetectTask的company_id字段对应PHP中的organization_id
	if s.OrganDetectTask != nil && s.OrganDetectTask.CompanyId > 0 {
		id = append(id, uint64(s.OrganDetectTask.CompanyId))
	}

	// 去重并返回
	return utils.ListDistinct(id)
}

// extractOrganizationId 从asset中提取organization_id字段
func (s *ScanForadarAsset) extractOrganizationId(asset interface{}) []uint64 {
	// 处理map[string]interface{}类型
	if assetMap, ok := asset.(map[string]interface{}); ok {
		if organizationId, exists := assetMap["organization_id"]; exists {
			return s.parseOrganizationId(organizationId)
		}
		return []uint64{}
	}

	// 处理结构体类型，直接返回空数组（因为在实际使用中，asset通常是map[string]interface{}）
	return []uint64{}
}

// parseOrgDetectAssetsTasksId 解析org_detect_assets_tasks_id字段
// 对应PHP逻辑：if (is_array($asset->org_detect_assets_tasks_id)) { $id = $asset->org_detect_assets_tasks_id; } else { if (is_string(($asset->org_detect_assets_tasks_id ?? ""))) { $id = [$asset->org_detect_assets_tasks_id]; } }
func (s *ScanForadarAsset) parseOrgDetectAssetsTasksId(orgDetectTasksId interface{}) []uint64 {
	if orgDetectTasksId == nil {
		return []uint64{}
	}

	var result []uint64

	switch v := orgDetectTasksId.(type) {
	case []interface{}:
		// 对应PHP: if (is_array($asset->org_detect_assets_tasks_id)) { $id = $asset->org_detect_assets_tasks_id; }
		for _, item := range v {
			if id := utils.SafeInt(item); id > 0 {
				result = append(result, uint64(id))
			}
		}
	case []uint64:
		// 如果已经是uint64数组
		result = v
	case []int:
		// 如果是int数组
		for _, id := range v {
			if id > 0 {
				result = append(result, uint64(id))
			}
		}
	case string:
		// 对应PHP: if (is_string(($asset->org_detect_assets_tasks_id ?? ''))) { $id = [$asset->org_detect_assets_tasks_id]; }
		if v != "" {
			// 尝试JSON解析，如果失败，当作单个字符串处理
			var ids []uint64
			if err := json.Unmarshal([]byte(v), &ids); err == nil {
				result = ids
			} else if id := utils.SafeInt(v); id > 0 {
				result = append(result, uint64(id))
			}
		}
	case int, int64, uint64:
		// 如果是数字，转换为数组
		if id := utils.SafeInt(v); id > 0 {
			result = append(result, uint64(id))
		}
	default:
		// 其他类型，尝试转换为int
		if id := utils.SafeInt(v); id > 0 {
			result = append(result, uint64(id))
		}
	}

	return result
}

// parseOrganizationId 解析organization_id字段
// 对应PHP逻辑：if (is_array($asset->organization_id)) { $id = $asset->organization_id; } else { if ((is_string($asset->organization_id ?? ""))) { $id = [$asset->organization_id]; } }
func (s *ScanForadarAsset) parseOrganizationId(organizationId interface{}) []uint64 {
	if organizationId == nil {
		return []uint64{}
	}

	var result []uint64

	switch v := organizationId.(type) {
	case []interface{}:
		// 对应PHP: if (is_array($asset->organization_id)) { $id = $asset->organization_id; }
		for _, item := range v {
			if id := utils.SafeInt(item); id > 0 {
				result = append(result, uint64(id))
			}
		}
	case []uint64:
		// 如果已经是uint64数组
		result = v
	case []int:
		// 如果是int数组
		for _, id := range v {
			if id > 0 {
				result = append(result, uint64(id))
			}
		}
	case string:
		// 对应PHP: if ((is_string($asset->organization_id ?? ''))) { $id = [$asset->organization_id]; }
		if v != "" {
			// 尝试JSON解析，如果失败，当作单个字符串处理
			var ids []uint64
			if err := json.Unmarshal([]byte(v), &ids); err == nil {
				result = ids
			} else if id := utils.SafeInt(v); id > 0 {
				result = append(result, uint64(id))
			}
		}
	case int, int64, uint64:
		// 如果是数字，转换为数组
		if id := utils.SafeInt(v); id > 0 {
			result = append(result, uint64(id))
		}
	default:
		// 其他类型，尝试转换为int
		if id := utils.SafeInt(v); id > 0 {
			result = append(result, uint64(id))
		}
	}

	return result
}

// IpAssetData IP资产数据结构
type IpAssetData map[string]interface{}

// completeIpAssetData 完善IP资产数据
func (s *ScanForadarAsset) completeIpAssetData(ctx context.Context, id string, fAssets []*foradar_assets.ForadarAsset, tAsset *fofaee_task_assets.FofaeeTaskAssets, oldAsset *fofaee_assets.FofaeeAssets) IpAssetData {
	// 默认地理位置信息
	defaultGeo := map[string]interface{}{
		"continent": "",
		"zip":       "",
		"country":   "",
		"city":      "",
		"org":       "",
		"isp":       "",
		"lon":       0,
		"as":        "",
		"province":  "",
		"district":  "",
		"asn":       nil, // 数字字段应该是nil而不是空字符串
		"lat":       0,
		"as_name":   "",
	}

	// 过滤域名资产
	hostUniqueHash, hostList, portLists := s.filterDomainAsset(ctx, fAssets, tAsset)

	// 提取线索企业名称
	var clueCompanyNameList []string
	for _, asset := range fAssets {
		// 兼容多种数据类型的处理
		switch ccn := asset.ClueCompanyName.(type) {
		case []string:
			// 原来的处理方式 - []string 类型
			if len(ccn) > 0 {
				clueCompanyNameList = append(clueCompanyNameList, ccn...)
			}
		case []interface{}:
			// 新的处理方式 - []interface{} 类型
			for _, item := range ccn {
				if str, ok := item.(string); ok && str != "" {
					clueCompanyNameList = append(clueCompanyNameList, str)
				}
			}
		case string:
			// 单个字符串类型
			if ccn != "" {
				clueCompanyNameList = append(clueCompanyNameList, ccn)
			}
		case interface{}:
			// 其他 interface{} 类型，尝试转换为字符串
			if str := utils.SafeString(ccn); str != "" {
				clueCompanyNameList = append(clueCompanyNameList, str)
			}
		default:
			// 记录未知类型用于调试
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]无法解析ClueCompanyName，类型: %T, 值: %+v", asset.ClueCompanyName, asset.ClueCompanyName)
		}
	}
	clueCompanyNameList = utils.ListDistinct(clueCompanyNameList)

	// 获取企业名称
	clueCompanyName := s.getAssetCompanyName(ctx, portLists, clueCompanyNameList, id)

	// 合并规则标签 - 对应PHP: collect(array_merge($tAsset->rules ?? [], $oldAsset->rule_tags ?? []))->keyBy('rule_id')->values()->toArray()
	ruleMap := make(map[string]interface{}) // 使用map根据rule_id去重

	// 添加tAsset的规则
	if tAsset != nil && len(tAsset.Rules) > 0 {
		for _, rule := range tAsset.Rules {
			// 将fofaee_task_assets.Rule结构体转换为map
			ruleData := map[string]interface{}{
				"rule_id":            rule.RuleID,
				"product":            rule.Product,
				"cn_product":         rule.CnProduct,
				"category":           rule.Category,
				"cn_category":        rule.CnCategory,
				"level":              rule.Level,
				"parent_category":    rule.ParentCategory,
				"cn_parent_category": rule.CnParentCategory,
				"company":            rule.Company,
				"cn_company":         rule.CnCompany,
				"softhard":           rule.SoftHard,
			}
			if rule.RuleID != "" {
				ruleMap[rule.RuleID] = ruleData
			}
		}
	}

	// 添加oldAsset的规则标签
	if oldAsset != nil && len(oldAsset.RuleTags) > 0 {
		for _, rule := range oldAsset.RuleTags {
			// 将RuleTag结构体转换为map
			ruleMapData := map[string]interface{}{
				"rule_id":            rule.RuleId,
				"product":            rule.Product,
				"cn_product":         rule.CnProduct,
				"category":           rule.Category,
				"cn_category":        rule.CnCategory,
				"level":              rule.Level,
				"parent_category":    rule.ParentCategory,
				"cn_parent_category": rule.CnParentCategory,
				"company":            rule.Company,
				"cn_company":         rule.CnCompany,
				"softhard":           rule.Softhard,
			}
			if rule.RuleId != "" {
				ruleMap[rule.RuleId] = ruleMapData
			}
		}
	}

	// 将map转换为数组 - 对应PHP的values()
	var rules []interface{}
	for _, rule := range ruleMap {
		rules = append(rules, rule)
	}

	// 处理主机信息
	var host []string
	if len(fAssets) > 0 {
		subdomainAndDomain := make([]string, 0)
		urlHost := make([]string, 0)

		for _, item := range hostList {
			if subdomain, ok := item["subdomain"].(string); ok && subdomain != "" {
				subdomainAndDomain = append(subdomainAndDomain, subdomain)
			}
			if domain, ok := item["domain"].(string); ok && domain != "" {
				subdomainAndDomain = append(subdomainAndDomain, domain)
			}
			if url, ok := item["url"].(string); ok && url != "" {
				if subdomain := utils.GetSubdomain(url); subdomain != "" {
					urlHost = append(urlHost, url)
				}
			}
		}

		subdomainAndDomain = utils.ListDistinct(subdomainAndDomain)
		urlHost = utils.ListDistinct(urlHost)
		host = append(subdomainAndDomain, urlHost...)
	}

	// 处理重复的reason_arr
	var reasonArr []interface{}
	hasInContent := make(map[string]bool)
	for _, item := range hostList {
		if reasonInterface, exists := item["reason"]; exists {
			if reasonSlice, ok := reasonInterface.([]foradar_assets.AssetReason); ok {
				for _, reason := range reasonSlice {
					content := utils.SafeString(reason.Content)
					if content != "" && !hasInContent[content] {
						reasonArr = append(reasonArr, reason)
						hasInContent[content] = true
					}
				}
			}
		}
	}

	// 构建扫描资产数据
	log.WithContextInfof(ctx, "[DEBUG] 构建 scanAsset，rules 类型: %T, 长度: %d", rules, len(rules))
	log.WithContextInfof(ctx, "[DEBUG] hostList 类型: %T, 长度: %d", hostList, len(hostList))

	scanAsset := IpAssetData{
		"id":         id,
		"hosts":      host,
		"created_at": time.Now().Format("2006-01-02 15:04:05"),
		"updated_at": time.Now().Format("2006-01-02 15:04:05"),
		"ip":         s.completeIPV6(tAsset.Ip),
		"is_ipv6":    tAsset.IsIpv6,
		"user_id":    s.Task.UserId,
		"type":       s.Task.AssetType,
		"tags":       s.getIpTags(),
		"geo":        defaultGeo,
		"rule_tags":  rules,
		"port_list":  portLists,
		"port_size":  len(portLists),
		"host_list":  hostList,
		"reason_arr": reasonArr,
	}
	log.WithContextInfof(ctx, "[DEBUG] scanAsset 构建完成，rule_tags 类型: %T, host_list 类型: %T",
		scanAsset["rule_tags"], scanAsset["host_list"])

	// 任务ID处理
	var taskIds []uint64
	for _, asset := range fAssets {
		if len(asset.TaskID) > 0 {
			for _, id := range asset.TaskID {
				taskIds = append(taskIds, uint64(id))
			}
		}
	}
	if len(taskIds) == 0 {
		taskIds = []uint64{uint64(s.Task.ID)}
	}
	scanAsset["task_id"] = utils.ListDistinct(taskIds)

	// 检测资产任务ID
	if len(fAssets) > 0 {
		var detectAssetsTaskIds []uint64
		for _, asset := range fAssets {
			if len(asset.DetectAssetsTasksID) > 0 {
				for _, id := range asset.DetectAssetsTasksID {
					detectAssetsTaskIds = append(detectAssetsTaskIds, uint64(id))
				}
			}
		}
		if len(detectAssetsTaskIds) > 0 {
			scanAsset["detect_assets_tasks_id"] = utils.ListDistinct(detectAssetsTaskIds)
		} else if s.Task.DetectAssetsTasksId > 0 {
			scanAsset["detect_assets_tasks_id"] = []uint64{uint64(s.Task.DetectAssetsTasksId)}
		} else {
			scanAsset["detect_assets_tasks_id"] = []uint64{}
		}
	} else {
		if s.Task.DetectAssetsTasksId > 0 {
			scanAsset["detect_assets_tasks_id"] = []uint64{uint64(s.Task.DetectAssetsTasksId)}
		} else {
			scanAsset["detect_assets_tasks_id"] = []uint64{}
		}
	}

	// 云名称 - 对应PHP: 'cloud_name' => $fAssets ? $fAssets->pluck('cloud_name')->unique()->filter()->values() : []
	cloudNames := make([]string, 0)
	if len(fAssets) > 0 {
		cloudNameSet := make(map[string]bool)
		for _, asset := range fAssets {
			if asset.CloudName != "" {
				cloudNameSet[asset.CloudName] = true
			}
		}
		for cloudName := range cloudNameSet {
			cloudNames = append(cloudNames, cloudName)
		}
	}
	scanAsset["cloud_name"] = cloudNames

	// 在线状态
	onlineState := 0
	for _, port := range portLists {
		if isOpen := utils.SafeInt(port["is_open"]); isOpen > onlineState {
			onlineState = isOpen
		}
	}
	scanAsset["online_state"] = onlineState

	// 状态设置
	status := fofaee_assets.STATUS_DEFAULT
	if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
		status = fofaee_assets.STATUS_CLAIMED
	}
	scanAsset["status"] = status

	// 地理位置信息
	if len(fAssets) > 0 {
		if geoData := fAssets[0].Geo; geoData.Country != "" || geoData.City != "" {
			// 创建安全的geo map，避免nil值变成"<nil>"
			scanAsset["geo"] = map[string]interface{}{
				"continent": safeStringValue(geoData.Continent),
				"country":   safeStringValue(geoData.Country),
				"city":      safeStringValue(geoData.City),
				"province":  safeStringValue(geoData.Province),
				"district":  safeStringValue(geoData.District),
				"as":        safeStringValue(geoData.As),
				"asn":       safeNumericValue(geoData.Asn), // 安全处理数字类型
				"as_name":   safeStringValue(geoData.AsName),
				"org":       safeStringValue(geoData.Org),
				"isp":       safeStringValue(geoData.Isp),
				"zip":       safeStringValue(geoData.Zip),
				"lon":       safeNumericValue(geoData.Lon), // 安全处理数字类型
				"lat":       safeNumericValue(geoData.Lat), // 安全处理数字类型
			}
		}
	}

	scanAsset["reliability_score"] = 0

	// 企业名称 - 使用安全的字符串数组处理
	scanAsset["clue_company_name"] = safeStringArray(clueCompanyName)

	// PHP原始逻辑：处理在线状态优化
	// 为了处理当前这次扫描任务，可能扫的端口不全，需要判断当前资产是否已经存在于系统中
	onlineState = utils.GetIntValue(scanAsset["online_state"])
	if onlineState == 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]当前online_state为0，需要检查旧资产数据", "online_state", map[string]interface{}{
			"ip_assets_id": id,
		})

		// 查询原始IP资产数据：IpAssets::query()->where('id',$scanAsset['id'])->where('user_id',$this->task->user_id)->first()
		conditions := [][]interface{}{
			{"id", "=", id},
			{"user_id", "=", s.Task.UserId},
		}
		originIpAssets, err := elastic.AllByParams[fofaee_assets.FofaeeAssets](
			1000, conditions, []es2.Sorter{es2.NewFieldSort("id").Desc(), es2.NewFieldSort("ip").Desc()},
		)

		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]查询原始IP资产失败: %v", err)
		} else if len(originIpAssets) > 0 {
			originAsset := originIpAssets[0]

			// 获取当前扫描出的端口
			var portArrNow []int
			for _, port := range portLists {
				portArrNow = append(portArrNow, utils.SafeInt(port["port"]))
			}

			if len(portArrNow) == 0 {
				// 这次没扫出来端口，但是旧的ip数据是在线的，认为这个ip在线
				if originAsset.OnlineState == 1 {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]这次没扫出来端口，但是旧的ip数据是在线的，认为这个ip在线", "online_state-1", map[string]interface{}{
						"ip_assets_id": id,
					})
					scanAsset["online_state"] = 1
				}
			} else {
				// 这次扫出来端口,但是端口没全部扫描，旧的ip是有某个端口在线，认为这个ip在线
				if originAsset.OnlineState == 1 {
					originPortMap := make(map[int]int)
					for _, portInfo := range originAsset.PortList {
						originPortMap[utils.SafeInt(portInfo.Port)] = utils.SafeInt(portInfo.IsOpen)
					}

					for originPort, isOpen := range originPortMap {
						if !utils.ListContains(portArrNow, originPort) && isOpen == 1 {
							log.WithContextInfof(ctx, "[ScanForadarAssetHandler]这次扫出来端口,但是端口没全部扫描，旧的ip是有某个端口在线，认为这个ip在线", "online_state-2", map[string]interface{}{
								"ip_assets_id": id,
								"origin_port":  originPort,
								"portArrNow":   portArrNow,
							})
							scanAsset["online_state"] = 1
							break
						}
					}
				}
			}
		}

		// 如果online_state还是0，那么取task_assets判断是不是在线离线
		onlineState = utils.GetIntValue(scanAsset["online_state"])
		if onlineState == 0 {
			taskAssetConditions := [][]interface{}{
				{"ip", "=", scanAsset["ip"]},
				{"user_id", "=", s.Task.UserId},
				{"task_id", "=", s.Task.ID},
			}
			taskAssets, err := elastic.AllByParams[fofaee_task_assets.FofaeeTaskAssets](
				1000, taskAssetConditions, []es2.Sorter{es2.NewFieldSort("_id").Desc(), es2.NewFieldSort("ip").Desc()},
			)

			if err == nil && len(taskAssets) > 0 {
				taskAsset := taskAssets[0]
				if taskAsset.OnlineState > 0 {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeIpAssetData ip维度这个数据是离线啊，然后我取任务里面这个ip的在线状态", map[string]interface{}{
						"ipassets_id": id,
						"task_id":     s.Task.ID,
					})
					scanAsset["online_state"] = taskAsset.OnlineState
				}
			}
		}
	}

	// 获取资产等级并合并到scanAsset
	assetLevel := s.getAssetLevel(ctx, fAssets, oldAsset)
	for key, value := range map[string]interface{}{
		"status":        assetLevel.Status,
		"level":         assetLevel.Level,
		"threaten_type": assetLevel.ThreatenType,
	} {
		if value != nil {
			scanAsset[key] = value
		}
	}

	// PHP原始逻辑：威胁类型处理
	threatenType := scanAsset["threaten_type"]
	if threatenType != nil && utils.SafeString(threatenType) != "" && utils.SafeInt(threatenType) == 0 {
		delete(scanAsset, "threaten_type")
	}
	if threatenType != nil && utils.SafeInt(threatenType) > 0 {
		// PHP代码：BlackKeywordType::query()->where('id',$scanAsset['threaten_type'])->value('name')
		blackKeywordTypeModel := black_keyword_type.NewTypeModel()
		keywordType, err := blackKeywordTypeModel.First(func(db *gorm.DB) {
			db.Where("id = ?", utils.SafeInt(threatenType))
		})
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]查询威胁类型名称失败: %v", err)
			scanAsset["threaten_type_name"] = ""
		} else {
			scanAsset["threaten_type_name"] = keywordType.Name
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeIpAssetData", fmt.Sprintf("TaskId:%d,ip:%s", s.Task.ID, tAsset.Ip), map[string]interface{}{
		"scanAsset_tags": scanAsset["tags"],
	})

	// 处理旧资产合并
	if oldAsset != nil {
		// 合并状态和类型
		scanAsset["status"] = utils.SafeInt(oldAsset.Status)
		scanAsset["type"] = utils.SafeInt(oldAsset.Type)
		scanAsset["created_at"] = oldAsset.CreatedAt

		if oldAsset.Status != 0 {
			scanAsset["status"] = oldAsset.Status
		}
		if oldAsset.Type != 0 {
			scanAsset["type"] = oldAsset.Type
		}
		if oldAsset.CreatedAt != "" {
			scanAsset["created_at"] = oldAsset.CreatedAt
		}

		// 对应PHP: if ($asset->status == ForadarAssets::STATUS_UPLOAD) { $scanAsset['status'] = ForadarAssets::STATUS_CLAIMED; }
		if oldAsset.Status == foradar_assets.StatusUploadAsset {
			scanAsset["status"] = fofaee_assets.STATUS_CLAIMED
		}
		// 对应PHP: if ($this->task->asset_type == \App\Models\MySql\Task::TASK_ASSET_MANUAL) { $scanAsset['status'] = ForadarAssets::STATUS_CLAIMED; }
		if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
			scanAsset["status"] = fofaee_assets.STATUS_CLAIMED
		}

		// 合并端口数据 - 对应PHP: $ports = collect($portLists)->pluck('port')->toArray() ?? [];
		ports := make([]interface{}, 0)
		for _, portItem := range portLists {
			if portVal := portItem["port"]; portVal != nil {
				ports = append(ports, portVal)
			}
		}

		// 打印详细的端口类型信息
		portTypes := make([]string, 0)
		for i, port := range ports {
			portTypes = append(portTypes, fmt.Sprintf("ports[%d]=%v(type:%T)", i, port, port))
		}
		log.WithContextInfof(ctx, "[DEBUG_PORT_MERGE] 开始合并端口数据，IP: %s, 当前端口列表: %v, 端口类型详情: %v, 旧资产端口数量: %d",
			tAsset.Ip, ports, portTypes, len(oldAsset.PortList))

		// 对应PHP: foreach ($oldAsset->port_list ?? [] as $portInfo)
		addedPortCount := 0
		skippedPortCount := 0
		for _, portInfo := range oldAsset.PortList {
			// 对应PHP: if (($portInfo['port'] ?? null) && (!in_array($portInfo['port'] ?? '', $ports)))
			if portInfo.Port != nil {
				// 检查端口是否已经存在于当前端口列表中 - 修复类型比较问题
				found := false
				oldPortInt := utils.SafeInt(portInfo.Port)
				for _, existingPort := range ports {
					existingPortInt := utils.SafeInt(existingPort)
					if existingPortInt == oldPortInt {
						found = true
						break
					}
				}

				log.WithContextInfof(ctx, "[DEBUG_PORT_MERGE] 检查旧端口: %v (type:%T, 转换为int: %d), 是否已存在: %t",
					portInfo.Port, portInfo.Port, oldPortInt, found)

				// 如果端口不存在，则添加到port_list中 - 对应PHP: array_push($scanAsset['port_list'], $portInfo);
				if !found {
					portMap := map[string]interface{}{
						"port":         portInfo.Port,
						"protocol":     portInfo.Protocol,
						"banner":       portInfo.Banner,
						"title":        portInfo.Title,
						"url":          portInfo.Url,
						"domain":       portInfo.Domain,
						"subdomain":    portInfo.Subdomain,
						"is_open":      portInfo.IsOpen,
						"online_state": portInfo.OnlineState,
						"header":       portInfo.Header,
					}
					if portListSlice, ok := scanAsset["port_list"].([]map[string]interface{}); ok {
						scanAsset["port_list"] = append(portListSlice, portMap)
						addedPortCount++
						log.WithContextInfof(ctx, "[DEBUG_PORT_MERGE] 添加旧端口: %v, 协议: %s", portInfo.Port, portInfo.Protocol)
					}
				} else {
					skippedPortCount++
					log.WithContextInfof(ctx, "[DEBUG_PORT_MERGE] 跳过重复端口: %v", portInfo.Port)
				}
			}
		}

		log.WithContextInfof(ctx, "[DEBUG_PORT_MERGE] 端口合并完成，IP: %s, 添加端口数: %d, 跳过端口数: %d",
			tAsset.Ip, addedPortCount, skippedPortCount)

		// 合并主机数据 - 对应PHP: foreach ($oldAsset->host_list ?? [] as $hostInfo)
		log.WithContextInfof(ctx, "[DEBUG_HOST_MERGE] 开始合并主机数据，IP: %s, 当前主机hash列表: %v, 旧资产主机数量: %d",
			tAsset.Ip, hostUniqueHash, len(oldAsset.HostList))

		addedHostCount := 0
		skippedHostCount := 0
		for _, hostInfo := range oldAsset.HostList {
			// 对应PHP: $hash = md5(($hostInfo['port'] ?? '').($hostInfo['protocol'] ?? '').$hostInfo['subdomain'] ?? '');
			port := utils.SafeString(hostInfo.Port)
			protocol := utils.SafeString(hostInfo.Protocol)
			subdomain := utils.SafeString(hostInfo.Subdomain)
			hash := utils.Md5Hash(port + protocol + subdomain)

			// 对应PHP: if (!in_array($hash, $hostUniqueHash) && getSubdomain(($hostInfo['url'] ?? '')))
			found := false
			for _, existingHash := range hostUniqueHash {
				if existingHash == hash {
					found = true
					break
				}
			}

			// 检查URL是否有效（对应PHP的getSubdomain函数检查）
			url := utils.SafeString(hostInfo.Url)
			hasValidSubdomain := url != "" && (strings.Contains(url, ".") || strings.HasPrefix(url, "http"))

			log.WithContextInfof(ctx, "[DEBUG_HOST_MERGE] 检查旧主机，端口: %s, 协议: %s, 子域名: %s, hash: %s, 已存在: %t, URL有效: %t, URL: %s",
				port, protocol, subdomain, hash, found, hasValidSubdomain, url)

			if !found && hasValidSubdomain {
				hostMap := map[string]interface{}{
					"port":      hostInfo.Port,
					"protocol":  hostInfo.Protocol,
					"subdomain": hostInfo.Subdomain,
					"url":       hostInfo.Url,
					"domain":    hostInfo.Domain,
					"title":     hostInfo.Title,
					"banner":    hostInfo.Banner,
					"header":    hostInfo.Header,
				}
				// 安全地处理host_list追加操作
				if hostListValue, exists := scanAsset["host_list"]; exists {
					switch hostList := hostListValue.(type) {
					case []map[string]interface{}:
						scanAsset["host_list"] = append(hostList, hostMap)
						addedHostCount++
						log.WithContextInfof(ctx, "[DEBUG_HOST_MERGE] 添加旧主机([]map)，端口: %s, 协议: %s, 子域名: %s, URL: %s",
							port, protocol, subdomain, url)
					case []interface{}:
						scanAsset["host_list"] = append(hostList, hostMap)
						addedHostCount++
						log.WithContextInfof(ctx, "[DEBUG_HOST_MERGE] 添加旧主机([]interface)，端口: %s, 协议: %s, 子域名: %s, URL: %s",
							port, protocol, subdomain, url)
					default:
						log.WithContextWarnf(ctx, "[DEBUG_HOST_MERGE] host_list类型不支持追加操作，类型: %T", hostListValue)
					}
				} else {
					// 如果host_list不存在，创建新的
					scanAsset["host_list"] = []map[string]interface{}{hostMap}
					addedHostCount++
					log.WithContextInfof(ctx, "[DEBUG_HOST_MERGE] 创建新host_list并添加旧主机，端口: %s, 协议: %s, 子域名: %s, URL: %s",
						port, protocol, subdomain, url)
				}
			} else {
				skippedHostCount++
				if found {
					log.WithContextInfof(ctx, "[DEBUG_HOST_MERGE] 跳过重复主机，hash: %s", hash)
				} else {
					log.WithContextInfof(ctx, "[DEBUG_HOST_MERGE] 跳过无效URL主机，URL: %s", url)
				}
			}
		}

		log.WithContextInfof(ctx, "[DEBUG_HOST_MERGE] 主机合并完成，IP: %s, 添加主机数: %d, 跳过主机数: %d",
			tAsset.Ip, addedHostCount, skippedHostCount)

		// 合并标签
		oldTags := oldAsset.Tags
		currentTags := scanAsset["tags"].([]int)
		allTags := append(currentTags, oldTags...)
		scanAsset["tags"] = utils.ListDistinct(allTags)

		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeIpAssetData", fmt.Sprintf("TaskId:%d,ip:%s", s.Task.ID, tAsset.Ip), map[string]interface{}{
			"current_tags":   currentTags,
			"scanAsset_tags": scanAsset["tags"],
			"oldAsset_tags":  oldTags,
		})

		// 更新任务ID和检测任务ID
		// PHP原始逻辑：getTaskId
		if len(oldAsset.TaskId) == 0 {
			scanAsset["task_id"] = []uint64{uint64(s.Task.ID)}
		} else {
			taskIds := make([]uint64, 0)
			for _, id := range oldAsset.TaskId {
				taskIds = append(taskIds, uint64(id))
			}
			taskIds = append(taskIds, uint64(s.Task.ID))
			scanAsset["task_id"] = utils.ListDistinct(taskIds)
		}
		scanAsset["detect_assets_tasks_id"] = s.getDetectAssetsTasksId(oldAsset)
		scanAsset["org_detect_assets_tasks_id"] = s.getOrgDetectAssetsTasksId(oldAsset) // 新增：组织架构测绘任务ID
		scanAsset["organization_id"] = s.getOrgId(oldAsset)                             // 新增：组织架构ID
	} else {
		// 新增资产时也需要设置组织架构相关字段
		scanAsset["org_detect_assets_tasks_id"] = s.getOrgDetectAssetsTasksId(map[string]interface{}{})
		scanAsset["organization_id"] = s.getOrgId(map[string]interface{}{})
	}

	// PHP原始逻辑：Company::query()->where('id', $this->task->company_id)->increment('used_ip_asset')
	if s.Task.CompanyId > 0 && s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
		// 查询Company模型并对used_ip_asset字段递增1
		companyModel := company.NewCompanyModel()
		companyInfo, err := companyModel.FindById(uint64(s.Task.CompanyId), 0)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]查询企业信息失败: %v", err)
		} else {
			// 使用UpdateLimit方法递增used_ip_asset
			if err := companyInfo.UpdateLimit("ip_asset", 1, true); err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]递增企业已使用IP资产数量失败: %v", err)
			} else {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功递增企业已使用IP资产数量", "incrementCompanyUsedIpAsset", map[string]interface{}{
					"company_id": s.Task.CompanyId,
				})
			}
		}
	}

	// IP段推荐来的资产设置为台账数据
	allReasonType := make([]int, 0)
	if reasonArrSlice, ok := scanAsset["reason_arr"].([]interface{}); ok {
		for _, reason := range reasonArrSlice {
			if reasonMap, ok := reason.(foradar_assets.AssetReason); ok {
				allReasonType = append(allReasonType, utils.SafeInt(reasonMap.Type))
			}
		}
	}

	scanStatus := utils.GetIntValue(scanAsset["status"])
	if utils.ListContains(allReasonType, clues.TYPE_IP) && scanStatus != fofaee_assets.STATUS_CLAIMED {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]把当前ip段推荐来的资产设置为台账资产 completeIpAssetData: %+v", map[string]interface{}{
			"scanAsset_ip": scanAsset["ip"],
			"id":           scanAsset["id"],
		})
		scanAsset["status"] = fofaee_assets.STATUS_CLAIMED
	}

	// 风险匹配逻辑
	canMatchRisk := false

	if utils.GetIntValue(scanAsset["status"]) == fofaee_assets.STATUS_CLAIMED && s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
		canMatchRisk = true
	}
	if !canMatchRisk {
		if s.DetectTask != nil && s.DetectTask.IsCheckRisk > 0 {
			canMatchRisk = true
		}
	}

	// 对应PHP: if (($this->detectTask->id ?? null) && (($this->detectTask->expand_source ?? null) == DetectAssetsTask::EXPAND_FROM_CLOUD)) { $canMatchRisk = true; }
	if !canMatchRisk {
		if s.DetectTask != nil && s.DetectTask.ID > 0 && s.DetectTask.ExpandSource == detect_assets_tasks.ExpandSourceRecommend {
			canMatchRisk = true
		}
	}

	if canMatchRisk {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]当前扫描任务进行风险匹配,%s:%+v", "canMatchRisk", map[string]interface{}{
			"task_id":      s.Task.ID,
			"scanAsset_id": scanAsset["id"],
		})
		// 认领资产才匹配风险事件
		// 设置风险级别字段risk_type
		// PHP代码：MicroClientService::getIns($host)->assetsIpRiskTypeNew($scanAsset)
		// 对应RPC接口：system_rule/match -> scanService的EngineRuleAssetMatch
		// 调用pkg/rule_engine包中的AssetMatch函数
		scanAssetJSON, err := json.Marshal(scanAsset)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]序列化资产数据失败: %v", err)
		} else {
			req := &pb.EngineRuleAssetMatchRequest{
				IpAssets: string(scanAssetJSON),
			}
			rsp := &pb.EngineRuleAssetMatchResponse{}

			if err := rule_engine.AssetMatch(ctx, req, rsp); err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]调用风险匹配服务失败: %v", err)
			} else {
				ipRiskTypeArr := rsp.IpRiskType
				portRiskType := rsp.PortRiskTypes

				scanAsset["risk_type"] = ipRiskTypeArr
				if len(ipRiskTypeArr) > 0 {
					// 记录一下这个风险事件ip
					// PHP代码：Cache::get('scan_risk_ip:'.$this->task->id)
					// 直接调用redis设置
					redisClient := redisInit.GetInstance()
					riskIpKey := fmt.Sprintf("scan_risk_ip:%d", s.Task.ID)

					// 获取已有的风险IP列表
					existingIps, err := redisClient.SMembers(ctx, riskIpKey).Result()
					if err != nil && err != goRedis.Nil {
						log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取风险IP缓存失败: %v", err)
					}

					// 添加当前IP
					currentIp := utils.SafeString(scanAsset["ip"])
					if !utils.ContainsString(existingIps, currentIp) {
						if err := redisClient.SAdd(ctx, riskIpKey, currentIp).Err(); err != nil {
							log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]缓存风险IP失败: %v", err)
						}
					}

					// 设置过期时间72小时
					redisClient.Expire(ctx, riskIpKey, 72*time.Hour)

					// 记录当前任务风险事件的数量，按照任务维度
					// PHP代码：Cache::get('scan_risk_type_num:'.$this->task->id)
					riskNumKey := fmt.Sprintf("scan_risk_type_num:%d", s.Task.ID)
					currentCount, err := redisClient.Get(ctx, riskNumKey).Int()
					if err != nil && !errors.Is(err, goRedis.Nil) {
						log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]获取风险类型数量缓存失败: %v", err)
						currentCount = 0
					}

					newCount := currentCount + len(ipRiskTypeArr)
					if err := redisClient.Set(ctx, riskNumKey, newCount, 72*time.Hour).Err(); err != nil {
						log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]缓存风险类型数量失败: %v", err)
					}
				}

				// 取端口上的风险，重组portlist数据
				if len(portRiskType) > 0 {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]更新了端口的风险资产", "portRiskType", map[string]interface{}{
						"portRiskType": portRiskType,
						"scanAsset_id": scanAsset["id"],
					})
					if portListSlice, ok := scanAsset["port_list"].([]map[string]interface{}); ok {
						for k, p := range portListSlice {
							port := uint64(utils.SafeInt(p["port"]))
							if riskTypes, ok := portRiskType[port]; ok && riskTypes != nil {
								portListSlice[k]["risk_type"] = riskTypes.RiskTypes
							} else {
								portListSlice[k]["risk_type"] = []uint64{}
							}
						}
						scanAsset["port_list"] = portListSlice
					}
				}
			}
		}
	} else {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]当前扫描任务不进行风险匹配 canMatchRisk", map[string]interface{}{
			"task_id": s.Task.ID,
		})
	}

	// 获取IP对应的历史域名数据
	var domainHistory map[string]interface{}
	if s.Task.AssetType == scan_task.TASK_ASSET_CLOUD {
		// PHP代码：MicroClientService::getIns($host)->getDomainHistoryByIp($scanAsset['ip'])
		// 对应RPC接口：ip/ip_domain_history -> coreService

		// 详细检查scanAsset中的IP字段
		// 创建简化版本的scanAsset用于日志打印，去掉大字段
		simplifiedAsset := make(map[string]interface{})
		for key, value := range scanAsset {
			if key == "port_list" || key == "portList" {
				// 简化port_list，去掉证书、banner、header等大字段
				if portList, ok := value.([]interface{}); ok {
					simplifiedPorts := make([]interface{}, 0, len(portList))
					for _, port := range portList {
						if portMap, ok := port.(map[string]interface{}); ok {
							simplifiedPort := make(map[string]interface{})
							for portKey, portValue := range portMap {
								// 只保留关键字段，去掉证书、banner、header等
								if portKey != "certs" && portKey != "banner" && portKey != "header" && portKey != "headers" {
									simplifiedPort[portKey] = portValue
								}
							}
							simplifiedPorts = append(simplifiedPorts, simplifiedPort)
						} else {
							simplifiedPorts = append(simplifiedPorts, port)
						}
					}
					simplifiedAsset[key] = simplifiedPorts
				} else {
					simplifiedAsset[key] = value
				}
			} else if key == "host_list" || key == "hostList" {
				// 简化host_list，去掉证书、banner、header等大字段
				// 安全地处理不同类型的host_list
				switch hostList := value.(type) {
				case []interface{}:
					simplifiedHosts := make([]interface{}, 0, len(hostList))
					for _, host := range hostList {
						if hostMap, ok := host.(map[string]interface{}); ok {
							simplifiedHost := make(map[string]interface{})
							for hostKey, hostValue := range hostMap {
								// 只保留关键字段，去掉证书、banner、header等
								if hostKey != "certs" && hostKey != "banner" && hostKey != "header" && hostKey != "headers" {
									simplifiedHost[hostKey] = hostValue
								}
							}
							simplifiedHosts = append(simplifiedHosts, simplifiedHost)
						} else {
							simplifiedHosts = append(simplifiedHosts, host)
						}
					}
					simplifiedAsset[key] = simplifiedHosts
				case []map[string]interface{}:
					simplifiedHosts := make([]interface{}, 0, len(hostList))
					for _, hostMap := range hostList {
						simplifiedHost := make(map[string]interface{})
						for hostKey, hostValue := range hostMap {
							// 只保留关键字段，去掉证书、banner、header等
							if hostKey != "certs" && hostKey != "banner" && hostKey != "header" && hostKey != "headers" {
								simplifiedHost[hostKey] = hostValue
							}
						}
						simplifiedHosts = append(simplifiedHosts, simplifiedHost)
					}
					simplifiedAsset[key] = simplifiedHosts
				default:
					log.WithContextWarnf(ctx, "[DEBUG] host_list简化处理：不支持的类型 %T", value)
					simplifiedAsset[key] = value
				}
			} else {
				// 其他字段直接复制
				simplifiedAsset[key] = value
			}
		}
		// log.WithContextInfof(ctx, "[ScanForadarAssetHandler]scanAsset简化内容: %+v", simplifiedAsset)
		ipRaw := scanAsset["ip"]
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]IP原始值: %+v (类型: %T)", ipRaw, ipRaw)

		ipValue := utils.SafeString(scanAsset["ip"])
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]准备调用IpDomainHistory - IP: '%s', 长度: %d, UserId: %d, TaskId: %d",
			ipValue, len(ipValue), s.Task.UserId, s.Task.ID)

		// 检查IP是否为空或无效
		if ipValue == "" {
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]IP值为空，跳过IpDomainHistory调用")
		} else {
			req := &corePB.IpDomainHistoryRequest{
				UserId: s.Task.UserId,
				Ip:     []string{ipValue},
				IsAll:  false,
			}

			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]IpDomainHistory请求参数: %+v", req)
			rsp, err := corePB.GetProtoCoreClient().IpDomainHistory(ctx, req, utils.RpcTimeoutDur(10*time.Minute), microx.ServerTimeoutDur(9*time.Minute))
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]IpDomainHistory响应: err=%v, rsp=%+v", err, rsp)

			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]调用coreService.IpDomainHistory失败: %v", err)
				domainHistory = map[string]interface{}{
					"items": []map[string]interface{}{},
				}
			} else {
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]IpDomainHistory调用成功，响应项数量: %d", len(rsp.Items))
				// 转换响应数据格式
				items := make([]map[string]interface{}, 0)
				for _, item := range rsp.Items {
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]处理IP项: %s, 域名数量: %d", item.Ip, len(item.Items))
					itemData := map[string]interface{}{
						"ip":    item.Ip,
						"items": make([]map[string]interface{}, 0),
					}
					for _, domainItem := range item.Items {
						domainData := map[string]interface{}{
							"domain":      domainItem.Domain,
							"icp_company": domainItem.IcpCompany,
							"found_time":  domainItem.FoundTime,
							"source":      domainItem.Source,
						}
						itemData["items"] = append(itemData["items"].([]map[string]interface{}), domainData)
					}
					items = append(items, itemData)
				}
				domainHistory = map[string]interface{}{
					"items": items,
				}
				log.WithContextInfof(ctx, "[ScanForadarAssetHandler]转换后的domainHistory: %+v", domainHistory)
			}
		}
	} else {
		// 非云端资产类型，设置空的域名历史
		domainHistory = map[string]interface{}{
			"items": []map[string]interface{}{},
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]getDomainHistoryByIp", "getDomainHistoryByIp", map[string]interface{}{
		"ip":     scanAsset["ip"],
		"result": domainHistory,
	})

	if itemsSlice, ok := domainHistory["items"].([]map[string]interface{}); ok && len(itemsSlice) > 0 {
		// 查找匹配的IP项 - 对应PHP逻辑：foreach ($list as $l) { if ($returnIp == $scanAsset['ip']) { $scanAsset['ip_domain_history'] = $l['items']; } break; }
		// 注意：PHP代码中break在if外面，意味着只检查第一个项目，这里保持一致
		item := itemsSlice[0]
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]检查IP匹配 - 返回IP: %s, 资产IP: %s",
			utils.SafeString(item["ip"]), utils.SafeString(scanAsset["ip"]))
		if utils.SafeString(item["ip"]) == utils.SafeString(scanAsset["ip"]) {
			scanAsset["ip_domain_history"] = item["items"]
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]IP匹配成功，设置ip_domain_history: %+v", item["items"])
		}
	}

	// 判断最新的一条域名实时解析是否匹配ip，域名对应的icp备案是否匹配ip对应的企业名称
	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]检查ip_domain_history字段: %+v", scanAsset["ip_domain_history"])

	// 尝试两种类型转换：[]interface{} 和 []map[string]interface{}
	var domainHistorySlice []map[string]interface{}
	var convertSuccess bool

	if slice1, ok := scanAsset["ip_domain_history"].([]interface{}); ok && len(slice1) > 0 {
		// 转换 []interface{} 到 []map[string]interface{}
		for _, item := range slice1 {
			if mapItem, ok := item.(map[string]interface{}); ok {
				domainHistorySlice = append(domainHistorySlice, mapItem)
			}
		}
		convertSuccess = len(domainHistorySlice) > 0
	} else if slice2, ok := scanAsset["ip_domain_history"].([]map[string]interface{}); ok && len(slice2) > 0 {
		// 直接使用 []map[string]interface{}
		domainHistorySlice = slice2
		convertSuccess = true
	}

	if convertSuccess && len(domainHistorySlice) > 0 {
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ip_domain_history转换成功，长度: %d", len(domainHistorySlice))
		firstDomain := domainHistorySlice[0]
		domain := utils.SafeString(firstDomain["domain"])
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]处理第一个域名: %s", domain)

		// 判断域名实时解析
		var nowParseIpArr []string
		if isIPv6, ok := scanAsset["is_ipv6"].(bool); ok && isIPv6 {
			nowParseIpArr = dns.GetAAAARecords(domain)
		} else {
			nowParseIpArr = dns.GetARecords(domain)
		}

		if utils.ContainsString(nowParseIpArr, utils.SafeString(scanAsset["ip"])) {
			scanAsset["ip_match"] = fofaee_assets.DOMAIN_PARSE_MATCH
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]域名解析匹配成功")
		} else {
			scanAsset["ip_match"] = fofaee_assets.DOMAIN_NOT_PARSE_MATCH
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]域名解析不匹配")
		}

		// 判断ICP备案匹配
		nowIcpCompanyName := utils.SafeString(firstDomain["icp_company"])
		if nowIcpCompanyName != "" {
			if clueCompanyList, ok := scanAsset["clue_company_name"].([]string); ok {
				if utils.ContainsString(clueCompanyList, nowIcpCompanyName) {
					scanAsset["company_match"] = fofaee_assets.DOMAIN_ICP_MATCH
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ICP备案匹配成功")
				} else {
					scanAsset["company_match"] = fofaee_assets.DOMAIN_NOT_ICP_MATCH
					log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ICP备案不匹配")
				}
			}
		}
		scanAsset["lateast_parse_domain"] = domain
		scanAsset["lateast_parse_domain_time"] = utils.SafeString(firstDomain["found_time"])
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]设置最新解析域名: %s, 时间: %s",
			domain, utils.SafeString(firstDomain["found_time"]))
	} else {
		log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]ip_domain_history字段为空或转换失败")
	}

	// log.WithContextInfof(ctx, "[ScanForadarAssetHandler]completeIpAssetData-final-最终出来的ip维度的数据", map[string]interface{}{
	// 	"status": scanAsset["status"],
	// }, map[string]interface{}{
	// 	"ip_assets": scanAsset,
	// })

	return scanAsset
}

// getForadarAssetsByIp 根据IP和用户ID获取ForadarAssets数据 - 对应PHP: ForadarAssets::query()->where('ip', $tAsset->ip)->where("user_id",$this->task->user_id)->get()
func (s *ScanForadarAsset) getForadarAssetsByIp(ctx context.Context, ip string, userId uint64) ([]*foradar_assets.ForadarAsset, error) {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	assets, err := elastic.AllByParams[foradar_assets.ForadarAsset](1000, [][]interface{}{
		{"ip", "=", ip},
		{"user_id", "=", userId},
	}, []es2.Sorter{
		es2.NewFieldSort("id").Desc(),
	})
	if err != nil {
		return nil, fmt.Errorf("查询ForadarAssets失败: %v", err)
	}

	return assets, nil
}

// getIpAssetById 根据ID获取IpAssets数据 - 对应PHP: IpAssets::query()->where('_id', $id)->first()
func (s *ScanForadarAsset) getIpAssetById(ctx context.Context, id string) (*fofaee_assets.FofaeeAssets, error) {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	asset, err := elastic.FirstByParams[fofaee_assets.FofaeeAssets]([][]interface{}{
		{"_id", "=", id},
	})
	if err != nil {
		// 如果没有找到，返回nil而不是错误
		return nil, nil
	}

	return asset, nil
}

// handleStatusConsistency 处理状态一致性问题 - 对应PHP状态处理逻辑
func (s *ScanForadarAsset) handleStatusConsistency(ctx context.Context, taskId uint64, scanAsset IpAssetData, fAssets []*foradar_assets.ForadarAsset) {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 获取当前IP端口维度的status数组状态值 - 对应PHP: $statusArr = ForadarAssets::query()->where('user_id',$this->task->user_id)->where('ip',$scanAsset['ip'])->pluck('status')->unique()->values()->toArray();
	statusMap := make(map[int]bool)
	for _, fAsset := range fAssets {
		var status int
		switch v := fAsset.Status.(type) {
		case int:
			status = v
		case float64:
			status = int(v)
		case float32:
			status = int(v)
		default:
			log.WithContextWarnf(ctx, "[ScanForadarAssetHandler]无法转换status类型: %T, value: %v", fAsset.Status, fAsset.Status)
			continue
		}
		statusMap[status] = true
	}

	var statusArr []int
	for status := range statusMap {
		statusArr = append(statusArr, status)
	}

	ip := scanAsset["ip"].(string)
	var scanStatus int
	switch v := scanAsset["status"].(type) {
	case int:
		scanStatus = v
	case float64:
		scanStatus = int(v)
	case float32:
		scanStatus = int(v)
	default:
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]无法转换scanAsset status类型: %T, value: %v", scanAsset["status"], scanAsset["status"])
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]打印当前ip端口维度的status数组状态值: task_id=%d, ip=%s, user_id=%d, ip_port_statusArr=%v, ip_status=%d",
		taskId, ip, s.Task.UserId, statusArr, scanStatus)

	// 对应PHP: if(count($statusArr) > 1){ ... } elseif (count($statusArr) == 1){ ... }
	if len(statusArr) > 1 {
		// IP端口维度存在2种不同情况状态数据，统一更新
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ip维度和ip端口维度status状态不一致，更新啊11111: task_id=%d, ip=%s, user_id=%d, ip_status=%d, ip_port_status=%d",
			taskId, ip, s.Task.UserId, scanStatus, statusArr[0])

		err := elastic.UpdateByParams[foradar_assets.ForadarAsset]([][]interface{}{
			{"user_id", "=", s.Task.UserId},
			{"ip", "=", ip},
		}, map[string]interface{}{
			"status": scanStatus,
		})
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新ForadarAssets状态失败: %v", err)
		}
	} else if len(statusArr) == 1 {
		// IP端口维度存在1种情况状态数据，判断是否与IP维度的status一致
		if scanStatus != statusArr[0] {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]ip维度和ip端口维度status状态不一致，更新啊222222: task_id=%d, ip=%s, user_id=%d, ip_status=%d, ip_port_status=%d",
				taskId, ip, s.Task.UserId, scanStatus, statusArr[0])
			err := elastic.UpdateByParams[foradar_assets.ForadarAsset]([][]interface{}{
				{"user_id", "=", s.Task.UserId},
				{"ip", "=", ip},
			}, map[string]interface{}{
				"status": scanStatus,
			})

			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新ForadarAssets状态失败: %v", err)
			}
		}
	}
}

// setCdnCache 设置CDN缓存 - 统一处理CDN缓存设置
func (s *ScanForadarAsset) setCdnCache(ctx context.Context, scanAsset IpAssetData) {
	isCdn := getBoolValue(scanAsset["is_cdn"])
	ip := safeStringValue(scanAsset["ip"])
	if ip != "" {
		cacheKey := "cdn-ip:" + ip
		var cacheData bool
		if !redis.GetCache(cacheKey, &cacheData) {
			// 缓存不存在，直接设置
			redis.SetCache(cacheKey, 30*24*time.Hour, isCdn) // 30天缓存
			log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] 设置CDN缓存 - IP: %s, 缓存Key: %s, 缓存值: %v", ip, cacheKey, isCdn)
		} else {
			// 缓存已存在，检查是否需要更新
			if !cacheData && isCdn {
				// 原来是false，现在要设置为true，需要更新
				redis.SetCache(cacheKey, 30*24*time.Hour, isCdn)
				log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] 更新CDN缓存 - IP: %s, 缓存Key: %s, 原值: %v, 新值: %v", ip, cacheKey, cacheData, isCdn)
			} else {
				// 其他情况保持原值不变
				log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] CDN缓存已存在且无需更新 - IP: %s, 缓存Key: %s, 现有值: %v, 尝试设置值: %v", ip, cacheKey, cacheData, isCdn)
			}
		}
	}
}

// detectCDN CDN检测 - 对应PHP CDN检测逻辑
func (s *ScanForadarAsset) detectCDN(ctx context.Context, scanAsset IpAssetData) {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 对应PHP: if(!($scanAsset['is_ipv6'] ?? false)){ $scanAsset['is_cdn'] = isCdn($scanAsset['ip']); }
	isIpv6, _ := scanAsset["is_ipv6"].(bool)
	if !isIpv6 {
		ip := scanAsset["ip"].(string)
		log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] detectCDN - 开始IP CDN检测 - IP: %s", ip)
		isCdnResult := network.IsCdn(ip, 0) // 0表示IP检测
		scanAsset["is_cdn"] = isCdnResult
		log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] detectCDN - IP CDN检测完成 - IP: %s, 结果: %v", ip, isCdnResult)
		// 如果检测为CDN，立即设置缓存
		if isCdnResult {
			s.setCdnCache(ctx, scanAsset)
		}
	}

	// 校验域名是否是CDN - 对应PHP: if(empty($scanAsset['is_cdn'])){ $scanAsset['is_cdn'] = isCdn(($scanAsset['cname'] ?? ''),1); }
	isCdn, _ := scanAsset["is_cdn"].(bool)
	if !isCdn {
		if cname, ok := scanAsset["cname"].(string); ok && cname != "" {
			log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] detectCDN - 开始CNAME CDN检测 - CNAME: %s", cname)
			isCdnResult := network.IsCdn(cname, 1) // 1表示域名检测
			scanAsset["is_cdn"] = isCdnResult
			log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] detectCDN - CNAME CDN检测完成 - CNAME: %s, 结果: %v", cname, isCdnResult)
			// 如果检测为CDN，立即设置缓存
			if isCdnResult {
				s.setCdnCache(ctx, scanAsset)
			}
		}
	}

	// 根据组件名称校验是否是CDN - 对应PHP: if(empty($scanAsset['is_cdn'])){ if(($scanAsset['rule_tags'] ?? [])){ $scanAsset['is_cdn'] = ruleIsCdn($scanAsset['rule_tags']); } }
	isCdn, _ = scanAsset["is_cdn"].(bool)
	if !isCdn {
		if ruleTags, ok := scanAsset["rule_tags"].([]interface{}); ok && len(ruleTags) > 0 {
			log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] detectCDN - 开始规则标签CDN检测 - 规则数量: %d", len(ruleTags))
			isCdnResult := network.RuleIsCdn(ruleTags)
			scanAsset["is_cdn"] = isCdnResult
			log.WithContextInfof(ctx, "[CDN_DETECT_DEBUG] detectCDN - 规则标签CDN检测完成 - 规则数量: %d, 结果: %v", len(ruleTags), isCdnResult)
			// 如果检测为CDN，立即设置缓存
			if isCdnResult {
				s.setCdnCache(ctx, scanAsset)
			}
		}
	}

	// 根据证书检测CDN - 对应PHP证书CDN检测逻辑
	isCdn, _ = scanAsset["is_cdn"].(bool)
	if !isCdn {
		if portList, ok := scanAsset["port_list"].([]interface{}); ok && len(portList) > 0 {
			isCdnResult := s.detectCdnByCert(portList)
			scanAsset["is_cdn"] = isCdnResult
			log.WithContextInfof(ctx, "[DEBUG] 证书CDN检测 - 端口数量: %d, 结果: %v", len(portList), isCdnResult)
			// 如果检测为CDN，立即设置缓存
			if isCdnResult {
				s.setCdnCache(ctx, scanAsset)
			}
		}
	}
}

// aggregateDomainsAndTitles 聚合域名和标题 - 对应PHP聚合逻辑
func (s *ScanForadarAsset) aggregateDomainsAndTitles(ctx context.Context, scanAsset IpAssetData) {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 对应PHP: $allDomain = collect($scanAsset['host_list'] ?? [])->pluck('domain')->filter()->unique()->values()->toArray();
	// 安全地处理host_list字段，支持多种数据类型
	domainMap := make(map[string]bool)
	titleMap := make(map[string]bool)

	if hostListValue, exists := scanAsset["host_list"]; exists && hostListValue != nil {
		log.WithContextInfof(ctx, "[DEBUG] host_list字段类型: %T, 值: %v", hostListValue, hostListValue)

		// 尝试多种类型转换
		switch hostList := hostListValue.(type) {
		case []interface{}:
			log.WithContextInfof(ctx, "[DEBUG] host_list解析为[]interface{}，长度: %d", len(hostList))
			for i, hostItem := range hostList {
				if hostMap, ok := hostItem.(map[string]interface{}); ok {
					// 收集域名
					if domain, ok := hostMap["domain"].(string); ok && domain != "" {
						domainMap[domain] = true
					}
					// 收集标题
					if title, ok := hostMap["title"].(string); ok && title != "" {
						titleMap[title] = true
					}
				} else {
					log.WithContextWarnf(ctx, "[DEBUG] host_list[%d]类型转换失败，类型: %T", i, hostItem)
				}
			}
		case []map[string]interface{}:
			log.WithContextInfof(ctx, "[DEBUG] host_list解析为[]map[string]interface{}，长度: %d", len(hostList))
			for _, hostMap := range hostList {
				// 收集域名
				if domain, ok := hostMap["domain"].(string); ok && domain != "" {
					domainMap[domain] = true
				}
				// 收集标题
				if title, ok := hostMap["title"].(string); ok && title != "" {
					titleMap[title] = true
				}
			}
		default:
			log.WithContextWarnf(ctx, "[DEBUG] host_list字段类型不支持: %T，尝试JSON解析", hostListValue)
			// 尝试JSON解析
			if jsonBytes, err := json.Marshal(hostListValue); err == nil {
				var hostList []map[string]interface{}
				if err := json.Unmarshal(jsonBytes, &hostList); err == nil {
					log.WithContextInfof(ctx, "[DEBUG] host_list通过JSON解析成功，长度: %d", len(hostList))
					for _, hostMap := range hostList {
						// 收集域名
						if domain, ok := hostMap["domain"].(string); ok && domain != "" {
							domainMap[domain] = true
						}
						// 收集标题
						if title, ok := hostMap["title"].(string); ok && title != "" {
							titleMap[title] = true
						}
					}
				} else {
					log.WithContextErrorf(ctx, "[DEBUG] host_list JSON解析失败: %v", err)
				}
			} else {
				log.WithContextErrorf(ctx, "[DEBUG] host_list JSON序列化失败: %v", err)
			}
		}
	} else {
		log.WithContextInfof(ctx, "[DEBUG] host_list字段不存在或为nil")
	}

	// 设置all_domain
	if len(domainMap) > 0 {
		var allDomain []string
		for domain := range domainMap {
			allDomain = append(allDomain, domain)
		}
		scanAsset["all_domain"] = allDomain
	}

	// 设置all_title
	if len(titleMap) > 0 {
		var allTitle []string
		for title := range titleMap {
			allTitle = append(allTitle, title)
		}
		scanAsset["all_title"] = allTitle
	}
}

// insertOrUpdateIpAsset 插入或更新IpAssets - 对应PHP插入更新逻辑
func (s *ScanForadarAsset) insertOrUpdateIpAsset(ctx context.Context, id string, scanAsset IpAssetData, oldAsset *fofaee_assets.FofaeeAssets, tAsset *fofaee_task_assets.FofaeeTaskAssets) error {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 对应PHP: if (!$asset) { IpAssets::query()->insert($scanAsset); } else { ... }
	if oldAsset == nil {
		// 不存在则写入 - 使用InsertOrUpdate方法
		// 将scanAsset转换为fofaee_assets.FofaeeAssets结构
		var asset fofaee_assets.FofaeeAssets
		assetBytes, _ := json.Marshal(scanAsset)
		json.Unmarshal(assetBytes, &asset)

		// [DEBUG] 打印插入时的is_cdn值
		log.WithContextInfof(ctx, "[DEBUG] 插入IpAssets - ID: %s, is_cdn: %v (scanAsset: %v, asset: %v)",
			id, scanAsset["is_cdn"], scanAsset["is_cdn"], asset.IsCdn)

		successCount, errCount, lastErrorInfo := elastic.InsertOrUpdateWithTime(fofaee_assets.FofaeeAssetsIndex, fofaee_assets.FofaeeAssetsType, []fofaee_assets.FofaeeAssets{asset}, 500)
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]插入新的IpAssets: %s, successCount: %d, errCount: %d, lastErrorInfo: %s", id, successCount, errCount, lastErrorInfo)
		if errCount > 0 {
			return fmt.Errorf("插入IpAssets失败, errorInfo: %s", lastErrorInfo)
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]插入新的IpAssets: %s", id)
	} else {
		// 处理推荐扫描的疑似状态逻辑 - 对应PHP推荐扫描逻辑
		if s.Task.AssetType == scan_task.TASK_ASSET_CLOUD {
			s.handleRecommendScanStatus(ctx, scanAsset, oldAsset)
		}

		// 更新现有记录
		// [DEBUG] 打印更新时的is_cdn值和数据大小
		log.WithContextInfof(ctx, "[DEBUG] 更新IpAssets - ID: %s, is_cdn: %v, 字段数量: %d", id, scanAsset["is_cdn"], len(scanAsset))

		// [DEBUG] 打印部分关键字段，检查是否有问题
		for key, value := range scanAsset {
			if key == "reason_arr" || key == "rule_tags" || key == "host_list" || key == "port_list" {
				log.WithContextInfof(ctx, "[DEBUG] 更新字段 %s 类型: %T, 长度: %v", key, value, getValueLength(value))
			}
		}

		// 在更新前打印详细的数据信息
		log.WithContextInfof(ctx, "[DEBUG] 准备更新ES - ID: %s", id)

		// 清理数据，移除可能导致更新失败的问题数据
		originalSize := len(scanAsset)
		scanAsset = cleanScanAssetData(scanAsset)
		cleanedSize := len(scanAsset)
		if originalSize != cleanedSize {
			log.WithContextInfof(ctx, "[DEBUG] 数据清理完成 - 原始字段数: %d, 清理后字段数: %d", originalSize, cleanedSize)
		}

		// 检查关键字段的数据格式
		if hostList, exists := scanAsset["host_list"]; exists {
			log.WithContextInfof(ctx, "[DEBUG] host_list字段详情 - 类型: %T, 长度: %d", hostList, getValueLength(hostList))
			if hostListSlice, ok := hostList.([]map[string]interface{}); ok && len(hostListSlice) > 0 {
				// 打印第一个host的详细信息
				firstHost := hostListSlice[0]
				log.WithContextInfof(ctx, "[DEBUG] 第一个host详情 - 字段数: %d", len(firstHost))
				for key, value := range firstHost {
					log.WithContextInfof(ctx, "[DEBUG] host字段 %s: %T = %v", key, value, value)
				}
			}
		}

		// 序列化检查
		scanAssetBytes, marshalErr := json.Marshal(scanAsset)
		if marshalErr != nil {
			log.WithContextErrorf(ctx, "[DEBUG] scanAsset JSON序列化失败: %v", marshalErr)
			return fmt.Errorf("scanAsset JSON序列化失败: %v", marshalErr)
		}
		log.WithContextInfof(ctx, "[DEBUG] scanAsset JSON序列化成功，大小: %d bytes", len(scanAssetBytes))

		// 尝试反序列化验证
		var testAsset fofaee_assets.FofaeeAssets
		if unmarshalErr := json.Unmarshal(scanAssetBytes, &testAsset); unmarshalErr != nil {
			log.WithContextErrorf(ctx, "[DEBUG] scanAsset JSON反序列化验证失败: %v", unmarshalErr)
			log.WithContextErrorf(ctx, "[DEBUG] 问题JSON片段: %s", string(scanAssetBytes[:min(500, len(scanAssetBytes))]))
			return fmt.Errorf("scanAsset JSON反序列化验证失败: %v", unmarshalErr)
		}
		log.WithContextInfof(ctx, "[DEBUG] scanAsset JSON反序列化验证成功")

		// 打印查询条件
		queryParams := [][]interface{}{
			{"_id", "=", id},
		}
		log.WithContextInfof(ctx, "[DEBUG] ES查询条件: %+v", queryParams)

		// 检查scanAsset中是否有问题字段
		problematicFields := []string{}
		for key, value := range scanAsset {
			// 检查是否有nil值
			if value == nil {
				problematicFields = append(problematicFields, fmt.Sprintf("%s=nil", key))
			}
			// 检查是否有空的复杂类型
			if reflect.ValueOf(value).Kind() == reflect.Slice {
				if reflect.ValueOf(value).Len() == 0 {
					problematicFields = append(problematicFields, fmt.Sprintf("%s=empty_slice", key))
				}
			}
		}
		if len(problematicFields) > 0 {
			log.WithContextWarnf(ctx, "[DEBUG] 发现可能有问题的字段: %v", problematicFields)
		}

		err := elastic.UpdateByParams[fofaee_assets.FofaeeAssets](
			queryParams,
			scanAsset,
		)
		if err != nil {
			log.WithContextErrorf(ctx, "[DEBUG] UpdateByParams 失败: %v", err)
			log.WithContextErrorf(ctx, "[DEBUG] 失败的scanAsset数据: %s", string(scanAssetBytes[:min(1000, len(scanAssetBytes))]))

			err = redis.GetClient().HSet(ctx, fmt.Sprintf("cache:ip_asset_update_error:%d", s.Task.ID), id, time.Now().Format(time.DateTime)).Err()
			if err != nil {
				log.WithContextErrorf(ctx, "[DEBUG] 设置ip_asset_update_error缓存失败: %v", err)
			}

			// 方案1: 尝试使用直接的ES客户端更新
			log.WithContextInfof(ctx, "[DEBUG] 尝试使用直接ES客户端更新方式")
			esClient := es.GetInstance()

			// 添加必要的字段
			scanAsset["updated_at"] = time.Now().Format("2006-01-02 15:04:05")

			updateResp, updateErr := esClient.Update().
				Index(fofaee_assets.FofaeeAssetsIndex).
				Type(fofaee_assets.FofaeeAssetsType).
				Id(id).
				Doc(scanAsset).
				Upsert(scanAsset). // 如果文档不存在则插入
				Refresh("true").
				Do(ctx)

			if updateErr != nil {
				log.WithContextErrorf(ctx, "[DEBUG] 直接ES更新也失败: %v", updateErr)

				// 方案2: 尝试使用 InsertOrUpdate 方式
				log.WithContextInfof(ctx, "[DEBUG] 尝试使用 InsertOrUpdate 方式")
				var asset fofaee_assets.FofaeeAssets
				assetBytes, marshalErr2 := json.Marshal(scanAsset)
				if marshalErr2 != nil {
					log.WithContextErrorf(ctx, "[DEBUG] InsertOrUpdate JSON序列化失败: %v", marshalErr2)
					return fmt.Errorf("InsertOrUpdate JSON序列化失败: %v", marshalErr2)
				}

				unmarshalErr2 := json.Unmarshal(assetBytes, &asset)
				if unmarshalErr2 != nil {
					log.WithContextErrorf(ctx, "[DEBUG] InsertOrUpdate JSON反序列化失败: %v", unmarshalErr2)
					return fmt.Errorf("InsertOrUpdate JSON反序列化失败: %v", unmarshalErr2)
				}

				successCount, errCount, lastErrorInfo := elastic.InsertOrUpdateWithTime(fofaee_assets.FofaeeAssetsIndex, fofaee_assets.FofaeeAssetsType, []fofaee_assets.FofaeeAssets{asset}, 500)
				log.WithContextInfof(ctx, "[DEBUG] InsertOrUpdate结果 - 成功: %d, 失败: %d, 错误信息: %s", successCount, errCount, lastErrorInfo)
				if errCount > 0 {
					return fmt.Errorf("所有更新方式都失败 - UpdateByParams: %v, 直接ES更新: %v, InsertOrUpdate: %s", err, updateErr, lastErrorInfo)
				}
				log.WithContextInfof(ctx, "[DEBUG] InsertOrUpdate 成功")
			} else {
				log.WithContextInfof(ctx, "[DEBUG] 直接ES更新成功 - 版本: %d, 结果: %s", updateResp.Version, updateResp.Result)
			}
		} else {
			log.WithContextInfof(ctx, "[DEBUG] UpdateByParams 成功更新 - ID: %s", id)
		}

		// 避免IP端口维度数据拆分的问题 - 对应PHP状态同步逻辑
		scanStatus := utils.GetIntValue(scanAsset["status"])
		oldStatus := utils.GetIntValue(oldAsset.Status)
		if oldStatus != scanStatus {
			err := elastic.UpdateByParams[foradar_assets.ForadarAsset]([][]interface{}{
				{"user_id", "=", s.Task.UserId},
				{"ip", "=", tAsset.Ip},
			}, map[string]interface{}{
				"status": scanAsset["status"],
			})
			if err != nil {
				log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]同步ForadarAssets状态失败: %v", err)
			}
		}
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]更新IpAssets: %s", id)
	}

	return nil
}

// dispatchRecordIpHistoryJob 分发记录IP历史任务 - 对应PHP: RecordIpHistory::dispatch(...)->onQueue('record_ip_history')
func (s *ScanForadarAsset) dispatchRecordIpHistoryJob(ctx context.Context, userId, companyId uint64, ip string, scanAsset IpAssetData) {
	// 对应PHP: RecordIpHistory::dispatch($this->task->user_id, $this->task->company_id ?? null, $tAsset->ip, $scanAsset)->onQueue('record_ip_history');
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 构造RecordIpHistoryJobPayload
	payload := RecordIpHistoryJobPayload{
		UserId:     int(userId),
		CompanyId:  int64(companyId),
		Ip:         ip,
		Data:       scanAsset,
		ThreatData: []map[string]interface{}{}, // 空的威胁数据
		DataType:   0,                          // 0表示资产数据
		IsCheck:    false,                      // false表示漏洞扫描
	}
	// 添加调试日志
	payloadStr := utils.AnyToStr(payload)
	log.WithContextInfof(ctx, "[RECORD_IP_HISTORY_DEBUG] 准备启动goroutine - payload: %s", payloadStr)

	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.WithContextErrorf(ctx, "[RECORD_IP_HISTORY_DEBUG] goroutine发生panic: %v", r)
			}
		}()

		log.WithContextInfof(ctx, "[RECORD_IP_HISTORY_DEBUG] goroutine开始执行")
		err := RecordIpHistoryJob(ctx, &asyncq.Task{
			Type:    asyncq.RecordIpHistoryJob,
			Payload: payloadStr,
		})
		if err != nil {
			log.WithContextErrorf(ctx, "[RECORD_IP_HISTORY_DEBUG] RecordIpHistoryJob执行失败: %v", err)
		} else {
			log.WithContextInfof(ctx, "[RECORD_IP_HISTORY_DEBUG] RecordIpHistoryJob执行成功")
		}
	}()

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]成功分发记录IP历史任务: userId=%d, companyId=%d, ip=%s", userId, companyId, ip)
}

// updateForadarAssetsStatus 更新ForadarAssets状态 - 对应PHP手动扫描状态更新逻辑
func (s *ScanForadarAsset) updateForadarAssetsStatus(ctx context.Context, tAsset *fofaee_task_assets.FofaeeTaskAssets) {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]修改foradar_assets状态: %s", tAsset.Ip)

	// 对应PHP: if (count($tAsset->ports ?? [])) { ForadarAssets::query()->where('ip', $tAsset->ip)->where('user_id', $this->task->user_id)->whereIn('port', $tAsset->ports)->update(['status' => ForadarAssets::STATUS_CLAIMED]); }
	if len(tAsset.Ports) > 0 {
		portInterfaces := make([]interface{}, len(tAsset.Ports))
		copy(portInterfaces, tAsset.Ports)

		err := elastic.UpdateByParams[foradar_assets.ForadarAsset]([][]interface{}{
			{"user_id", "=", s.Task.UserId},
			{"ip", "=", tAsset.Ip},
			{"port", "in", portInterfaces},
		}, map[string]interface{}{
			"status": fofaee_assets.STATUS_CLAIMED, // STATUS_CLAIMED
		})
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新ForadarAssets状态失败: %v", err)
		}
	}
}

// updateCompanyUsedIpAsset 更新公司使用的IP资产数量 - 对应PHP公司使用数量统计
func (s *ScanForadarAsset) updateCompanyUsedIpAsset(ctx context.Context) {
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 对应PHP: $used_num = IpAssets::query()->where('user_id', $this->task->user_id)->whereIn('status', [IpAssets::STATUS_CLAIMED, IpAssets::STATUS_UPLOAD])->count(); Company::query()->where('id', $this->task->company_id)->update(['used_ip_asset' => $used_num]);
	usedNum, err := elastic.CountByParams[fofaee_assets.FofaeeAssets]([][]interface{}{
		{"user_id", "=", s.Task.UserId},
		{"status", "in", []interface{}{fofaee_assets.STATUS_CLAIMED, fofaee_assets.STATUS_UPLOAD}},
	})
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]统计IP资产数量失败: %v", err)
		return
	}

	// 更新公司使用的IP资产数量
	qb := &mysql.QueryBuilder{}
	qb.Where = append(qb.Where, mysql.CompareCond{Field: "id", Operator: "=", Value: s.Task.CompanyId})
	_, err = mysql.NewDSL[company.Company]().Update(qb, map[string]interface{}{
		"used_ip_asset": usedNum,
	})
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]更新公司IP资产数量失败: %v", err)
		return
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]统计台账数量: companyId=%d, usedNum=%d", s.Task.CompanyId, usedNum)
}

// 辅助方法已移除，直接使用network包中的函数

func (s *ScanForadarAsset) detectCdnByCert(portList []interface{}) bool {
	// 对应PHP: if(empty($scanAsset['is_cdn']) && ($scanAsset['port_list'] ?? '')){ $allCert = collect($scanAsset['port_list'])->pluck('cert')->filter()->pluck('subject_key')->filter()->unique()->values()->toArray(); foreach ($allCert as $certinfo){ $cdnNum = count(explode('cdn',$certinfo)); if($cdnNum > 1){ $scanAsset['is_cdn'] = true; break; } } }

	// 收集所有证书的subject_key
	var allCert []string
	for _, portItem := range portList {
		if portMap, ok := portItem.(map[string]interface{}); ok {
			if cert, ok := portMap["cert"].(map[string]interface{}); ok {
				if subjectKey, ok := cert["subject_key"].(string); ok && subjectKey != "" {
					// 去重处理
					found := false
					for _, existing := range allCert {
						if existing == subjectKey {
							found = true
							break
						}
					}
					if !found {
						allCert = append(allCert, subjectKey)
					}
				}
			}
		}
	}

	// 检查每个证书信息是否包含cdn
	for _, certInfo := range allCert {
		cdnNum := len(strings.Split(strings.ToLower(certInfo), "cdn"))
		if cdnNum > 1 {
			return true
		}
	}

	return false
}

func (s *ScanForadarAsset) handleRecommendScanStatus(ctx context.Context, scanAsset IpAssetData, oldAsset *fofaee_assets.FofaeeAssets) {
	// 对应PHP: if ($this->task->asset_type == \App\Models\MySql\Task::TASK_ASSET_CLOUD) { if($asset['is_user_sign_unsure'] == 1 && ($asset['status'] == ForadarAssets::STATUS_DEFAULT) && ($scanAsset['status'] != ForadarAssets::STATUS_DEFAULT)){ LogService::info('scanAsset-ip维度的','更新该资产状态为疑似状态，因为这个资产之前人工标记为疑似',['assets_id'=>$id]); $scanAsset['status'] = ForadarAssets::STATUS_DEFAULT; } }
	defer func() {
		if r := recover(); r != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]发生panic: %v", r)
		}
	}()

	// 如果是推荐扫描，然后当前数据是之前用户标记过为疑似资产，那么还要设置为疑似状态
	if s.Task.AssetType == scan_task.TASK_ASSET_CLOUD {
		// 直接使用FofaeeAssets结构体中的IsUserSignUnsure字段
		oldStatus := utils.GetIntValue(oldAsset.Status)
		scanStatus := utils.GetIntValue(scanAsset["status"])

		if oldAsset.IsUserSignUnsure == 1 && oldStatus == fofaee_assets.STATUS_DEFAULT && scanStatus != fofaee_assets.STATUS_DEFAULT {
			id := scanAsset["_id"].(string)
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]更新该资产状态为疑似状态，因为这个资产之前人工标记为疑似: assets_id=%s", id)
			scanAsset["status"] = fofaee_assets.STATUS_DEFAULT
		}
	}
}

// getRecommendResultInfo 获取推荐数据
// 对应PHP中的getRecommendResultInfo方法
// 参数：$subAsset, $url
// 返回：推荐结果信息，如果没有找到则返回nil
func (s *ScanForadarAsset) getRecommendResultInfo(subAsset map[string]interface{}, url string) *recommend_result.RecommendResult {
	log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]我来获取推荐数据啊-进来第一步啊", "getRecommendResultInfo", map[string]interface{}{
		"subassets_id":      subAsset["_id"],
		"url":               url,
		"isRecommendResult": subAsset["is_recommend"],
	})

	// 本地扫描不取推荐理由-2023-06-19添加，是为了解决有时候本地扫描取错推荐理由（同一个ip服务器上部署多个域名的时候）
	if s.Task.AssetType == scan_task.TASK_ASSET_MANUAL {
		return nil
	}

	// 如果isRecommendResult是true的话，就代表已经有推荐理由了，所以跳出去
	if isRecommendResult, exists := subAsset["is_recommend"]; exists {
		if isRecommendBool, ok := isRecommendResult.(bool); ok && isRecommendBool {
			log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getRecommendResultInfo", "isRecommendResult是true，因为是true的话，就代表已经有推荐理由了，所以跳出去", map[string]interface{}{
				"$url": url,
			})
			// 返回subAsset作为推荐结果，需要转换为RecommendResult
			return s.convertSubAssetToRecommendResult(subAsset)
		}
	}

	// 获取flag
	flag := s.Task.Flag
	if s.DetectTask != nil && flag == "" {
		flag = s.DetectTask.ExpendFlags
	}

	log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getRecommendResultInfo", fmt.Sprintf("Flag:%s, IP:%s, Port:%v", flag, subAsset["ip"], subAsset["port"]))

	var recommendInfo *recommend_result.RecommendResult

	// 使用pkg/utils中的GetSubdomain函数，对应PHP的getSubdomain($url, false)
	realSubdomain := utils.GetSubdomain(url)
	if realSubdomain != "" {
		// 查询推荐结果
		condition := recommend_result.NewFindCondition()
		condition.UserId = int(s.Task.UserId)
		// 对应PHP: ->whereNull('fake_task_id')
		condition.FakeTaskId = 0

		// 对应PHP: ->where('ip', $subAsset->ip)->where('port', $subAsset->port)
		if ip, exists := subAsset["ip"]; exists {
			condition.Ip = utils.SafeString(ip)
		}
		if port, exists := subAsset["port"]; exists {
			condition.Ports = []int{utils.SafeInt(port)}
		}

		// 对应PHP: ->when($this->detectTask, function ($q) { return $q->where('task_id', $this->task->id); })
		if s.DetectTask != nil {
			condition.TaskId = int(s.Task.ID)
		}

		// 对应PHP: ->where('protocol', $subAsset->protocol)
		if protocol, exists := subAsset["protocol"]; exists {
			condition.Protocol = []string{utils.SafeString(protocol)}
		}

		// 对应PHP: ->when($flag, function ($query) use ($flag) { return $query->where('flag', $flag); })
		if flag != "" {
			condition.Flag = flag
		}

		// 对应PHP: ->where('subdomain', $realSubdomain)
		condition.Subdomains = []string{realSubdomain}

		// 查询结果，对应PHP: ->orderByDesc('source_updated_at')->first()
		query := es2.NewBoolQuery()
		recommend_result.BuildQueryByCondition(query, condition)
		if condition.Keyword != "" {
			recommend_result.FakeAssetKeywordQuery(query, condition.Keyword)
		}
		results, err := elastic.All[recommend_result.RecommendResult](100, query, []es2.Sorter{es2.NewFieldSort("source_updated_at").Desc()})
		if err != nil {
			log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]getRecommendResultInfo", "查询推荐结果失败: %v", err)
		}
		if err == nil && len(results) > 0 {
			// 按source_updated_at降序排序，取第一个
			recommendInfo = s.findLatestBySourceUpdatedAt(results)
		}

		log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]url里面取到子域名啊，不用下边语法查询", "getRecommendResultInfo", map[string]interface{}{
			"subassets_id":  subAsset["_id"],
			"url":           url,
			"realsubdomain": realSubdomain,
		})
	} else {
		log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]url里面取不到子域名啊，用下边语法查询", "getRecommendResultInfo", map[string]interface{}{
			"subassets_id": subAsset["_id"],
			"url":          url,
		})
	}

	if recommendInfo == nil {
		log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getRecommendResultInfo", "没在微内核扫出来的资产里面查找到推荐理由啊，我第一次没取到推荐理由", map[string]interface{}{
			"$subAsset-id": subAsset["_id"],
			"$url":         url,
			"$flag":        flag,
			"task_id":      s.Task.ID,
		})

		// 处理recommand_result里面数据subdomain有值，但是fofee_subdomain里面的subdomain字段没值，需要去掉subdomain限制条件再查询一下数据
		// 如果subassets的协议是unknown的话，那么就去掉协议匹配数据
		condition := recommend_result.NewFindCondition()
		condition.UserId = int(s.Task.UserId)
		condition.FakeTaskId = 0

		if ip, exists := subAsset["ip"]; exists {
			condition.Ip = utils.SafeString(ip)
		}
		if port, exists := subAsset["port"]; exists {
			condition.Ports = []int{utils.SafeInt(port)}
		}

		if s.DetectTask != nil {
			condition.TaskId = int(s.Task.ID)
		}

		// 对应PHP: if($subAsset->protocol == 'unknown'){ } else { }
		if protocol, exists := subAsset["protocol"]; exists && utils.SafeString(protocol) != "unknown" {
			condition.Protocol = []string{utils.SafeString(protocol)}
		}

		if flag != "" {
			condition.Flag = flag
		}

		recommendList, err := recommend_result.NewRecommendResultModel().FindByCondition(condition)
		if err == nil && len(recommendList) > 0 {
			// 按source_updated_at降序排序
			recommendList = s.sortBySourceUpdatedAtDesc(recommendList)

			// 取有域名的数据，对应PHP: $recommendInfo = $recommendList->filter(function ($s) { return $s->subdomain ?? null; })->first();
			for _, item := range recommendList {
				if item.Subdomain != "" {
					recommendInfo = item
					break
				}
			}

			// 未取到时,按域名排序取第一个，对应PHP: $sortRows = $recommendList->sortBy(function ($item) { return strlen($item['subdomain'] ?? ''); }, SORT_ASC);
			if recommendInfo == nil {
				recommendInfo = s.findShortestSubdomain(recommendList)
			}
		}

		log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getRecommendResultInfo", "第二次的推荐结果", map[string]interface{}{
			"$subAsset-id":   subAsset["_id"],
			"$url":           url,
			"$flag":          flag,
			"task_id":        s.Task.ID,
			"$recommendInfo": recommendInfo,
		})
	}

	if recommendInfo == nil {
		log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getRecommendResultInfo", "最后输出的推荐结果-是我没找到推荐理由，没有线索链", map[string]interface{}{
			"$subAsset-id": subAsset["_id"],
			"$url":         url,
			"$flag":        flag,
			"task_id":      s.Task.ID,
			"protocol":     subAsset["protocol"],
			"port":         subAsset["port"],
		})
	} else {
		log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]getRecommendResultInfo", "最后输出的推荐结果-是我找到了推荐理由，有线索链", map[string]interface{}{
			"$subAsset-id":   subAsset["_id"],
			"$url":           url,
			"$flag":          flag,
			"task_id":        s.Task.ID,
			"$recommendInfo": recommendInfo,
		})
	}

	return recommendInfo
}

// convertSubAssetToRecommendResult 将subAsset转换为RecommendResult
func (s *ScanForadarAsset) convertSubAssetToRecommendResult(subAsset map[string]interface{}) *recommend_result.RecommendResult {
	result := &recommend_result.RecommendResult{}

	if id, exists := subAsset["_id"]; exists {
		result.Id = utils.SafeString(id)
	}
	if ip, exists := subAsset["ip"]; exists {
		result.Ip = utils.SafeString(ip)
	}
	if port, exists := subAsset["port"]; exists {
		result.Port = utils.SafeInt(port)
	}
	if protocol, exists := subAsset["protocol"]; exists {
		result.Protocol = utils.SafeString(protocol)
	}
	if title, exists := subAsset["title"]; exists {
		result.Title = utils.SafeString(title)
	}
	if domain, exists := subAsset["domain"]; exists {
		result.Domain = utils.SafeString(domain)
	}
	if subdomain, exists := subAsset["subdomain"]; exists {
		result.Subdomain = utils.SafeString(subdomain)
	}
	// 帮我把我新加的几个字段返回回去 assets_source oneforall_source source_updated_at
	if assetsSource, exists := subAsset["assets_source"]; exists && assetsSource != nil {
		// 添加调试日志
		log.WithContextInfof(context.Background(), "[DEBUG] convertSubAssetToRecommendResult assets_source原始值: %v, 类型: %T", assetsSource, assetsSource)

		// 尝试多种方式转换
		switch v := assetsSource.(type) {
		case int:
			result.AssetsSource = v
		case int64:
			result.AssetsSource = int(v)
		case *int64:
			if v != nil {
				result.AssetsSource = int(*v)
			}
		case float64:
			result.AssetsSource = int(v)
		case string:
			if parsed := utils.SafeInt(v); parsed != 0 {
				result.AssetsSource = parsed
			}
		default:
			result.AssetsSource = utils.SafeInt(assetsSource)
		}

		log.WithContextInfof(context.Background(), "[DEBUG] convertSubAssetToRecommendResult 最终AssetsSource值: %d", result.AssetsSource)
	}
	if oneforallSource, exists := subAsset["oneforall_source"]; exists {
		result.OneforallSource = utils.SafeString(oneforallSource)
	}
	if sourceUpdatedAt, exists := subAsset["source_updated_at"]; exists {
		result.SourceUpdatedAt = utils.SafeString(sourceUpdatedAt)
	}
	if isCdn, exists := subAsset["is_cdn"]; exists {
		if cdnBool, ok := isCdn.(bool); ok {
			result.IsCDN = cdnBool
		} else {
			result.IsCDN = utils.SafeInt(isCdn) != 0
		}
	}
	cloudName, _ := subAsset["cloud_name"].(string)
	result.CloudName = cloudName

	isIpv6, _ := subAsset["is_ipv6"].(bool)
	result.IsIPv6 = isIpv6

	// 处理推荐理由字段
	if reason, exists := subAsset["reason"]; exists {
		if reasonSlice, ok := reason.([]RecommendReason); ok {
			// 转换为 recommend_result.RecommendReason 类型
			result.Reason = make([]recommend_result.RecommendReason, 0, len(reasonSlice))
			for _, r := range reasonSlice {
				result.Reason = append(result.Reason, recommend_result.RecommendReason{
					Id:              r.Id,
					Type:            r.Type,
					Content:         r.Content,
					GroupId:         r.GroupId,
					ClueCompanyName: r.ClueCompanyName,
					Source:          r.Source,
				})
			}
		}
	}

	return result
}

// findLatestBySourceUpdatedAt 找到source_updated_at最新的记录
func (s *ScanForadarAsset) findLatestBySourceUpdatedAt(results []*recommend_result.RecommendResult) *recommend_result.RecommendResult {
	if len(results) == 0 {
		return nil
	}

	latest := results[0]
	for _, result := range results[1:] {
		if result.SourceUpdatedAt > latest.SourceUpdatedAt {
			latest = result
		}
	}
	return latest
}

// sortBySourceUpdatedAtDesc 按source_updated_at降序排序
func (s *ScanForadarAsset) sortBySourceUpdatedAtDesc(results []*recommend_result.RecommendResult) []*recommend_result.RecommendResult {
	// 创建副本避免修改原数组
	sorted := make([]*recommend_result.RecommendResult, len(results))
	copy(sorted, results)

	// 简单的冒泡排序，按source_updated_at降序
	for i := 0; i < len(sorted)-1; i++ {
		for j := 0; j < len(sorted)-1-i; j++ {
			if sorted[j].SourceUpdatedAt < sorted[j+1].SourceUpdatedAt {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}
	return sorted
}

// findShortestSubdomain 找到subdomain最短的记录
func (s *ScanForadarAsset) findShortestSubdomain(results []*recommend_result.RecommendResult) *recommend_result.RecommendResult {
	if len(results) == 0 {
		return nil
	}

	shortest := results[0]
	shortestLen := len(shortest.Subdomain)

	for _, result := range results[1:] {
		if len(result.Subdomain) < shortestLen {
			shortest = result
			shortestLen = len(result.Subdomain)
		}
	}
	return shortest
}

// getCname 获取CNAME
// 对应PHP中的getCname方法
// 参数：url 可选的URL字符串
// 返回：CNAME记录数组
func (s *ScanForadarAsset) getCname(url *string) []string {
	cname := []string{}
	if url == nil || *url == "" {
		return cname
	}

	// 对应PHP: if ($urlDomain = parse_url($url)['host'] ?? null)
	urlDomain := ""
	if u, err := netUrl.Parse(*url); err == nil && u.Host != "" {
		urlDomain = u.Host
	}

	if urlDomain != "" {
		// 对应PHP: try { ... } catch (\Throwable $e) { ... }
		defer func() {
			if r := recover(); r != nil {
				log.WithContextWarnf(context.Background(), "[ScanForadarAssetHandler]GetCname:"+urlDomain, safeStringValue(r))
			}
		}()

		// 对应PHP: if (!filter_var($urlDomain, FILTER_VALIDATE_IP))
		if !utils.IsIP(urlDomain) {
			// 对应PHP: collect(dns_get_record($urlDomain, DNS_CNAME))->pluck('target')->toArray()
			cnameRecords := dns.GetCNAMERecords(urlDomain)
			cname = append(cname, cnameRecords...)

			// 对应PHP: LogService::info('GetCname:'.$urlDomain, implode(',', $cname));
			log.WithContextInfof(context.Background(), "[ScanForadarAssetHandler]GetCname:"+urlDomain, strings.Join(cname, ","))
		}
	}
	return cname
}

// finalizeTask 对应PHP中的__destruct方法
// 在任务处理完成后执行清理和状态更新逻辑
func (s *ScanForadarAsset) finalizeTask(ctx context.Context) {
	// 对应PHP: if (!$st = Task::query()->where('id', $this->st->id)->first()) { return; }
	st, err := mysql.NewDSL[scan_task.ScanTasks]().FindByID(uint64(s.Task.ID))
	if err != nil {
		return
	}

	// 对应PHP: if ($this->isHandle && $st->step != Task::STEP_SCANNING && $st->status == Task::STATUS_RUNNING) {
	if s.isHandle && st.Step != scan_task.StepScanning && st.Status == scan_task.StatusDoing {
		// 对应PHP: Task::query()->where('id', $this->st->id)->update(['status'=>Task::STATUS_FAILED]);
		_, err := mysql.NewDSL[scan_task.ScanTasks]().UpdateByID(
			uint64(s.Task.ID),
			map[string]interface{}{
				"status": scan_task.StatusFailed,
			},
		)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]finalizeTask更新任务状态为失败失败，任务ID: %d，错误: %v", s.Task.ID, err)
		}

		// 对应PHP: LogService::info('微内核-资产扫描', 'Job结束,修改任务状态-任务标记为失败', $st->toArray());
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-资产扫描", "Job结束,修改任务状态-任务标记为失败", map[string]interface{}{
			"st": st,
		})

		// 对应PHP: $this->clearFailedAssets();
		s.clearFailedAssets(ctx)
	}

	// 对应PHP: if ($this->isHandle && $st->progress == 100 && $st->status != Task::STEP_FINISHED) {
	if s.isHandle && st.Progress == 100 && st.Status != scan_task.StatusFinished {
		// 对应PHP: Task::query()->where('id', $this->st->id)->update(['status'=>Task::STATUS_FINISHED,'step' => Task::STEP_FINISHED]);
		_, err := mysql.NewDSL[scan_task.ScanTasks]().UpdateByID(
			uint64(s.Task.ID),
			map[string]interface{}{
				"status": scan_task.StatusFinished,
				"step":   scan_task.StepFinished,
			},
		)
		if err != nil {
			log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]finalizeTask更新任务状态为完成失败，任务ID: %d，错误: %v", s.Task.ID, err)
		}

		// 对应PHP: LogService::info('微内核-资产扫描', 'Job结束,修改任务状态', $st->toArray());
		log.WithContextInfof(ctx, "[ScanForadarAssetHandler]微内核-资产扫描", "Job结束,修改任务状态", map[string]interface{}{
			"st": st,
		})

		// 计算疑似资产新增的数量 - 对应PHP: $originUnserIps = IpAssets::query()...
		originUnserIps, err := elastic.AllByParams[fofaee_assets.FofaeeAssets](
			1000,
			[][]interface{}{
				{"user_id", "=", s.Task.UserId},
				{"task_id", "!=", s.Task.ID},
				{"status", "=", fofaee_assets.STATUS_DEFAULT},
			},
			[]es2.Sorter{es2.NewFieldSort("id").Desc(), es2.NewFieldSort("ip").Desc()},
			"ip",
		)
		if err != nil {
			originUnserIps = make([]*fofaee_assets.FofaeeAssets, 0)
		}

		nowUnsureIps, err := elastic.AllByParams[fofaee_assets.FofaeeAssets](
			1000,
			[][]interface{}{
				{"user_id", "=", s.Task.UserId},
				{"task_id", "=", s.Task.ID},
				{"status", "=", fofaee_assets.STATUS_DEFAULT},
			},
			[]es2.Sorter{es2.NewFieldSort("id").Desc(), es2.NewFieldSort("ip").Desc()},
			"ip",
		)
		if err != nil {
			nowUnsureIps = make([]*fofaee_assets.FofaeeAssets, 0)
		}

		// 计算差异 - 对应PHP: $addUnserIps = array_values(array_filter(array_diff($nowUnsureIps,$originUnserIps)));
		originIpMap := make(map[string]bool)
		for _, asset := range originUnserIps {
			originIpMap[asset.Ip] = true
		}

		addUnserIps := make([]string, 0)
		for _, asset := range nowUnsureIps {
			if !originIpMap[asset.Ip] {
				addUnserIps = append(addUnserIps, asset.Ip)
			}
		}
		// 记录风险事件 - 对应PHP: if($addUnserIps){ ... recordRiskEvent(...); }
		if len(addUnserIps) > 0 {
			riskIpNum := len(addUnserIps)
			contentData := map[string]interface{}{
				"content":    fmt.Sprintf("发现%d个疑似资产", riskIpNum),
				"realted_ip": addUnserIps,
			}
			contentBytes, _ := json.Marshal(contentData)
			// 记录风险事件record
			s.recordRiskEvent(risks.TYPE_ASSET_INFO, string(contentBytes), int64(s.Task.UserId), int64(s.Task.CompanyId))
		}
	}
}

// recordRiskEvent 记录风险事件
// 对应PHP中的recordRiskEvent函数
// 参数：riskType 风险类型, content 详细内容, userId 用户ID, companyId 企业ID
func (s *ScanForadarAsset) recordRiskEvent(riskType int, content string, userId int64, companyId int64) {
	// 对应PHP: $data['created_at'] = \Carbon\Carbon::now();
	// 对应PHP: $data['user_id']    = $user_id;
	// 对应PHP: $data['company_id'] = $company_id;
	// 对应PHP: $data['type']       = $type;
	// 对应PHP: $data['content']    = $content;
	data := risks.Risks{
		UserID:    userId,
		CompanyID: companyId,
		Type:      riskType,
		Content:   content,
	}
	// created_at字段通过dbx.ModelFull自动处理
	// 对应PHP: \App\Models\MySql\Risk::query()->insert($data);
	err := risks.NewModel().Create(&data)
	if err != nil {
		log.WithContextErrorf(context.Background(), "[ScanForadarAssetHandler]recordRiskEvent记录风险事件失败，错误: %v", err)
	}
}

// getValueLength 获取值的长度，用于调试
func getValueLength(value interface{}) interface{} {
	if value == nil {
		return "nil"
	}
	switch v := value.(type) {
	case []interface{}:
		return len(v)
	case []string:
		return len(v)
	case []int:
		return len(v)
	case string:
		return len(v)
	case map[string]interface{}:
		return len(v)
	default:
		return "unknown"
	}
}

// isLastScanTaskOfDetectTask 检查当前扫描任务是否是测绘任务的最后一个任务
// 判断逻辑：scan_tasks的detect_assets_tasks_id一样的数据，除了自己当前这个任务，其余的都是状态2（已完成）
func (s *ScanForadarAsset) isLastScanTaskOfDetectTask(ctx context.Context) bool {
	// 如果没有测绘任务，直接返回false
	if s.DetectTask == nil {
		return false
	}

	// 查询所有相同detect_assets_tasks_id的扫描任务
	allTasks, err := mysql.NewDSL[scan_task.ScanTasks]().FindAll(
		mysql.WithWhere("detect_assets_tasks_id = ?", s.DetectTask.ID),
		mysql.WithWhere("user_id = ?", s.Task.UserId),
	)
	if err != nil {
		log.WithContextErrorf(ctx, "[ScanForadarAssetHandler]isLastScanTaskOfDetectTask: 查询相关扫描任务失败: %v", err)
		return false
	}

	// 检查除了当前任务外的其他任务是否都已完成
	for _, task := range allTasks {
		// 跳过当前任务
		if task.ID == s.Task.ID {
			continue
		}

		// 如果有任何一个任务不是已完成状态，则当前任务不是最后一个
		if task.Status != scan_task.StatusFinished {
			log.WithContextInfof(ctx, "[ScanForadarAssetHandler]isLastScanTaskOfDetectTask: 发现未完成的任务，task_id=%d, status=%d", task.ID, task.Status)
			return false
		}
	}

	log.WithContextInfof(ctx, "[ScanForadarAssetHandler]isLastScanTaskOfDetectTask: 当前任务是测绘任务的最后一个任务，task_id=%d, detect_task_id=%d", s.Task.ID, s.DetectTask.ID)
	return true
}
