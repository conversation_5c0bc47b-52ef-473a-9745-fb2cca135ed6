package main

import (
	_ "micro-service/cliService/aliyun_avd"
	_ "micro-service/cliService/call"
	"micro-service/cliService/command"
	_ "micro-service/cliService/config"
	_ "micro-service/cliService/describe"
	_ "micro-service/cliService/icp"
	_ "micro-service/cliService/migrate"
	_ "micro-service/cliService/proxy"
	_ "micro-service/cliService/router"
	_ "micro-service/cliService/rule_engine"
	_ "micro-service/cliService/scope"
	_ "micro-service/cliService/services"
	"micro-service/pkg/cfg"
)

func main() {
	cfg.InitLoadCfg()

	if err := command.Run(); err != nil {
		panic(err)
	}
}
