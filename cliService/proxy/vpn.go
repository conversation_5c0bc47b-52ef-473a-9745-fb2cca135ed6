package proxy

import (
	"compress/gzip"
	"context"
	"crypto/tls"
	"fmt"
	"io"
	"micro-service/pkg/cfg"
	"net"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/urfave/cli/v2"
	"golang.org/x/net/proxy"
)

func Vpn(ctx *cli.Context) error {
	args := ctx.Args().Slice()
	argsUrl := ""
	if len(args) < 1 {
		argsUrl = "https://www.google.com"
	} else {
		argsUrl = args[0]
	}
	if !cfg.LoadSocks().Enable {
		_, _ = os.Stdout.WriteString("未开启VPN功能!\n")
		return nil
	}
	socksAddr := fmt.Sprintf("%s:%d", cfg.LoadSocks().LocalAddr, cfg.LoadSocks().LocalPort)
	dialSocksProxy, err := proxy.SOCKS5("tcp", socksAddr, nil, proxy.Direct)
	if err != nil {
		_, _ = os.Stdout.WriteString("解析代理地址失败!\n")
		panic(err)
	}
	request, _ := http.NewRequest("GET", argsUrl, strings.NewReader(""))
	request.Header.Add("Accept-Encoding", "gzip")
	client := &http.Client{
		Transport: &http.Transport{
			IdleConnTimeout: time.Duration(60) * time.Second,
			DialContext: func(ctx context.Context, network, address string) (net.Conn, error) {
				return dialSocksProxy.Dial(network, address)
			},
			ForceAttemptHTTP2:     true,
			MaxIdleConns:          100,
			TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
			TLSHandshakeTimeout:   100 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		},
		Timeout: time.Second * time.Duration(60), // 超时时间
	}
	resp, err := client.Do(request)
	if err != nil {
		panic(err)
	}
	defer resp.Body.Close()
	if strings.EqualFold(resp.Header.Get("Content-Encoding"), "gzip") {
		reader, readerErr := gzip.NewReader(resp.Body)
		if readerErr != nil {
			panic(readerErr)
		}
		data, ReadErr := io.ReadAll(reader)
		if ReadErr != nil {
			panic(ReadErr)
		}
		_, _ = os.Stdout.WriteString("代理请求成功,返回如下:\n")
		_, _ = os.Stdout.Write(data)
	} else {
		data, RErr := io.ReadAll(resp.Body)
		if RErr != nil {
			panic(RErr)
		}
		_, _ = os.Stdout.WriteString("代理请求成功,返回如下:\n")
		_, _ = os.Stdout.Write(data)
	}
	return nil
}
