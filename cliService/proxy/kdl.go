package proxy

import (
	"compress/gzip"
	"crypto/tls"
	"io"
	"micro-service/initialize/redis"
	"micro-service/pkg/cfg"
	"micro-service/pkg/kuaidaili"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/urfave/cli/v2"
)

func Kdl(ctx *cli.Context) error {
	args := ctx.Args().Slice()
	argsUrl := ""
	if len(args) < 1 {
		argsUrl = "https://httpbin.org/get"
	} else {
		argsUrl = args[0]
	}
	_ = redis.GetInstance(cfg.LoadRedis())
	if !cfg.LoadCommon().Proxy {
		_, _ = os.Stdout.WriteString("未开启代理功能!\n")
		return nil
	}
	if proxyUrl := kuaidaili.GetProxyIp(ctx.Context); proxyUrl != "" {
		urls, err := url.Parse("http://" + proxyUrl)
		if err != nil {
			_, _ = os.Stdout.WriteString("解析代理地址失败!\n")
			panic(err)
		}
		_, _ = os.Stdout.WriteString("代理地址:" + proxyUrl + "\n")
		request, _ := http.NewRequest("GET", argsUrl, strings.NewReader(""))
		request.Header.Add("Accept-Encoding", "gzip")
		request.Header.Add("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
		client := &http.Client{
			Transport: &http.Transport{
				IdleConnTimeout:       time.Duration(60) * time.Second,
				Proxy:                 http.ProxyURL(urls),
				ForceAttemptHTTP2:     true,
				MaxIdleConns:          100,
				TLSClientConfig:       &tls.Config{InsecureSkipVerify: true},
				TLSHandshakeTimeout:   100 * time.Second,
				ExpectContinueTimeout: 1 * time.Second,
			},
			Timeout: time.Second * time.Duration(60), // 超时时间
		}
		resp, err := client.Do(request)
		if err != nil {
			panic(err)
		}
		defer resp.Body.Close()
		if strings.EqualFold(resp.Header.Get("Content-Encoding"), "gzip") {
			reader, readerErr := gzip.NewReader(resp.Body)
			if readerErr != nil {
				panic(readerErr)
			}
			data, ReadErr := io.ReadAll(reader)
			if ReadErr != nil {
				panic(ReadErr)
			}
			_, _ = os.Stdout.WriteString("代理请求成功,返回如下:\n")
			_, _ = os.Stdout.Write(data)
		} else {
			data, RErr := io.ReadAll(resp.Body)
			if RErr != nil {
				panic(RErr)
			}
			_, _ = os.Stdout.WriteString("代理请求成功,返回如下:\n")
			_, _ = os.Stdout.Write(data)
		}
	} else {
		_, _ = os.Stdout.WriteString("未获取到代理IP,请检查代理是否配置正确\n")
	}
	return nil
}
