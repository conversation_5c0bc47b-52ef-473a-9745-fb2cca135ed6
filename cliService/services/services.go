package services

import (
	"micro-service/cliService/command"
	"os"
	"sort"

	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/urfave/cli/v2"
	"go-micro.dev/v4/registry"
)

func init() {
	command.GetFCLI().Register(&cli.Command{
		Name:   "services",
		Usage:  "查看已注册的服务列表",
		Action: List,
	})
}

// List fetches running services from the registry and lists them. Exits on
// error.
func List(ctx *cli.Context) error {
	r := *command.GetFCLI().Fcmd.Options().Registry
	srvs, err := r.ListServices()
	if err != nil {
		return err
	}
	sortByService(srvs)
	t := table.NewWriter()
	t.SetOutputMirror(os.Stdout)
	t.AppendHeader(table.Row{"服务编号", "服务名称", "服务版本", "节点信息", "MetaData"})
	index := 1
	srvNames := sortByService(srvs)
	for _, srv := range srvNames {
		if srvInfo, err := r.GetService(srv); err == nil {
			for _, info := range srvInfo {
				for _, node := range info.Nodes {
					t.AppendRow([]interface{}{index, info.Name, info.Version, node.Address, node.Metadata})
					t.AppendSeparator()
					index += 1
				}
			}
		} else {
			t.AppendRow([]interface{}{index, srv, "获取失败", "获取失败", "获取失败"})
			t.AppendSeparator()
			index += 1
		}
	}
	t.Render()
	return nil
}

func sortByService(service []*registry.Service) []string {
	s := make([]string, len(service))
	for ix, srv := range service {
		s[ix] = srv.Name
	}
	sort.Strings(s)
	return s
}
