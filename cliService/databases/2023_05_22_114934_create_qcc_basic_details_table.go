package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateQccBasicDetailsTable())
// }

type QccBasicDetailsTable struct{}

func CreateQccBasicDetailsTable() interfaces.Migration {
	return &QccBasicDetailsTable{}
}

func (t *QccBasicDetailsTable) Up() error {
	return mysql.Schema.Create("qcc_basic_details", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("key_no", 32).Nullable().Index().Comment("企查查内部KeyNo")
		table.String("name", 128).Nullable().Index().Comment("企业名称")
		table.String("no", 20).Nullable().Index().Comment("工商注册号")
		table.String("belong_org", 128).Nullable().Comment("登记机关")
		table.String("oper_id", 36).Nullable().Comment("法定代表人ID")
		table.String("oper_name", 50).Nullable().Comment("法定代表人名称")
		table.String("start_date", 20).Nullable().Comment("成立日期")
		table.String("end_date", 20).Nullable().Comment("吊销日期")
		table.String("status", 32).Nullable().Comment("登记状态")
		table.String("province", 10).Nullable().Comment("省份")
		table.String("updated_date", 20).Nullable().Comment("更新日期")
		table.String("credit_code", 20).Nullable().Index().Comment("统一社会信用代码")
		table.String("regist_capi", 32).Nullable().Comment("注册资本")
		table.String("econ_kind", 48).Nullable().Comment("企业类型")
		table.String("address", 128).Nullable().Comment("注册地址")
		table.String("scope", 1024).Nullable().Comment("经营范围")
		table.String("term_start", 20).Nullable().Comment("营业期限始")
		table.String("team_end", 20).Nullable().Comment("营业期限至")
		table.String("check_date", 20).Nullable().Comment("核准日期")
		table.String("org_no", 20).Nullable().Comment("组织机构代码")
		table.String("is_on_stock", 1).Nullable().Comment("是否上市（0-未上市，1-上市）")
		table.String("stock_number", 10).Nullable().Comment("股票代码（如A股和港股同时存在，优先显示A股代码）")
		table.String("stock_type", 10).Nullable().Comment("上市类型（A股、港股、美股、新三板、新四板）")
		table.Text("original_name").Nullable().Comment("曾用名")
		table.String("image_url", 128).Nullable().Comment("企业Logo地址")
		table.String("ent_type", 2).Nullable().Comment("企业性质，0-大陆企业，1-社会组织 ，3-中国香港公司，4-事业单位，5-中国台湾公司，6-基金会，7-医院，8-海外公司，9-律师事务所，10-学校 ，11-机关单位，-1-其他")
		table.String("rec_cap", 32).Nullable().Comment("实缴资本")
		table.Text("revoke_info").Nullable().Comment("注销吊销信息")
		table.Text("area").Nullable().Comment("行政区域")
		table.String("area_code", 6).Nullable().Comment("行政区划代码")
		table.Timestamps()
	})
}

func (t *QccBasicDetailsTable) Down() error {
	return mysql.Schema.DropIfExists("qcc_basic_details")
}
