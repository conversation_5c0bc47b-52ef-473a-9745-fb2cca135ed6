package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateCloudAssetsCluesTable())
// }

type CloudAssetsCluesTable struct{}

func CreateCloudAssetsCluesTable() interfaces.Migration {
	return &CloudAssetsCluesTable{}
}

func (t *CloudAssetsCluesTable) Up() error {
	return mysql.Schema.Create("cloud_assets_clues", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("clue_hash", 64).Index().Comment("线索Hash")
		table.String("content", 512).Comment("线索内容").Index()
		table.Boolean("type").Comment("线索类型")
		table.Integer("hash", 11).Comment("ICON HASH").Default(0)
		table.String("company_name", 512).Comment("企业名称").Nullable().Index()
		table.Decimal("process", 11, 2).Comment("任务进度")
		table.Timestamps()
	})
}

func (t *CloudAssetsCluesTable) Down() error {
	return mysql.Schema.DropIfExists("cloud_assets_clues")
}
