package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterIntelligenceEventAddSummaryTable struct{}

func CreateAlterIntelligenceEventAddSummaryTable() interfaces.Migration {
	return &AlterIntelligenceEventAddSummaryTable{}
}

func (t *AlterIntelligenceEventAddSummaryTable) Up() error {
	return mysql.Schema.Table("intelligence_event", func(table interfaces.Blueprint) {
		// DisclosureTime *time.Time `json:"disclosure_time"` // 披露时间,数据来自汇总文件
		// Summary        string     `json:"summary"`         // 概述
		// FofaQuery      string     `json:"fofa_query"`      // FOFA语句
		table.DateTime("disclosure_time").Nullable().Comment("披露时间")
		table.String("summary", 2000).Nullable().Comment("事件概述")
		table.String("fofa_query", 2000).Nullable().Comment("FOFA语句")
	})
}

func (t *AlterIntelligenceEventAddSummaryTable) Down() error {
	return nil
}
