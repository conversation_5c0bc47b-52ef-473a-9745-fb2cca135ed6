package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/apps"
)

type AlterAppsHistory20230726 struct{}

func CreateAlterAppsHistory20230726() interfaces.Migration {
	return &AlterAppsHistory20230726{}
}

func (t *AlterAppsHistory20230726) Up() error {
	return mysql.Schema.Table(apps.HistoryTable, func(table interfaces.Blueprint) {
		table.Decimal("progress", 10, 3).Default("0.00").Comment("任务进度")
	})
}

func (t *AlterAppsHistory20230726) Down() error {
	return mysql.Schema.Table(apps.HistoryTable, func(table interfaces.Blueprint) {
		table.DropColumn("progress")
	})
}
