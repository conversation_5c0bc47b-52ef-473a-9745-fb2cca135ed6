package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AddTopDomainToBusinessSystemResultTable struct{}

func CreateAddTopDomainToBusinessSystemResultTable() interfaces.Migration {
	return &AddTopDomainToBusinessSystemResultTable{}
}

func (t *AddTopDomainToBusinessSystemResultTable) Up() error {
	return mysql.Schema.Table("business_system_result", func(table interfaces.Blueprint) {
		table.String("top_domain", 255).Comment("域名的主域名").Nullable()
	})
}

func (t *AddTopDomainToBusinessSystemResultTable) Down() error {
	return nil
}
