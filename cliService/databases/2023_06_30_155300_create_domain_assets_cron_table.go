package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	dac "micro-service/middleware/mysql/domain_assets_cron"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDomainAssetsCronTable())
// }

type DomainAssetsCron struct{}

func CreateDomainAssetsCronTable() interfaces.Migration {
	return &DomainAssetsCron{}
}

func (*DomainAssetsCron) Up() error {
	return mysql.Schema.Create(dac.DomainAssetsCronTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.DeletedAt(true)
		table.BigInt("user_id", 22).Comment("用户id")
		table.BigInt("operate_user_id", 22).Comment("操作人id")
		table.BigInt("cron_id", 22).Default(0).Comment("cron id")
		table.Integer("ex", 5).Default(0).Comment("执行周期, 单位-天")
		table.Integer("mode", 1).Default(dac.ModeImmediately).Comment("任务模式：1-立即执行 2-定时任务模式")
		table.Integer("current_used", 5).Default(0).Comment("立即执行模式，当天已使用次数")
		table.Integer("status", 1).Default(0).Comment("启用状态")
		table.String("cron_expression", 50).Default("''").Comment("cron表达式")
		table.DateTime("last_updated_at").Nullable().Comment("立即执行模式, 上次更新时间")
		// 表索引
		table.Index("user_id").IndexName("idx_user_id")
	})
}

func (*DomainAssetsCron) Down() error {
	return mysql.Schema.DropIfExists(dac.DomainAssetsCronTableName)
}
