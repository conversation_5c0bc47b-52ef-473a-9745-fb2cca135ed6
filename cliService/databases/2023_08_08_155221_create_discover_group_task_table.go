package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type DiscoverGroupTaskTable struct{}

func CreateDiscoverGroupTaskTable() interfaces.Migration {
	return &DiscoverGroupTaskTable{}
}

func (t *DiscoverGroupTaskTable) Up() error {
	return mysql.Schema.Create("discover_group_task", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Integer("type", 11).Comment("任务类型").Index()
		table.Decimal("percent", 11, 2).Comment("控股比例").Index()
		table.String("company_name", 512).Comment("企业名称").Index()
		table.Decimal("process", 11, 2).Comment("进度")
		table.String("reason", 512).Nullable().Comment("失败原因")
		table.Timestamps()
	})
}

func (t *DiscoverGroupTaskTable) Down() error {
	return mysql.Schema.DropIfExists("discover_group_task")
}
