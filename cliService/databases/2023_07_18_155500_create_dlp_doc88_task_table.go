package migrations

import (
	"micro-service/middleware/mysql/dataleak_doc88"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDlpDoc88TaskTable())
// }

type DlpDoc88Task struct{}

func CreateDlpDoc88TaskTable() interfaces.Migration {
	return &DlpDoc88Task{}
}

func (*DlpDoc88Task) Up() error {
	return mysql.Schema.Create(dataleak_doc88.TaskTable, func(table interfaces.Blueprint) {
		// 建表字段
		table.Id("id", 22)
		table.Timestamps()
		table.String("keyword", 500).Comment("关键词")
		table.String("keyword_hash", 100).Comment("关键词hash值")
		table.Decimal("progress", 7, 2).Default(0).Comment("进度")
		// 表索引
		table.Index("keyword_hash").IndexName("idx_keyword_hash")
	})
}

func (*DlpDoc88Task) Down() error {
	return mysql.Schema.DropIfExists(dataleak_doc88.TaskTable)
}
