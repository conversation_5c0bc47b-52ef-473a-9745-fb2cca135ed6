package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceOtherTable struct{}

func CreateIntelligenceOtherTable() interfaces.Migration {
	return &IntelligenceOtherTable{}
}

func (t *IntelligenceOtherTable) Up() error {
	return mysql.Schema.Create("intelligence_other", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("url", 512).Comment("风险URL")
		table.String("platform", 128).Nullable().Comment("平台")
		table.String("keyword", 255).Index().Nullable().Comment("关键词")
		table.String("poster", 512).Nullable().Comment("发帖人")
		table.String("title", 255).Index().Nullable().Comment("标题")
		table.String("sample", 512).Nullable().Comment("样本")
		table.String("screenshot", 2000).Nullable().Comment("截图")
		table.String("article_id", 512).Nullable().Comment("文章ID")
		table.DateTime("article_created_at").Nullable().Comment("收录时间")
		table.DateTime("found_at").Nullable().Comment("发现时间")
		table.Text("article_context").Nullable().Comment("文章内容啊")
		table.String("company", 255).Index().Nullable().Comment("关联企业")
		table.Integer("is_public", 11).Default(2).Comment("是否公开 1/公开 2/不公开")
		table.Timestamps()
		table.Comment("情报-其他情报")
	})
}

func (t *IntelligenceOtherTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_other")
}
