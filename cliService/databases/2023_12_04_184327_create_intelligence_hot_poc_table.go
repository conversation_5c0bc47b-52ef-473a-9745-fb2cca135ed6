package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceHotPocTable struct{}

func CreateIntelligenceHotPocTable() interfaces.Migration {
	return &IntelligenceHotPocTable{}
}

func (t *IntelligenceHotPocTable) Up() error {
	return mysql.Schema.Create("intelligence_hot_poc", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("name", 512).Index().Comment("漏洞名称")
		table.String("cve", 255).Index().Nullable().Comment("CVE编号")
		table.String("cnnvd", 255).Index().Nullable().Comment("CNNVD")
		table.String("impact_range", 1024).Nullable().Comment("影响范围")
		table.String("impact_product", 1024).Nullable().Comment("影响产品")
		table.String("impact_version", 1024).Nullable().Comment("影响版本")
		table.String("solution", 2048).Nullable().Comment("解决方案")
		table.String("introduce", 2048).Nullable().Comment("漏洞介绍")
		table.String("risk_level", 255).Nullable().Comment("风险级别")
		table.String("fofa_query", 3500).Nullable().Comment("FOFA查询语句")
		table.BigInt("fofa_count", 22).Unsigned().Default(0).Nullable().Comment("影响资产数量")
		table.DateTime("found_at").Index().Nullable().Comment("发现时间")
		table.Timestamps()
		table.Comment("情报-热点漏洞")
	})
}

func (t *IntelligenceHotPocTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_hot_poc")
}
