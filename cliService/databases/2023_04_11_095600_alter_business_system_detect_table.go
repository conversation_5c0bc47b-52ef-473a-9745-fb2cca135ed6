package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAlterBusinessSystemResultTable0411())
// }

type AlterBusinessSystemResultTable0411 struct{}

func CreateAlterBusinessSystemResultTable0411() interfaces.Migration {
	return &AlterBusinessSystemResultTable0411{}
}

func (*AlterBusinessSystemResultTable0411) Up() error {
	return mysql.Schema.Table("business_system_result", func(table interfaces.Blueprint) {
		table.String("asset_key", 50).Default("").Comment("资产id, 用户唯一")
		table.String("note", 500).Default("").Nullable().Comment("备注")
	})
}

func (*AlterBusinessSystemResultTable0411) Down() error {
	return mysql.Schema.Table("business_system_result", func(table interfaces.Blueprint) {
		table.DropColumn("asset_key")
		table.DropColumn("note")
		table.Unique("user_id", "address")
	})
}
