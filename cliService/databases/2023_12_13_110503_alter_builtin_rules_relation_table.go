package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	"micro-service/middleware/mysql/engine_rules"
)

type AlterBuiltinRulesRelationTable struct{}

func CreateAlterBuiltinRulesRelationTable() interfaces.Migration {
	return &AlterBuiltinRulesRelationTable{}
}

func (t *AlterBuiltinRulesRelationTable) Up() error {
	return mysql.Schema.Table(engine_rules.RuleRelationTableName, func(table interfaces.Blueprint) {
		table.BigInt("company_id", 20).Nullable().Comment("企业ID").Index()
		table.String("node", 255).Nullable().Comment("执行这个数据更新的job的node节点，方便排查代码")
		table.DateTime("lastest_found_time").Nullable().Comment("最新再次发现时间")
		table.Boolean("status").Default(3).Comment("是否显示 0/默认 1/确认 2/忽略 3/删除").Modify()
		table.Boolean("enable").Nullable().Default(1).Comment("该规则是否开启匹配资产 0/关闭 1/开启")
		table.BigInt("rule_id", 22).Index().Comment("规则ID").Modify()
		table.BigInt("user_id", 22).Index().Comment("用户ID").Modify()
	})
}

func (t *AlterBuiltinRulesRelationTable) Down() error {
	return nil
}
