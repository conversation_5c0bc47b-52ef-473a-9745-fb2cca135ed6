package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/dataleak_github"
)

type githubRelation struct{}

func CreateGitHubRelationTable() interfaces.Migration {
	return &githubRelation{}
}

func (t *githubRelation) Up() error {
	return mysql.Schema.Create(dataleak_github.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.TableComment("github: 任务与结果关系表")
	})
}

func (t *githubRelation) Down() error {
	return mysql.Schema.DropIfExists(dataleak_github.RelationTableName)
}
