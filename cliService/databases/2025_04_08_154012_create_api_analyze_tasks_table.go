package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type ApiAnalyzeTasksTable struct{}

func CreateApiAnalyzeTasksTable() interfaces.Migration {
	return &ApiAnalyzeTasksTable{}
}

func (t *ApiAnalyzeTasksTable) Up() error {
	return mysql.Schema.Create("api_analyze_tasks", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Integer("user_id", 22).Comment("用户ID")
		table.String("target_url", 255).Comment("目标URL")
		table.String("domain", 255).Comment("域名")
		table.String("user_agent", 255).Comment("UserAgent")
		table.Integer("fuzz", 1).Comment("模糊测试")
		table.Decimal("progress", 5, 2).Comment("进度")
		table.Integer("status", 1).Default(1).Comment("状态 0-待扫描，1-扫描中，2-扫描完成，3-扫描失败")
		table.Integer("js_count", 22).Default(0).Comment("JS数量")
		table.Integer("url_count", 22).Default(0).Comment("URL数量")
		table.Integer("api_count", 22).Default(0).Comment("API数量")
		table.Index("user_id").IndexName("idx_user_id")
	})
}

func (t *ApiAnalyzeTasksTable) Down() error {
	return mysql.Schema.DropIfExists("api_analyze_tasks")
}
