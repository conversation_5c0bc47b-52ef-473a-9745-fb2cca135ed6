package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type DiscoverGroupCompanyTable struct{}

func CreateDiscoverGroupCompanyTable() interfaces.Migration {
	return &DiscoverGroupCompanyTable{}
}

func (t *DiscoverGroupCompanyTable) Up() error {
	return mysql.Schema.Create("discover_group_company", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("task_id", 22).Comment("任务ID").Index()
		table.String("company_name", 512).Comment("企业名称").Index()
		table.Decimal("percent", 11, 2).Comment("股权比例")
		table.String("should_capi", 32).Nullable().Comment("投资比例")
		table.Integer("level", 11).Nullable().Comment("层级")
		table.BigInt("parent_id", 22).Nullable().Comment("父级ID")
		table.String("path", 512).Nullable().Comment("层级路径")
		table.Timestamps()
	})
}

func (t *DiscoverGroupCompanyTable) Down() error {
	return mysql.Schema.DropIfExists("discover_group_company")
}
