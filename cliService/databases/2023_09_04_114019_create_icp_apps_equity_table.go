package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/icp_apps"
)

type icpAppsEquity struct{}

func CreateIcpAppsEquityTable() interfaces.Migration {
	return &icpAppsEquity{}
}

func (t *icpAppsEquity) Up() error {
	return mysql.Schema.Create(icp_apps.EquityTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("name", 255).Comment("关键词")
		table.String("app_name", 255).Nullable().Comment("")
		table.BigInt("parent_id", 22).Nullable().Comment("父级id")
		table.String("icp", 255).Nullable().Comment("icp备案号")
		table.Integer("type", 2).Nullable().Comment("1-小程序，2-应用，3-快应用")
		table.Integer("company_type", 1).Nullable().Comment("1-企业 2-个人")
		table.DateTime("record_time").Nullable().Comment("审核日期")
		table.Integer("status", 1).Nullable().Comment("状态 1-在线 2-注销")
		table.String("source", 50).Nullable().Comment("来源")
		table.Index("name").IndexName("idx_name")
		table.Index("icp").IndexName("idx_icp")
		table.Index("parent_id").IndexName("idx_parent_id")
		table.TableComment("ICP(小程序、应用、快应用)：备案记录")
	})
}

func (t *icpAppsEquity) Down() error {
	return mysql.Schema.DropIfExists(icp_apps.EquityTableName)
}
