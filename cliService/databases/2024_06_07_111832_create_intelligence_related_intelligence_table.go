package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceRelatedIntelligenceTable struct{}

func CreateIntelligenceRelatedIntelligenceTable() interfaces.Migration {
	return &IntelligenceRelatedIntelligenceTable{}
}

func (t *IntelligenceRelatedIntelligenceTable) Up() error {
	return mysql.Schema.Create("intelligence_related_intelligence", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Comment("关联情报表")
		table.Integer("intelligence_id", 8).Comment("情报ID")
		table.Integer("enterprise_id", 8).Comment("企业ID")
		table.String("asset_id", 255).Comment("资产ID")
		table.Integer("user_id", 8).Comment("用户ID")
		table.String("asset_ip", 50).Nullable().Comment("ip 地址")
		table.String("asset_port", 20).Nullable().Comment("端口")
		table.String("asset_protocol", 100).Nullable().Comment("协议")
		table.String("risk_type", 10).Comment("风险类型，1: 热点漏洞，2: 风险事件，3: 专项事件 4: 数据泄露")
		table.String("risk_name", 255).Comment("风险名称/URL")
		table.String("asset_url", 255).Nullable().Comment("URL")
		table.String("asset_title", 255).Nullable().Comment("网站标题")
		table.String("asset_domain", 255).Nullable().Comment("domain")
		table.String("asset_status", 10).Nullable().Comment("状态")
		table.String("intelligence_type", 255).Nullable().Comment("情报类型")
		table.String("intelligence_tags", 255).Nullable().Comment("情报标签")
		table.String("intelligence_country", 10).Nullable().Comment("情报国家")
		table.String("service_component", 255).Nullable().Comment("受影响的服务或组件")
		table.String("found_time", 20).Nullable().Comment("第一次检测发现，发现时间")
		table.String("update_time", 20).Nullable().Comment("最近一次检测发现，更新时间")
	})
}

func (t *IntelligenceRelatedIntelligenceTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_related_intelligence")
}
