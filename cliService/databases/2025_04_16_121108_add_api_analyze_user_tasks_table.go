package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AddApiAnalyzeUserTasksTable struct{}

func CreateApiAnalyzeUserTasksTable() interfaces.Migration {
	return &AddApiAnalyzeUserTasksTable{}
}

func (t *AddApiAnalyzeUserTasksTable) Up() error {
	return mysql.Schema.Create("api_analyze_user_tasks", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Integer("user_id", 22).Comment("用户ID")
		table.String("name", 255).Comment("任务名称")
		table.Decimal("progress", 5, 2).Comment("进度")
		table.Integer("status", 1).Default(1).Comment("状态 0-待扫描，1-扫描中，2-扫描完成，3-扫描失败")
		table.Integer("sub_task_num", 22).Default(0).Comment("子任务数量")
		table.Integer("total_risks", 22).Default(0).Comment("总风险数量")
		table.Index("user_id").IndexName("idx_user_id")
	})
}

func (t *AddApiAnalyzeUserTasksTable) Down() error {
	return nil
}
