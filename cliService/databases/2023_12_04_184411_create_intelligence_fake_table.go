package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceFakeTable struct{}

func CreateIntelligenceFakeTable() interfaces.Migration {
	return &IntelligenceFakeTable{}
}

func (t *IntelligenceFakeTable) Up() error {
	return mysql.Schema.Create("intelligence_fake", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("url", 512).Comment("钓鱼URL")
		table.String("ip", 128).Nullable().Comment("IP地址")
		table.String("title", 512).Index().Nullable().Comment("标题")
		table.String("country", 255).Nullable().Comment("IP所在国家")
		table.String("target", 512).Index().Nullable().Comment("仿冒目标")
		table.String("cloud_name", 255).Nullable().Comment("云厂商")
		table.Integer("status", 11).Default(1).Nullable().Comment("是否在线: 1/在线 2/离线")
		table.String("source", 128).Nullable().Comment("数据来源")
		table.DateTime("found_at").Nullable().Comment("发现时间")
		table.Timestamps()
		table.Comment("情报-钓鱼仿冒")
	})
}

func (t *IntelligenceFakeTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_fake")
}
