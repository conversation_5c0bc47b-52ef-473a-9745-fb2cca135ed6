package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateWhoisTable())
// }

type WhoisTable struct{}

func CreateWhoisTable() interfaces.Migration {
	return &WhoisTable{}
}

func (t *WhoisTable) Up() error {
	return mysql.Schema.Create("whois", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("domain", 255).Comment("域名")
		table.String("sponsoring_registrar", 512).Comment("注册商").Nullable()
		table.String("registrant_name", 512).Comment("联系人").Index().Nullable()
		table.String("registrant_mobile", 255).Comment("联系手机号").Index().Nullable()
		table.String("registrant_email", 255).Comment("联系邮箱").Index().Nullable()
		table.String("registrant_org", 255).Comment("联系公司").Index().Nullable()
		table.DateTime("registration_date").Comment("注册时间").Nullable()
		table.DateTime("expiration_date").Comment("过期时间").Nullable()
		table.Text("dns").Comment("dns信息").Nullable()
		table.String("status", 255).Index().Comment("状态").Nullable()
		table.Text("raw").Comment("原始数据").Nullable()
		table.TableComment("Whois信息表")
		table.Timestamps()
	})
}

func (t *WhoisTable) Down() error {
	return mysql.Schema.DropIfExists("whois")
}
