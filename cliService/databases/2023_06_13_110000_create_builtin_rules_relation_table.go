package migrations

import (
	"micro-service/middleware/mysql/engine_rules"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateBuiltinRulesRelationTable())
// }

type BuiltinRulesRelationTable struct{}

func CreateBuiltinRulesRelationTable() interfaces.Migration {
	return &BuiltinRulesRelationTable{}
}

func (t *BuiltinRulesRelationTable) Up() error {
	return mysql.Schema.Create(engine_rules.RuleRelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("rule_id", 22).Comment("规则ID")
		table.BigInt("user_id", 22).Comment("用户ID")
		table.Integer("status", 2).Default(engine_rules.StatusDisable).Comment("启用状态")
		table.TableComment("内置规则与用户关联表")
	})
}

func (t *BuiltinRulesRelationTable) Down() error {
	return mysql.Schema.DropIfExists(engine_rules.RuleRelationTableName)
}
