package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateApplyApiRecords())
// }

type ApplyApiRecords struct{}

func CreateApplyApiRecords() interfaces.Migration {
	return &ApplyApiRecords{}
}

func (t *ApplyApiRecords) Up() error {
	return mysql.Schema.Create("apply_api_records", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("email", 512).Comment("邮箱email").Index()
		table.String("description", 512).Nullable().Default("").Comment("申请说明")
		table.String("reason", 512).Nullable().Default("").Comment("拒绝的原因")
		table.String("email_status", 10).Nullable().Default(0).Comment("邮件发送状态:0-默认状态 1-发送中, 2-完成 3-失败")
		table.String("is_pass", 10).Nullable().Default(0).Comment("0 待审核 1 审核通过 2驳回")
		table.Timestamps()
		table.Comment("根据企业名称获取资产的api申请记录表")
	})
}

func (t *ApplyApiRecords) Down() error {
	return mysql.Schema.DropIfExists("apply_api_records")
}
