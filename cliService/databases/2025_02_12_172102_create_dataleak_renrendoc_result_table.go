package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type DataleakRenrendocResultTable struct{}

func CreateDataleakRenrendocResultTable() interfaces.Migration {
	return &DataleakRenrendocResultTable{}
}

func (t *DataleakRenrendocResultTable) Up() error {
	return mysql.Schema.Create("dataleak_renrendoc_result", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("doc_url", 500).Default("''").Comment("地址")
		table.String("doc_title", 2000).Default("''").Comment("标题")
		table.String("screenshot", 255).Default("截图地址")
		table.Index("task_id").IndexName("idx_task")
	})
}

func (t *DataleakRenrendocResultTable) Down() error {
	return mysql.Schema.DropIfExists("dataleak_renrendoc_result")
}
