package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateGeneralCluesTaskTable())
// }

type GeneralCluesTaskTable struct{}

func CreateGeneralCluesTaskTable() interfaces.Migration {
	return &GeneralCluesTaskTable{}
}

func (t *GeneralCluesTaskTable) Up() error {
	return mysql.Schema.Create("general_clues_task", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("task_id", 22).Unsigned().Comment("任务ID").Index()
		table.BigInt("clue_id", 22).Unsigned().Comment("线索ID").Index()
		table.TableComment("线索任务执行记录")
		table.Timestamps()
	})
}

func (t *GeneralCluesTaskTable) Down() error {
	return mysql.Schema.DropIfExists("general_clues_task")
}
