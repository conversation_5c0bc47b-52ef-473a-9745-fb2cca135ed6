package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type DataleakGitcodeResultTable struct{}

func CreateDataleakGitcodeResultTable() interfaces.Migration {
	return &DataleakGitcodeResultTable{}
}

func (t *DataleakGitcodeResultTable) Up() error {
	return mysql.Schema.Create("dataleak_gitcode_result", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("repo_name", 255).Default("''").Comment("仓库名称")
		table.String("repo_url", 255).Default("''").Comment("仓库地址")
		table.String("repo_desc", 2000).Default("''").Comment("仓库描述")
		table.String("code_url", 255).Default("''")
		table.String("screen_shot", 255).Default("''")
		table.String("sha", 255).Default("''").Comment("sha")
		table.Text("code_snippet").Nullable().Comment("代码片段")
		table.String("language", 50).Default("''")
		table.Index("task_id").IndexName("idx_task")
	})
}

func (t *DataleakGitcodeResultTable) Down() error {
	return mysql.Schema.DropIfExists("dataleak_gitcode_result")
}
