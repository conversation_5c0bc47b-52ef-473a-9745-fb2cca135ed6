package mapping

func init() {
	GetInstance().Index["foradar_general_assets"] = `
  {
    "settings": {
    "index": {
      "mapping": {
        "total_fields": {
          "limit": "1000"
        }
      },
      "number_of_shards": "5",
          "blocks": {
        "read_only_allow_delete": "false"
      },
      "max_result_window": "10000000",
          "number_of_replicas": "1"
    }
  },
    "mappings": {
    "general": {
      "properties": {
        "id": {
          "type": "keyword"
        },
        "ip": {
          "type": "ip",
              "fields": {
            "ip_raw": {
              "type": "keyword",
                  "index": true
            }
          }
        },
        "port": {
          "type": "long"
        },
        "url": {
          "type": "keyword"
        },
        "protocol": {
          "type": "keyword"
        },
        "base_protocol": {
          "type": "keyword"
        },
        "certs_valid": {
          "type": "boolean"
        },
        "is_ipv6": {
          "type": "boolean"
        },
        "is_cdn": {
          "type": "boolean"
        },
        "cloud_name": {
          "type": "keyword"
        },
        "title": {
          "type": "keyword"
        },
        "domain": {
          "type": "keyword"
        },
        "subdomain": {
          "type": "keyword"
        },
        "cert": {
          "type": "keyword"
        },
        "icp": {
          "type": "keyword"
        },
        "server": {
          "type": "keyword"
        },
        "version": {
          "type": "keyword"
        },
        "cname": {
          "type": "keyword"
        },
        "fid": {
          "type": "keyword"
        },
        "source": {
          "type": "keyword"
        },
        "icon_hash": {
          "type": "long"
        },
        "icon": {
          "type": "text"
        },
        "clues_hash": {
          "type": "keyword"
        },
        "clues": {
          "properties": {
            "type": {
              "type": "long"
            },
            "hash": {
              "type": "long"
            },
            "content": {
              "type": "text"
            },
            "company_name": {
              "type": "text"
            },
            "source": {
              "type": "long"
            }
          }
        },
		"geo": {
			"properties": {
				"continent": {
					"type": "keyword"
				},
				"country": {
					"type": "keyword"
				},
				"city": {
					"type": "keyword"
				},
				"province": {
					"type": "keyword"
				},
				"district": {
					"type": "keyword"
				},
				"as": {
					"type": "keyword"
				},
				"asn": {
					"type": "keyword"
				},
				"as_name": {
					"type": "keyword"
				},
				"org": {
					"type": "keyword"
				},
				"isp": {
					"type": "keyword"
				},
				"zip": {
					"type": "keyword"
				},
				"lon": {
					"type": "float"
				},
				"lat": {
					"type": "float"
				}
			}
		},
        "source_updated_at": {
          "type": "date",
              "format": "YYYY-MM-dd HH:mm:ss"
        },
        "created_at": {
          "type": "date",
              "format": "YYYY-MM-dd HH:mm:ss"
        },
        "updated_at": {
          "type": "date",
              "format": "YYYY-MM-dd HH:mm:ss"
        }
      }
    }
  }
  }`
}
