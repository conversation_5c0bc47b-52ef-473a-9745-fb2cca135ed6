package migrations

import (
	ipdomain "micro-service/middleware/mysql/ip_domain"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateInformationDomainTable())
// }

type InformationDomain struct{}

func CreateInformationDomainTable() interfaces.Migration {
	return &InformationDomain{}
}

func (*InformationDomain) Up() error {
	return mysql.Schema.Create(ipdomain.DomainTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("domain", 255).Comment("请求域名")
		table.Index("domain").IndexName("idx_domain")
	})
}

func (*InformationDomain) Down() error {
	return mysql.Schema.DropIfExists(ipdomain.DomainTableName)
}
