package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterIntelligenceHotPocAddTagTable struct{}

func CreateAlterIntelligenceHotPocAddTagTable() interfaces.Migration {
	return &AlterIntelligenceHotPocAddTagTable{}
}

func (t *AlterIntelligenceHotPocAddTagTable) Up() error {
	return mysql.Schema.Table("intelligence_hot_poc", func(table interfaces.Blueprint) {
		table.String("tag", 255).Nullable().Comment("标签")
	})
}

func (t *AlterIntelligenceHotPocAddTagTable) Down() error {
	return nil
}
