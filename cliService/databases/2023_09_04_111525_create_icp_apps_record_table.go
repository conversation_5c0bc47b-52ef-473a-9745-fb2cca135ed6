package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/icp_apps"
)

type icpAppsRecord struct{}

func CreateIcpAppsRecordTable() interfaces.Migration {
	return &icpAppsRecord{}
}

func (t *icpAppsRecord) Up() error {
	return mysql.Schema.Create(icp_apps.RecordTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("name", 255).Comment("关键词")
		table.Integer("type", 1).Nullable().Comment("查询类型 1-小程序 2-应用 3-快应用")
		table.BigInt("equity_id", 22).Nullable().Comment("备案记录ID")
		table.Index("name").IndexName("idx_name")
		table.TableComment("ICP备案: 小程序、应用、快应用")
	})
}

func (t *icpAppsRecord) Down() error {
	return mysql.Schema.DropIfExists(icp_apps.RecordTableName)
}
