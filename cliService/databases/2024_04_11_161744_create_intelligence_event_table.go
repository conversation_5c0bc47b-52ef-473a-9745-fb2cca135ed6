package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceEventTable struct{}

func CreateIntelligenceEventTable() interfaces.Migration {
	return &IntelligenceEventTable{}
}

func (t *IntelligenceEventTable) Up() error {
	return mysql.Schema.Create("intelligence_event", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.DateTime("creation_time").Comment("创建时间")
		table.String("download_link", 255).Comment("下载链接")
		table.String("filename", 255).Comment("文件名")
		table.String("hash", 255).Comment("文件哈希")
		table.String("tags", 255).Comment("事件标签")
		table.Integer("ip_count", 8).Comment("事件关联IP数量")
		table.Comment("情报-事件专项表")
	})
}

func (t *IntelligenceEventTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_event")
}
