package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAliyunAvdTable())
// }

type AliyunAvdTable struct{}

func CreateAliyunAvdTable() interfaces.Migration {
	return &AliyunAvdTable{}
}

func (t *AliyunAvdTable) Up() error {
	return mysql.Schema.Create("aliyun_avd", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("avd", 128).Comment("AVD编号").Index()
		table.String("cve", 128).Comment("CVE编号").Index()
		table.String("name", 512).Comment("漏洞名称").Index()
		table.String("type", 128).Comment("漏洞类型").Index()
		table.String("type_text", 512).Comment("漏洞类型介绍").Index()
		table.String("status", 128).Comment("漏洞状态").Index()
		table.DateTime("disclosure_at").Comment("披露时间").Index()
		table.String("level", 125).Comment("漏洞级别").Index()
		table.String("utilization", 125).Comment("利用情况")
		table.String("patch_status", 125).Comment("补丁情况")
		table.Decimal("cvss", 11, 1).Comment("CVSS评分")
		table.Decimal("aliyun_score", 11, 1).Comment("阿里云评分")
		table.LongText("describe").Comment("描述")
		table.String("tips", 512).Comment("提示")
		table.LongText("suggestion").Comment("解决建议")
		table.LongText("reference").Comment("参考链接")
		table.LongText("affected_software").Comment("受影响软件情况{type:类型,manufacturer:厂商,product:产品,version:版本,version_range:版本范围,impact:影响面}")
		table.String("attack_path", 128).Comment("攻击路径")
		table.String("attack_complexity", 128).Comment("攻击复杂度")
		table.String("permission_requirements", 128).Comment("权限要求")
		table.String("impact", 128).Comment("影响范围")
		table.String("exp_maturity", 128).Comment("EXP成熟度")
		table.String("data_confidentiality", 128).Comment("数据保密性")
		table.String("data_integrity", 128).Comment("数据完整性")
		table.String("category", 128).Comment("类别")
		table.String("server_hazards", 128).Comment("服务器危害")
		table.String("total", 128).Comment("全网数量")
		table.LongText("cwe_infos").Comment("CWE信息{cwe_id:CWE编号,cwe_type:漏洞类型}")
		table.String("nvd_link", 512).Comment("NVD地址")
		table.Timestamps()
	})
}

func (t *AliyunAvdTable) Down() error {
	return mysql.Schema.DropIfExists("aliyun_avd")
}
