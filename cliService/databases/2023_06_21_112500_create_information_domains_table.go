package migrations

import (
	ipdomain "micro-service/middleware/mysql/ip_domain"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateInformationIpTable())
// }

type InformationIp struct{}

func CreateInformationIpTable() interfaces.Migration {
	return &InformationIp{}
}

func (t *InformationIp) Up() error {
	return mysql.Schema.Create(ipdomain.IpTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("ip", 100).Comment("请求IP")
		table.Index("ip").IndexName("idx_ip")
	})
}

func (t *InformationIp) Down() error {
	return mysql.Schema.DropIfExists(ipdomain.IpTableName)
}
