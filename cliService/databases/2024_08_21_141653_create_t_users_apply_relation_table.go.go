package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type TUsersApplyRelationTable struct{}

func CreateTUsersApplyRelationTable() interfaces.Migration {
	return &TUsersApplyRelationTable{}
}

func (t *TUsersApplyRelationTable) Up() error {
	return mysql.Schema.Create("t_users_apply_relation", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Integer("op_user_id", 22).Comment("操作人用户ID,指定账户才有权限为下属员工开通账户")
		table.String("company_name", 255).Comment("企业名称")
		table.String("username", 128).Comment("用户名称").Unique()
		table.String("email", 64).Comment("邮箱").Unique()
		table.String("mobile", 32).Comment("手机号").Unique()
		table.String("initial_password", 255).Comment("初始账户密码")
		table.Integer("status", 8).Comment("状态【0账号生成中、1启用、2禁用】")
		table.Integer("is_formal", 1).Comment("用户属性【0测试用户、1正式用户】")
		table.Comment("FD01 Saas账户授权关联表")
	})
}

func (t *TUsersApplyRelationTable) Down() error {
	return mysql.Schema.DropIfExists("t_users_apply_relation")
}
