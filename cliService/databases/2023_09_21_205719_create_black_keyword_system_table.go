package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	"micro-service/middleware/mysql/black_keyword_system"
)

// 黄赌毒总库 新建表迁移
type blackKeywordSystem struct{}

func CreateBlackKeywordSystemTable() interfaces.Migration {
	return &blackKeywordSystem{}
}

func (t *blackKeywordSystem) Up() error {
	return mysql.Schema.Create(black_keyword_system.TableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("keyword", 500).Comment("关键词")
		table.BigInt("type_id", 1).Nullable().Comment("分类ID")
		table.Integer("status", 1).Nullable().Default(1).Comment("启用状态")
		table.Index("keyword", "type_id").IndexName("idx_keyword_type")
		table.TableComment("黄赌毒关键词总库表")
	})
}

func (t *blackKeywordSystem) Down() error {
	return mysql.Schema.DropIfExists(black_keyword_system.TableName)
}
