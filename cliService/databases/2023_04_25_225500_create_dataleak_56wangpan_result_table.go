package migrations

import (
	wp "micro-service/middleware/mysql/dataleak_56wangpan"
	docin "micro-service/middleware/mysql/dataleak_docin"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDataLeak56WangpanResultTable())
// }

type DataLeak56WangpanResultTable struct{}

func CreateDataLeak56WangpanResultTable() interfaces.Migration {
	return &DataLeak56WangpanResultTable{}
}

func (*DataLeak56WangpanResultTable) Up() error {
	return mysql.Schema.Create(wp.ResultTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("file_url", 500).Default("''").Comment("地址")
		table.String("file_name", 500).Default("''").Comment("文件名")
		table.String("screenshot", 255).Default("截图地址")
		table.Index("task_id").IndexName("idx_task")
	})
}

func (*DataLeak56WangpanResultTable) Down() error {
	return mysql.Schema.DropIfExists(docin.ResultTableName)
}
