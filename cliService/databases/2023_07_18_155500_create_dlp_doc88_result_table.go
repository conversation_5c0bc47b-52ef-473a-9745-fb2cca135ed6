package migrations

import (
	"micro-service/middleware/mysql/dataleak_doc88"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDlpDoc88ResultTable())
// }

type DlpDoc88Result struct{}

func CreateDlpDoc88ResultTable() interfaces.Migration {
	return &DlpDoc88Result{}
}

func (*DlpDoc88Result) Up() error {
	return mysql.Schema.Create(dataleak_doc88.ResultTable, func(table interfaces.Blueprint) {
		// 建表字段
		table.Id("id", 22)
		table.Timestamps()
		table.String("url", 300).Default("").Comment("url")
		table.String("title", 2000).Default("").Comment("title")
		table.String("upload_date", 20).Default("").Comment("上传日期")
		// 表索引
		table.Index("url").IndexName("idx_url")
	})
}

func (*DlpDoc88Result) Down() error {
	return mysql.Schema.DropIfExists(dataleak_doc88.ResultTable)
}
