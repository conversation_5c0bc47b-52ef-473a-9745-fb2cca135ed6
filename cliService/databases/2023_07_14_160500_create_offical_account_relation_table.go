package migrations

import (
	"micro-service/middleware/mysql/official_account"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateOfficialAccountRelationTable())
// }

type OfficialAccountRelationTable struct{}

func CreateOfficialAccountRelationTable() interfaces.Migration {
	return &OfficialAccountRelationTable{}
}

func (t *OfficialAccountRelationTable) Up() error {
	return mysql.Schema.Create(official_account.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.Integer("official_account_history_id", 22).Comment("任务id")
		table.Integer("official_account_id", 22).Comment("公众号记录id")
	})
}

func (t *OfficialAccountRelationTable) Down() error {
	return mysql.Schema.DropIfExists(official_account.RelationTableName)
}
