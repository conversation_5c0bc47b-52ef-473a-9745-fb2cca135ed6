package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	"micro-service/middleware/mysql/icp_apps"
)

type unregisteredIcpApps struct{}

func CreateUnregisteredIcpAppsTable() interfaces.Migration {
	return &unregisteredIcpApps{}
}

func (t *unregisteredIcpApps) Up() error {
	return mysql.Schema.Create(icp_apps.UnregisteredTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("keyword", 255).Comment("搜索关键词")
		table.Integer("type", 1).Nullable().Comment("搜索类型")
		table.Index("keyword", "type").IndexName("idx_keyword_type")
		table.TableComment("无备案结果的搜索内容")
	})
}

func (t *unregisteredIcpApps) Down() error {
	return mysql.Schema.DropIfExists(icp_apps.UnregisteredTableName)
}
