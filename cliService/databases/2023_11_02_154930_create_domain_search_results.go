package migrations

import (
	"micro-service/middleware/mysql/domain_search"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// 域名搜索结果 新建表迁移
type createDomainSearchResult20231102 struct{}

func CreateDomainSearchResultTable() interfaces.Migration {
	return &createDomainSearchResult20231102{}
}

func (t *createDomainSearchResult20231102) Up() error {
	return mysql.Schema.Create(domain_search.ResultTable, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.Text("url").Nullable().Comment("url")
		table.String("domain", 255).Nullable().Default("").Comment("域名")
		table.String("title", 2000).Nullable().Default("").Comment("标题")
		table.String("source", 50).Nullable().Default("").Comment("来源: Google/Bing/Baidu")
		table.String("search_syntax", 1000).Nullable().Default("").Comment("搜索语法")
		table.Index("task_id").IndexName("idx_task_id")
		table.TableComment("域名搜索结果")
	})
}

func (t *createDomainSearchResult20231102) Down() error {
	return mysql.Schema.DropIfExists(domain_search.ResultTable)
}
