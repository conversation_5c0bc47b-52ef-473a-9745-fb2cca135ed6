package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterCompanyEquityTable struct{}

func CreateAlterCompanyEquityTable() interfaces.Migration {
	return &AlterCompanyEquityTable{}
}

func (t *AlterCompanyEquityTable) Up() error {
	return mysql.Schema.Table("company_equity", func(table interfaces.Blueprint) {
		table.String("reg_status", 500).Default("存续").Comment("企业状态 存续/注销/或者其他")
	})
}

func (t *AlterCompanyEquityTable) Down() error {
	return nil
}
