package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceDataSummaryTable struct{}

func CreateIntelligenceDataSummaryTable() interfaces.Migration {
	return &IntelligenceDataSummaryTable{}
}

func (t *IntelligenceDataSummaryTable) Up() error {
	return mysql.Schema.Create("intelligence_data_summary", func(table interfaces.Blueprint) {
		table.Comment("数据泄露信息汇总表")
		table.Id("id", 22)
		table.Timestamps()
		table.String("special_project_name", 255).Comment("专项名称")
		table.Text("entities").Comment("数据所属实体，全部涉及企业")
		table.BigInt("data_volume", 32).Comment("数据量，互联网影响面")
		table.Integer("company_num", 11).Comment("涉及企业数量，关联资产得到")
		table.BigInt("asset_num", 32).Comment("涉及资产数量，关联资产得到")
		table.DateTime("last_update_time").Comment("最新更新时间")
	})
}

func (t *IntelligenceDataSummaryTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_data_summary")
}
