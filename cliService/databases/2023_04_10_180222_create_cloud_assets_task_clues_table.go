package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateCloudAssetsTaskCluesTable())
// }

type CloudAssetsTaskCluesTable struct{}

func CreateCloudAssetsTaskCluesTable() interfaces.Migration {
	return &CloudAssetsTaskCluesTable{}
}

func (t *CloudAssetsTaskCluesTable) Up() error {
	return mysql.Schema.Create("cloud_assets_task_clues", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("clue_id", 22).Unsigned().Comment("线索id")
		table.BigInt("task_id", 22).Unsigned().Comment("任务id")
		table.Timestamps()
	})
}

func (t *CloudAssetsTaskCluesTable) Down() error {
	return mysql.Schema.DropIfExists("cloud_assets_task_clues")
}
