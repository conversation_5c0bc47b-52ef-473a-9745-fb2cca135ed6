package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateUrlStatusDetectResultTable())
// }

type UrlStatusDetectResultTable struct{}

func CreateUrlStatusDetectResultTable() interfaces.Migration {
	return &UrlStatusDetectResultTable{}
}

func (t *UrlStatusDetectResultTable) Up() error {
	return mysql.Schema.Create("url_status_detect_result", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("url", 1000).Comment("URL")
		table.String("ip", 255).Comment("IP").Nullable()
		table.String("port", 255).Comment("PORT").Nullable()
		table.String("protocol", 255).Comment("Protocol").Nullable()
		table.Boolean("online_state").Comment("1:在线，2:离线")
		table.Text("title").Comment("网站的title").Nullable()
		table.String("status_code", 255).Comment("url返回的状态码").Nullable()
		table.BigInt("task_id", 22).Unsigned().Comment("url状态检测任务表id").Index()
		table.TableComment("url状态检测结果表")
		table.Timestamps()
		table.DeletedAt(true)
	})
}

func (t *UrlStatusDetectResultTable) Down() error {
	return mysql.Schema.DropIfExists("url_status_detect_result")
}
