package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	dashengpan "micro-service/middleware/mysql/dataleak_dashengpan"
)

type DataleakDashengpanResultTable struct{}

func CreateDataleakDashengpanResultTable() interfaces.Migration {
	return &DataleakDashengpanResultTable{}
}

func (t *DataleakDashengpanResultTable) Up() error {
	return mysql.Schema.Create(dashengpan.ResultTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务ID")
		table.String("file_url", 1000).Default("''").Comment("地址")
		table.String("file_name", 1000).Default("''").Comment("文件名")
		table.String("screenshot", 500).Default("截图地址")
		table.Index("task_id").IndexName("idx_task")
	})
}

func (t *DataleakDashengpanResultTable) Down() error {
	return mysql.Schema.DropIfExists(dashengpan.ResultTableName)
}
