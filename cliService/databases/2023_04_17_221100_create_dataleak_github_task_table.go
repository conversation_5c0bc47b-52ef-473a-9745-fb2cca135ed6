package migrations

import (
	"micro-service/middleware/mysql/dataleak_github"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateDataLeakGithubTaskTable())
// }

type DataLeakGithubTaskTable struct{}

func CreateDataLeakGithubTaskTable() interfaces.Migration {
	return &DataLeakGithubTaskTable{}
}

func (*DataLeakGithubTaskTable) Up() error {
	return mysql.Schema.Create(dataleak_github.TaskTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("keyword", 255).Comment("关键词")
		table.String("keyword_hash", 255).Comment("关键词hash值")
		table.Integer("status", 1).Default(1).Comment("任务状态: 1-进行中, 2-完成")
		table.Decimal("progress", 10, 2).Default(0).Comment("任务进度")
		table.Timestamps()
	})
}

func (*DataLeakGithubTaskTable) Down() error {
	return mysql.Schema.DropIfExists(dataleak_github.TaskTableName)
}
