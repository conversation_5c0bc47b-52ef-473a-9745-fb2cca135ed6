package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	miaosou "micro-service/middleware/mysql/dataleak_miaosou"
)

type DataLeakMiaosouTasksTable struct{}

func CreateDataLeakMiaosouTasksTable() interfaces.Migration {
	return &DataLeakMiaosouTasksTable{}
}

func (t *DataLeakMiaosouTasksTable) Up() error {
	return mysql.Schema.Create(miaosou.TaskTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.String("keyword", 255).Comment("关键词")
		table.String("keyword_hash", 255).Comment("关键词hash值")
		table.Integer("status", 1).Default(1).Comment("任务状态: 1-进行中, 2-完成")
		table.Decimal("progress", 10, 2).Default(0).Comment("任务进度")
		table.Index("keyword_hash").IndexName("idx_keyword_hash")
	})
}

func (t *DataLeakMiaosouTasksTable) Down() error {
	return mysql.Schema.DropIfExists(miaosou.TaskTableName)
}
