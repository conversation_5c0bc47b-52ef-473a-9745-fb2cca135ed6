package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type AlterDiscoverCluesTable struct{}

func CreateAlterDiscoverCluesTable() interfaces.Migration {
	return &AlterDiscoverCluesTable{}
}

func (t *AlterDiscoverCluesTable) Up() error {
	return mysql.Schema.Table("discover_clues", func(table interfaces.Blueprint) {
		table.Integer("status", 11).Default(1).Comment("线索状态,1:正常,2:未启用")
	})
}

func (t *AlterDiscoverCluesTable) Down() error {
	return mysql.Schema.Table("discover_clues", func(table interfaces.Blueprint) {
		table.DropColumn("status")
	})
}
