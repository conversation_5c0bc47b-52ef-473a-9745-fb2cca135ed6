package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"

	docin "micro-service/middleware/mysql/dataleak_docin"
)

type docinRelation struct{}

func CreateDocinRelationTable() interfaces.Migration {
	return &docinRelation{}
}

func (t *docinRelation) Up() error {
	return mysql.Schema.Create(docin.RelationTableName, func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.TableComment("豆丁：任务与结果关系表")
	})
}

func (t *docinRelation) Down() error {
	return mysql.Schema.DropIfExists(docin.RelationTableName)
}
