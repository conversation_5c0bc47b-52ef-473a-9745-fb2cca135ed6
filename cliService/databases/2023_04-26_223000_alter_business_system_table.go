package migrations

import (
	"micro-service/middleware/mysql/business_system"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAlterBusinessSystem20230426())
// }

type AlterBusinessSystem20230426 struct{}

func CreateAlterBusinessSystem20230426() interfaces.Migration {
	return &AlterBusinessSystem20230426{}
}

func (*AlterBusinessSystem20230426) Up() error {
	return mysql.Schema.Table(business_system.TableName, func(table interfaces.Blueprint) {
		table.Index("user_id", "asset_key").IndexName("idx_user_asset_key")
	})
}

func (*AlterBusinessSystem20230426) Down() error {
	return mysql.Schema.Table(business_system.TableName, func(table interfaces.Blueprint) {
		table.DropIndex("idx_user_asset_key")
	})
}
