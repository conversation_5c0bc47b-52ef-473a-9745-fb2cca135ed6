package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateOfficialAccountsTable())
// }

type OfficialAccountsTable struct{}

func CreateOfficialAccountsTable() interfaces.Migration {
	return &OfficialAccountsTable{}
}

func (t *OfficialAccountsTable) Up() error {
	return mysql.Schema.Create("official_accounts", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.String("name", 512).Comment("公众号名称").Index()
		table.String("account", 125).Comment("账号").Index()
		table.String("qrcode", 1024).Comment("二维码地址").Nullable()
		table.String("company_name", 512).Comment("账号主体").Index().Nullable()
		table.String("platform", 512).Comment("平台").Index().Nullable()
		table.Boolean("is_online").Comment("在线状态 1/2 在线/离线").Default(1)
		table.TableComment("公众号总库")
		table.Timestamps()
	})
}

func (t *OfficialAccountsTable) Down() error {
	return mysql.Schema.DropIfExists("official_accounts")
}
