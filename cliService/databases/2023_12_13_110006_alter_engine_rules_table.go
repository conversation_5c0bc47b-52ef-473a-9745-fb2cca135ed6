package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	"micro-service/middleware/mysql/engine_rules"
)

type AlterEngineRulesTable struct{}

func CreateAlterEngineRulesTable() interfaces.Migration {
	return &AlterEngineRulesTable{}
}

func (t *AlterEngineRulesTable) Up() error {
	return mysql.Schema.Table(engine_rules.RuleTableName, func(table interfaces.Blueprint) {
		table.String("event_type", 30).Comment("事件规则类型")
		table.MediumText("content").Comment("用户输入的规则内容").Modify()
		table.MediumText("rule_content").Comment("实际执行的规则内容").Modify()
	})
}

func (t *AlterEngineRulesTable) Down() error {
	return nil
}
