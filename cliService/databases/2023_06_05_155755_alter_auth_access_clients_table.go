package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAlterAuthAccessClientsTable0605())
// }

type AlterAuthAccessClientsTable0411 struct{}

func CreateAlterAuthAccessClientsTable0605() interfaces.Migration {
	return &AlterAuthAccessClientsTable0411{}
}

func (*AlterAuthAccessClientsTable0411) Up() error {
	return mysql.Schema.Table("auth_access_clients", func(table interfaces.Blueprint) {
		table.Integer("account_type", 2).Default(0).Comment("账户类型 0/1 默认/中航金网账户")
		table.Integer("date_key", 11).Unsigned().Default(0).Comment("单日日期key")
		table.Integer("per_cname_limit", 11).Unsigned().Default(0).Comment("单次企业查询次数限制")
		table.Integer("day_assets_limit", 11).Unsigned().Default(0).Comment("单日企业资产数限制")
		table.Integer("mon_detect_limit", 11).Unsigned().Default(0).Comment("月企业查询次数限制")
		table.Integer("day_assets_count", 11).Unsigned().Default(0).Comment("单日资产数量")
		table.Integer("used_detect_count", 11).Unsigned().Default(0).Comment("单月查询次数统计")
	})
}

func (*AlterAuthAccessClientsTable0411) Down() error {
	return mysql.Schema.Table("auth_access_clients", func(table interfaces.Blueprint) {
		table.DropColumn("account_type")
		table.DropColumn("date_key")
		table.DropColumn("per_cname_limit")
		table.DropColumn("day_assets_limit")
		table.DropColumn("mon_detect_limit")
		table.DropColumn("day_assets_count")
		table.DropColumn("used_detect_count")
	})
}
