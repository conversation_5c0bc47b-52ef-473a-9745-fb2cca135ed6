package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateAlterAssetAuditTask20230406())
// }

type AlterAssetAuditTask20230406 struct{}

func CreateAlterAssetAuditTask20230406() interfaces.Migration {
	return &AlterAssetAuditTask20230406{}
}

func (*AlterAssetAuditTask20230406) Up() error {
	return mysql.Schema.Table("asset_audit_tasks", func(table interfaces.Blueprint) {
		table.CustomSql("ADD `assets_port` BIGINT DEFAULT 0 COMMENT \"新增端口资产\"")
		table.CustomSql("ADD `asset_system_find` BIGINT DEFAULT 0 COMMENT \"系统发现\"")
		table.CustomSql("ADD `asset_total` BIGINT DEFAULT 0 COMMENT \"数据总表\"")
		table.CustomSql("ADD `sync_asset` TINYINT DEFAULT 1 COMMENT \"是否同步资产到台账(1否, 2是)\"")
	})
}

func (*AlterAssetAuditTask20230406) Down() error {
	return nil
}
