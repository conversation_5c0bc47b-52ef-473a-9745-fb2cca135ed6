package migrations

import "github.com/panda843/go-migrate/config"

// 注册的migration函数应严格按照实际顺序进行注册
// 非必要，已注册的migration函数不要随意变动顺序
// 新的migration函数应该放在最后追加
// 非常重要！！！

func init() {
	config.Migrations = append(config.Migrations,
		CreateGeneralCluesTable(),
		CreateGeneralHistoryTable(),
		CreateGeneralCluesTaskTable(),
		CreateWhoisTable(),
		CreateAuthAccessClientsTable(),
		CreateAuthAccessScopeTable(),
		CreateAuthAccessTokensTable(),
		CreateCronTable(),
		CreateCronHistoryTable(),
		CreateOfficialAccountsTable(),
		CreateDomainBurstsTable(),
		CreateFofaCountTable(),
		CreatePublicClueRecordTable(),
		CreatePublicClueResultTable(),
		CreateGolangDomainTasksTable(),
		CreateAssetsStatusDetectResultTable(),
		CreateUrlStatusDetectResultTable(),
		CreateScanCertTable(),
		CreatePublicClueImgTable(),
		CreatePublicClueRelationTable(),
		CreateOfficialAccountsHistoryTable(),
		CreateBusinessSystemResultTable(),
		CreatePublicNoticeTable(),
		CreateAssetsStatusDetectTaskTable(),
		CreateUrlStatusDetectTaskTable(),
		CreateAliyunAvdTable(),
		CreateAssetAuditTaskTable(),
		CreateAlterCompanyIcpTable(),
		CreateAlterAssetAuditTask20230406(),
		CreateCloudAssetsTaskTable(),
		CreateCloudAssetsTaskCluesTable(),
		CreateAlterBusinessSystemResultTable0411(),
		CreateCloudAssetsCluesTable(),
		CreateDataLeakGithubResultTable(),
		CreateDataLeakGithubTaskTable(),
		CreateDataLeakGiteeResultTable(),
		CreateDataLeakGiteeTaskTable(),
		CreateDataLeakBaiduLibraryResultTable(),
		CreateDataLeakBaiduLibraryTaskTable(),
		CreateDataLeakDocinResultTable(),
		CreateDataDocinTaskTable(),
		CreateDataLeak56WangpanResultTable(),
		CreateData56wangpanTaskTable(),
		CreatePansosoTaskTable(),
		CreateDataLeakPansosoResultTable(),
		CreateAlterBusinessSystem20230426(),
		CreateQccBasicDetailsTable(),
		CreateQccBasicDetailsHistoryTable(),
		CreateApiCounterTable(),
		CreateAlterAuthAccessClientsTable0605(),
		CreateEngineRulesTable(),
		CreateBuiltinRulesRelationTable(),
		CreateInformationIpTable(),
		CreateInformationDomainTable(),
		CreateDomainAssetsCronTable(),
		CreateUnregisteredIcpTable(),
		CreateOfficialAccountRelationTable(),
		AlterOfficialAccountHistoryTable20230714(),
		CreateDlpDoc88RelationTable(),
		CreateDlpDoc88ResultTable(),
		CreateDlpDoc88TaskTable(),
		CreateAlterTaskTypeToAssetAuditTasks(),
		CreateAlterAppsHistory20230726(),
		CreateAppHistoryRelationTable(),
		CreateAlterAppsTable20230726(),
		CreateDiscoverCluesTable(),
		CreateDiscoverTaskTable(),
		CreateApplyApiRecords(),
		AlterAuthAccessClientTable20230805(),
		CreateDiscoverGroupTaskTable(),
		CreateDiscoverGroupCompanyTable(),
		CreateUserScopeTable(),
		CreateWeiboHistoryTable(),         // 天眼查-微博-搜索历史
		CreateWeiboAccountTable(),         // 天眼查-微博
		CreatePansosoRelationTable(),      // 创建 总库-盘搜搜：任务-结果关系表
		CreateBaiduLibraryRelationTable(), // 创建 总库-百度文库：任务-结果关系表
		CreateDocinRelationTable(),        // 创建 总库-豆丁：任务-结果关系表
		CreateGitHubRelationTable(),       // 创建 总库-github：任务-结果关系表
		Create56WangPanRelationTable(),    // 创建 总库-56网盘：任务-结果关系表
		CreateGiteeRelationTable(),        // 创建 总库-gitee：任务-结果关系表
		CreateAlterDiscoverCluesTable(),
		CreateIcpAppsRecordTable(),
		CreateIcpAppsEquityTable(),
		CreateUnregisteredIcpAppsTable(),
		CreateAlterAccessClient230919Table(),
		CreateBlackKeywordSystemTable(),            // 黄赌毒总库表-新建
		CreateAlterOfficalAccountHistory20231030(), // 添加keyword索引
		CreateDomainSearchTaskTable(),              // 新建域名搜索任务表
		CreateDomainSearchResultTable(),            // 新建域名搜索结果表
		AlterDomainSearchTaskTable20231110(),
		CreateIntelligenceHotPocTable(),     // 情报-热点漏洞
		CreateIntelligenceFakeTable(),       // 情报-钓鱼仿冒
		CreateIntelligenceThreatTable(),     // 情报-威胁
		CreateIntelligenceOtherTable(),      // 情报-其他
		CreateIntelligenceUserThreatTable(), // 情报-威胁-用户关联表
		CreateIntelligenceUserHotPocTable(),
		CreateAlterEngineRulesTable(),
		CreateAlterBuiltinRulesRelationTable(),
		CreateDataleakPostmanRelationTable(), // 创建 总库-postman：任务-结果关系表
		CreateDataLeakPostmanResultTable(),
		CreateDataLeakPostmanTaskTable(),
		CreateDataLeakMiaosouRelationTable(), // 创建 总库-秒搜：任务-结果关系表
		CreateDataLeakMiaosouResultTable(),
		CreateDataLeakMiaosouTasksTable(),

		CreateDataLeakMagicalsearchTasksTable(), //数据泄露-奇妙搜索
		CreateDataLeakMagicalsearchResultTable(),
		CreateDataLeakMagicalsearchRelationTable(),

		CreateDataleakDashengpanTasksTable(), //数据泄露-大圣盘
		CreateDataleakDashengpanResultTable(),
		CreateDataleakDashengpanRelationTable(),

		// API请求记录表
		CreateRquestApiRecordTable(),

		CreateIntelligenceEventTable(),
		CreateIntelligenceEventDetailTable(),
		CreateIntelligenceDataTable(),
		CreateAlterIntelligenceDataTable(),
		CreateAlterIntelligenceDataTableAddLocallink(),
		CreateAlterIntelligenceEventTable(),
		CreateAlterIntelligenceHotPocTable(),
		CreateAlterIntelligenceHotPocAddReportTable(),
		CreateAlterIntelligenceEventAddSummaryTable(),
		CreateIntelligenceDataSummaryTable(),
		CreateIntelligenceRelatedIntelligenceTable(),
		CreateAlterIntelligenceEventAddStatisticalInfoTable(),
		CreateAlterIntelligenceRelatedIntelligenceAddProjectNameTable(),
		CreateAlterIntelligenceHotPocAddTagTable(),
		CreateTUsersApplyRelationTable(),
		CreateAlterFofaCountTable(),
		CreateAlterCompanyEquityTable(),
		CreateAlterIsAutoBusinessApiToDetectAssetsTasksTable(),
		CreateAlterStatusCodeToBusinessSystemResultTable(),

		CreateDataleakGitcodeRelationTable(),
		CreateDataleakGitcodeResultTable(),
		CreateDataleakGitcodeTaskTable(),

		CreateDataleakRenrendocRelationTable(),
		CreateDataleakRenrendocResultTable(),
		CreateDataleakRenrendocTasksTable(),

		CreateZeroZoneMiniAppHistoryTable(),
		CreateZeroZoneMiniAppTable(),
		CreateZeroZoneMiniAppRelationTable(),

		CreateAddTopDomainToBusinessSystemResultTable(),
		CreateAddPlatformTypeToDataleakDashengpanResultTable(),
		CreateAddIsNeedDnscheckerToDetectAssetsTasksTable(),

		CreateAddUpdateTimeToAppsTable(),
		CreateAddUpdateTimeToZeroZoneMiniAppTable(),
		CreateAddUpdateTimeToOfficialAccountsTable(),
		CreateApiAnalyzeTasksTable(),
		CreateApiAnalyzeResultTable(),
		CreateApiAnalyzeDetailTable(),
		CreateApiAnalyzeUserTasksTable(),
		CreateApiAnalyzeRelationTable(),
		CreateAddStartAndEndTimeToApiAnalyzeUserTasksTable(),
		CreateAddOperatorNameToApiAnalyzeUserTasksTable(),
		CreateAddDeletedAtToApiAnalyzeUserTasksTable(),
		CreateAddDeletedAtToApiAnalyzeTasksTable(),
		CreateAddIsAutoExpendIpToDetectAssetsTasksTable(),
		AlterAuthAccessClientsAddQuotaMigration(),
		CreateApiQuotaConfigTableMigration(),
		CreateCustomPocTable(),
		CreateAlterScanPocsTable(),
	)
}
