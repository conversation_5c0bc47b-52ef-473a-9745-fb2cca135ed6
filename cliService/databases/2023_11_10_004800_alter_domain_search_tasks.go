package migrations

import (
	ds "micro-service/middleware/mysql/domain_search"

	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// 域名搜索任务 新建表迁移
type alterDomainSearchTask20231110 struct{}

func AlterDomainSearchTaskTable20231110() interfaces.Migration {
	return &alterDomainSearchTask20231110{}
}

func (t *alterDomainSearchTask20231110) Up() error {
	return mysql.Schema.Table(ds.TaskTable, func(table interfaces.Blueprint) {
		table.BigInt("user_id", 22).Modify().Nullable().Comment("用户ID")
	})
}

func (t *alterDomainSearchTask20231110) Down() error {
	return nil
}
