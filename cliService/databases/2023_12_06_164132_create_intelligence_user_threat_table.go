package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type IntelligenceUserThreatTable struct{}

func CreateIntelligenceUserThreatTable() interfaces.Migration {
	return &IntelligenceUserThreatTable{}
}

func (t *IntelligenceUserThreatTable) Up() error {
	return mysql.Schema.Create("intelligence_user_threat", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.BigInt("user_id", 22).Index().Unsigned().Comment("用户ID")
		table.BigInt("threat_id", 22).Index().Unsigned().Comment("情报ID")
		table.Integer("hit", 11).Comment("是否命中 1/命中 2/未命中")
		table.Timestamps()
	})
}

func (t *IntelligenceUserThreatTable) Down() error {
	return mysql.Schema.DropIfExists("intelligence_user_threat")
}
