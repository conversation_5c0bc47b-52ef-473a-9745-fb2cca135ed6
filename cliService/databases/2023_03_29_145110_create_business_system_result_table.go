package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

// func init() {
// 	config.Migrations = append(config.Migrations, CreateBusinessSystemResultTable())
// }

type BusinessSystemResultTable struct{}

func CreateBusinessSystemResultTable() interfaces.Migration {
	return &BusinessSystemResultTable{}
}

func (t *BusinessSystemResultTable) Up() error {
	return mysql.Schema.Create("business_system_result", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.DeletedAt(true)
		table.BigInt("user_id", 22).Unsigned().Index().Comment("用户id")
		table.BigInt("operator_id", 22).Unsigned().Nullable().Comment("操作人id")
		table.String("system_name", 1000).Nullable().Comment("系统名称")
		table.String("address", 255).Unique().Comment("访问地址")
		table.String("ip", 100).Comment("ip").Nullable().Default("")
		table.String("port", 10).Comment("端口").Nullable().Default("")
		table.String("domain", 255).Comment("域名").Nullable().Default("")
		table.String("protocol", 50).Comment("协议").Nullable().Default("")
		table.String("belongs", 255).Comment("归属").Nullable().Default("")
		table.String("source", 255).Comment("来源").Nullable().Default("1")
		table.Boolean("system_status").Comment("系统状态(1在线, 2离线)").Default(0)
		table.TableComment("业务系统探测表")
	})
}

func (t *BusinessSystemResultTable) Down() error {
	return mysql.Schema.DropIfExists("business_system_result")
}
