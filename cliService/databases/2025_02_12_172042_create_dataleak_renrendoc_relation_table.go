package migrations

import (
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
)

type DataleakRenrendocRelationTable struct{}

func CreateDataleakRenrendocRelationTable() interfaces.Migration {
	return &DataleakRenrendocRelationTable{}
}

func (t *DataleakRenrendocRelationTable) Up() error {
	return mysql.Schema.Create("dataleak_renrendoc_relation", func(table interfaces.Blueprint) {
		table.Id("id", 22)
		table.Timestamps()
		table.BigInt("task_id", 22).Comment("任务id")
		table.BigInt("result_id", 22).Comment("结果id")
		table.Index("task_id", "result_id").IndexName("idx_task_result")
		table.TableComment("人人文档：任务与结果关系表")
	})
}

func (t *DataleakRenrendocRelationTable) Down() error {
	return mysql.Schema.DropIfExists("dataleak_renrendoc_relation")
}
