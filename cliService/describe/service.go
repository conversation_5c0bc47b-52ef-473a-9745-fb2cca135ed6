package describe

import (
	"encoding/json"
	"fmt"
	"github.com/jedib0t/go-pretty/v6/table"
	"github.com/spf13/cast"
	"os"
	"strings"

	"micro-service/cliService/command"

	"github.com/urfave/cli/v2"
	"gopkg.in/yaml.v2"
)

const FormatJson = "json"
const FormatYaml = "yaml"
const FormatTable = "table"

// Service fetches information for a service from the registry and prints it in
// either JSON or YAML, depending on the format flag passed. Exits on error.
func Service(ctx *cli.Context) error {
	args := ctx.Args().Slice()
	if len(args) < 1 {
		return cli.ShowSubcommandHelp(ctx)
	}
	if ctx.String("format") != FormatJson && ctx.String("format") != FormatYaml && ctx.String("format") != FormatTable {
		return cli.ShowSubcommandHelp(ctx)
	}

	r := *command.GetFCLI().Fcmd.Options().Registry
	srvs, err := r.GetService(args[0])
	if err != nil {
		return err
	}
	if len(srvs) == 0 {
		return fmt.Errorf("service %s not found", args[0])
	}
	t := table.NewWriter()
	t.SetOutputMirror(os.Stdout)
	t.AppendHeader(table.Row{"索引", "服务信息", "方法名称", "请求参数", "响应参数", "节点信息"})
	index := 1
	for _, srv := range srvs {
		if ctx.String("format") == FormatJson {
			if b, formatErr := json.MarshalIndent(srv, "", "  "); formatErr != nil {
				return formatErr
			} else {
				_, _ = os.Stdout.Write(b)
			}
		} else if ctx.String("format") == FormatYaml {
			if b, formatErr := yaml.Marshal(srv); formatErr != nil {
				return formatErr
			} else {
				_, _ = os.Stdout.Write(b)
			}
		} else if ctx.String("format") == FormatTable {
			var nodeStr []string
			for _, node := range srv.Nodes {
				nodeStr = append(nodeStr, node.Address)
			}
			for _, endpoint := range srv.Endpoints {
				var rspStr, reqStr []byte
				if endpoint.Request != nil {
					reqStr, _ = json.MarshalIndent(endpoint.Request.Values, "", "  ")
				}
				if endpoint.Response != nil {
					rspStr, _ = json.MarshalIndent(endpoint.Response.Values, "", "  ")
				}
				if cast.ToBool(endpoint.Metadata["subscriber"]) {
					t.AppendRow([]interface{}{index, "subscriber", endpoint.Metadata["topic"], "-", "-", strings.Join(nodeStr, "\n")})
				} else {
					t.AppendRow([]interface{}{index, srv.Name + ":" + srv.Version, endpoint.Name, string(reqStr), string(rspStr), strings.Join(nodeStr, "\n")})
				}
				t.AppendSeparator()
				index += 1
			}
		}
	}
	if ctx.String("format") == FormatTable {
		t.Render()
	}

	return nil
}
