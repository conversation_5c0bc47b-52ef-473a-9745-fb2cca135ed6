package describe

import (
	"micro-service/cliService/command"

	"github.com/urfave/cli/v2"
)

var flags = []cli.Flag{
	&cli.StringFlag{
		Name:  "format",
		Value: "table",
		Usage: "output a formatted description, e.g. json or yaml or table",
	},
}

func init() {
	command.GetFCLI().Register(&cli.Command{
		Name:  "describe",
		Usage: "查看指定服务信息,例: foradar_cli describe service foradar.crawler",
		Subcommands: []*cli.Command{
			{
				Name:    "service",
				Aliases: []string{"s"},
				Usage:   "查看指定服务信息,例: foradar_cli describe service foradar.crawler",
				Action:  Service,
				Flags:   flags,
			},
		},
	})
}
