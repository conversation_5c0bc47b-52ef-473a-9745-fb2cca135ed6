package config

import (
	"bytes"
	"encoding/json"
	"micro-service/cliService/command"
	"micro-service/pkg/cfg"
	"os"

	"github.com/urfave/cli/v2"
)

func init() {
	command.GetFCLI().Register(&cli.Command{
		Name:   "config",
		Usage:  "获取系统当前配置",
		Action: GetConfig,
	})
}

// GetConfig calls a service endpoint and prints its response. Exits on error.
func GetConfig(ctx *cli.Context) error {
	jsonStr, err := json.Marshal(cfg.GetInstance())
	if err != nil {
		panic(err)
	}
	var prettyJSON bytes.Buffer
	indentErr := json.Indent(&prettyJSON, jsonStr, "", "\t")
	if indentErr != nil {
		panic(err)
	}
	_, _ = os.Stdout.WriteString(prettyJSON.String())
	return nil
}
