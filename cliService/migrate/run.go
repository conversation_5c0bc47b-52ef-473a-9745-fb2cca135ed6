package migrate

import (
	"context"
	"fmt"
	"os"
	"reflect"

	_ "micro-service/cliService/databases"
	"micro-service/cliService/databases/mapping"
	esconn "micro-service/initialize/es"
	"micro-service/pkg/cfg"
	"micro-service/pkg/utils"

	"github.com/panda843/go-migrate/config"
	"github.com/panda843/go-migrate/pkg/interfaces"
	"github.com/urfave/cli/v2"
)

func Run(_ *cli.Context) error {
	// 迁移ES索引
	InitElasticIndex()
	// 迁移Mysql
	migrator := config.Migrator
	exists, err := migrator.CheckTable()
	if err != nil {
		return err
	}

	if !exists {
		if cErr := migrator.CreateTable(); cErr != nil {
			return cErr
		}
	}

	migrations, err := migrator.GetMigrations()
	if err != nil {
		return err
	}
	batch := 0

nextMigrate:
	for _, v := range config.Migrations {
		migration := reflect.TypeOf(v).String()
		for _, m := range migrations {
			batch = m.Batch
			if migration == m.Migration {
				continue nextMigrate
			}
		}
		if err = v.Up(); err != nil {
			e, ok := err.(interfaces.Seeder)
			if ok {
				if e.Error() != "" {
					return e.(error)
				}
			} else {
				return err
			}
		}
		if err := migrator.WriteRecord(migration, batch+1); err != nil {
			return err
		}
		_, _ = fmt.Fprintf(os.Stdout, "migrate %s success.\n", migration)
	}
	return nil
}

func InitElasticIndex() {
	cfg.InitLoadCfg()
	es := esconn.GetInstance(cfg.LoadElastic())
	if list, err := es.IndexNames(); err != nil {
		_, _ = fmt.Fprintf(os.Stdout, "创建索引失败,获取索引信息失败:%s", err.Error())
	} else {
		for k, v := range mapping.GetInstance().Index {
			if !utils.ListContains(list, k) {
				createIndex, cErr := es.CreateIndex(k).BodyString(v).Do(context.Background())
				if cErr != nil {
					_, _ = fmt.Fprintf(os.Stdout, "创建索引 %s 失败:%s\n", k, cErr.Error())
				} else {
					if !createIndex.Acknowledged {
						_, _ = fmt.Fprintf(os.Stdout, "创建索引 %s 失败\n", k)
					} else {
						_, _ = fmt.Fprintf(os.Stdout, "创建索引 %s 成功\n", k)
					}
				}
			}
		}
	}
}
