package migrate

import (
	"github.com/panda843/go-migrate/config"
	"github.com/panda843/go-migrate/pkg/lib/mysql"
	"github.com/spf13/cast"
	"github.com/urfave/cli/v2"
	"micro-service/cliService/command"
	"micro-service/pkg/cfg"
)

func init() {
	// 命令定义
	command.GetFCLI().Register(&cli.Command{
		Name:  "migrate",
		Usage: "数据迁移",
		Subcommands: []*cli.Command{
			{Name: "run", Usage: "该migrate命令将执行所有迁移操作，除了已执行的迁移并将状态记录到数据库中。", Action: Run},
			{Name: "new", Usage: "该new命令将创建一个迁移文件", Action: New},
			{Name: "rollback", Usage: "该rollback命令将回滚一批的迁移。", Action: Rollback},
		},
	})
	// migrate 数据库定义
	config.Config = config.DatabaseConfig{
		Host:     cfg.LoadMysql().Address,
		Dbname:   cfg.LoadMysql().Database,
		Port:     cast.ToInt(cfg.LoadMysql().Port),
		Username: cfg.LoadMysql().UserName,
		Password: cfg.LoadMysql().Password,
	}
	config.Driver = "mysql"
	if config.Migrator == nil {
		config.Migrator = mysql.InitMigrator()
	}
}
